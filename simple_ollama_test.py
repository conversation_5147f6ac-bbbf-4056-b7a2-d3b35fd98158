#!/usr/bin/env python3
"""
简单测试Ollama服务器连接状态 - 不导入app模块
"""

import requests
import json
import time
import sys

def test_server_connection(port):
    """测试服务器基本连接"""
    try:
        response = requests.get(f"http://localhost:{port}/api/tags", timeout=5)
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except:
        return False, None

def main():
    print("🚀 Ollama服务器连接测试")
    print("=" * 40)
    
    servers = [
        (11434, "server0 (GPU 0)"),
        (11433, "server1 (GPU 1)")
    ]
    
    for port, name in servers:
        print(f"\n🔍 测试 {name}")
        is_online, data = test_server_connection(port)
        
        if is_online:
            models = data.get('models', []) if data else []
            print(f"  ✅ 在线，模型数量: {len(models)}")
        else:
            print(f"  ❌ 离线或连接失败")
    
    print("\n" + "=" * 40)

if __name__ == "__main__":
    main()
