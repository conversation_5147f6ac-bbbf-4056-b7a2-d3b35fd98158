from fastapi import FastAPI, Request, HTTPException, Depends, BackgroundTasks, Query, UploadFile, Body, Form, WebSocket, WebSocketDisconnect  
from fastapi.responses import JSONResponse, StreamingResponse, HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from typing import Optional, Union, Dict, List, Tuple
from urllib.parse import urlparse, parse_qs
import httpx
from queue import Queue
import configparser
import logging
from logging.handlers import RotatingFileHandler
from starlette.middleware.base import BaseHTTPMiddleware
import time
import asyncio
import sys
from itertools import cycle
from threading import Lock
import json
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import Response
import numpy as np
import base64
import cv2
import uuid
from fastapi import UploadFile, Body
from fastapi.background import BackgroundTasks
from collections import defaultdict
import re
import rec
from asyncio import Event, create_task, wait_for, TimeoutError
from fastapi.openapi.docs import get_swagger_ui_html
from dataclasses import dataclass, field, asdict, field
import os
from pydantic import validator
from fastapi.openapi.docs import get_swagger_ui_html, get_swagger_ui_oauth2_redirect_html  
from fastapi.responses import StreamingResponse
from fastapi.responses import RedirectResponse
import signal
from urllib.parse import quote
#from seal import HSRecognizer
from timefinder import TimeFinder
from multiprocessing import Queue, Process, Manager
import torch
import warnings
import onnxruntime
from layout_content_extractor import extract_page_content_by_layout

# 添加更详细的警告过滤
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=UserWarning, module="numpy.core.getlimits")
warnings.filterwarnings("ignore", message="The value of the smallest subnormal.*")

# 设置 numpy 错误处理模式
np.seterr(all="ignore")

# 如果需要，也可以设置特定的 numpy 错误处理
np.seterr(divide='ignore', invalid='ignore', over='ignore', under='ignore')

# GPU资源分配策略：为不同服务预留专用GPU资源
# GPU 0,1: 主要用于LLM (Ollama)
# GPU 2,3: 主要用于OCR/Layout，但可以动态共享
# 不设置全局CUDA_VISIBLE_DEVICES，让每个服务动态管理
# os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'  # 注释掉全局设置
os.environ['ONNXRUNTIME_CUDA_PROVIDER_OPTIONS'] = '{"cudnn_conv_algo_search": "DEFAULT"}'
# 添加 numpy 配置
os.environ['NUMPY_WARNINGS'] = 'ignore'

# 添加警告过滤
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 添加 ONNX Runtime 警告过滤
warnings.filterwarnings("ignore", category=UserWarning, module="onnxruntime")
warnings.filterwarnings("ignore", message=".*VerifyEachNodeIsAssignedToAnEp.*")
warnings.filterwarnings("ignore", message=".*Some nodes were not assigned.*")

# ONNX Runtime 环境配置
onnxruntime.set_default_logger_severity(3)  # 设置日志级别为 WARNING
os.environ['ORT_LOGGING_LEVEL'] = '2'  # 设置为 WARNING
os.environ['ORT_DISABLE_SHAPE_OPS_ON_CPU'] = '0'  # 允许在 CPU 上执行形状操作

# 定义 bearer
bearer = HTTPBearer()

# 定义应用
app = FastAPI(docs_url=None, redoc_url=None)

def setup_logging(log_dir):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "app.log")
    
    # 创建一个格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 获取根日志记录器
    rlog = logging.getLogger()
    
    # 清除所有现有的处理器
    for handler in rlog.handlers[:]:
        rlog.removeHandler(handler)
    
    # 设置日志
    rlog.setLevel(logging.INFO)
    
    # 创建文件处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    rlog.addHandler(file_handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    rlog.addHandler(console_handler)
    
    # 输出一条试日志
    logging.info("Logging system initialized")



# ⚠️  架构问题：这个类职责过多，违反单一职责原则
# TODO: 需要重构拆分成多个单一职责的类
@dataclass
class UnifiedServerManager:
    """
    统一服务器管理器 - 职责过多的问题类

    当前职责（应该拆分）：
    1. 🔗 Ollama服务器连接管理
    2. 📊 队列和并发控制
    3. 📝 任务生命周期管理
    4. 📈 性能统计和监控
    5. 🎯 GPU任务计数

    理想拆分：
    - OllamaServer: 只管连接
    - ConcurrencyController: 只管并发
    - TaskManager: 只管任务
    - MetricsCollector: 只管统计
    """
    url: str
    # 队列配置
    max_queue_size: int = 15
    max_concurrent_chat: int = 1  # 单GPU只能处理1个Chat请求
    max_concurrent_embeddings: int = 6  # Embedding并发限制

    def __post_init__(self):
        """初始化队列和计数器"""
        # 队列管理
        self.chat_queue = Queue()
        self.embeddings_queue = Queue()

        # 并发计数
        self.chat_processing: int = 0
        self.embeddings_processing: int = 0

        # 错误统计
        self.chat_errors: int = 0
        self.embeddings_errors: int = 0

        # 性能指标
        self.avg_response_time: float = 0.0
        self.total_requests: int = 0

        # 任务管理 (整合自UnifiedTaskManager)
        self.tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, 'TaskResult'] = {}
        self.task_counter: int = 0
        self.lock = asyncio.Lock()

        # GPU任务计数 (按类型分类)
        self.llm_chat_tasks: int = 0
        self.llm_embedding_tasks: int = 0
        self.ocr_tasks: int = 0

    def record_success(self, endpoint: str, response_time: float = 0.0):
        """记录成功请求"""
        if endpoint == "chat":
            self.chat_errors = 0
        else:
            self.embeddings_errors = 0

        # 更新性能指标
        self.total_requests += 1
        if response_time > 0:
            alpha = 0.1  # 平滑因子
            if self.avg_response_time == 0:
                self.avg_response_time = response_time
            else:
                self.avg_response_time = alpha * response_time + (1 - alpha) * self.avg_response_time

    def record_error(self, endpoint: str):
        """记录错误请求"""
        if endpoint == "chat":
            self.chat_errors += 1
        else:
            self.embeddings_errors += 1

    # 任务管理方法 (整合自UnifiedTaskManager)
    def generate_task_id(self, task_type: str) -> str:
        """生成唯一的任务ID"""
        import uuid
        return f"{task_type}_{uuid.uuid4().hex[:8]}"

    async def create_task(self, task_type: str, task_data: dict, gpu_id: Optional[int] = None) -> str:
        """创建新任务并返回task_id，同时更新计数器"""
        async with self.lock:
            task_id = self.generate_task_id(task_type)

            # 更新GPU任务计数
            if task_type == "llm":
                # 根据任务数据判断是chat还是embedding
                if "messages" in task_data or "prompt" in task_data:
                    self.llm_chat_tasks += 1
                    self.chat_processing += 1
                else:
                    self.llm_embedding_tasks += 1
                    self.embeddings_processing += 1
            elif task_type == "ocr":
                self.ocr_tasks += 1
            elif task_type == "embedding":
                self.llm_embedding_tasks += 1
                self.embeddings_processing += 1

            # 创建任务结果对象
            from dataclasses import dataclass
            task_result = TaskResult(
                task_id=task_id,
                task_type=task_type,
                status="processing",
                progress=0,
                gpu_id=gpu_id
            )
            self.task_results[task_id] = task_result

            logging.info(f"📝 创建任务 {task_id} [{task_type}] GPU计数更新")
            return task_id

    async def complete_task(self, task_id: str):
        """完成任务并清理计数器"""
        async with self.lock:
            if task_id in self.task_results:
                task_result = self.task_results[task_id]
                task_type = task_result.task_type

                # 更新GPU任务计数
                if task_type == "llm":
                    # 根据任务结果判断类型
                    if self.llm_chat_tasks > 0:
                        self.llm_chat_tasks -= 1
                        if self.chat_processing > 0:
                            self.chat_processing -= 1
                    elif self.llm_embedding_tasks > 0:
                        self.llm_embedding_tasks -= 1
                        if self.embeddings_processing > 0:
                            self.embeddings_processing -= 1
                elif task_type == "ocr" and self.ocr_tasks > 0:
                    self.ocr_tasks -= 1
                elif task_type == "embedding":
                    if self.llm_embedding_tasks > 0:
                        self.llm_embedding_tasks -= 1
                    if self.embeddings_processing > 0:
                        self.embeddings_processing -= 1

                # 更新任务状态
                task_result.status = "completed"
                task_result.progress = 100

                logging.info(f"✅ 完成任务 {task_id} [{task_type}] GPU计数更新")

    async def fail_task(self, task_id: str, error_message: str):
        """任务失败并清理计数器"""
        async with self.lock:
            if task_id in self.task_results:
                task_result = self.task_results[task_id]
                task_type = task_result.task_type

                # 更新GPU任务计数 (与complete_task相同的逻辑)
                if task_type == "llm":
                    if self.llm_chat_tasks > 0:
                        self.llm_chat_tasks -= 1
                        if self.chat_processing > 0:
                            self.chat_processing -= 1
                    elif self.llm_embedding_tasks > 0:
                        self.llm_embedding_tasks -= 1
                        if self.embeddings_processing > 0:
                            self.embeddings_processing -= 1
                elif task_type == "ocr" and self.ocr_tasks > 0:
                    self.ocr_tasks -= 1
                elif task_type == "embedding":
                    if self.llm_embedding_tasks > 0:
                        self.llm_embedding_tasks -= 1
                    if self.embeddings_processing > 0:
                        self.embeddings_processing -= 1

                # 更新任务状态
                task_result.status = "failed"
                task_result.error_message = error_message

                logging.info(f"❌ 任务失败 {task_id} [{task_type}] GPU计数更新: {error_message}")

    def get_status_dict(self) -> dict:
        """获取服务器状态字典"""
        return {
            "url": self.url,
            "chat_queue_size": self.chat_queue.qsize(),
            "embeddings_queue_size": self.embeddings_queue.qsize(),
            "chat_processing": self.chat_processing,
            "embeddings_processing": self.embeddings_processing,
            "max_concurrent_chat": self.max_concurrent_chat,
            "max_concurrent_embeddings": self.max_concurrent_embeddings,
            "chat_errors": self.chat_errors,
            "embeddings_errors": self.embeddings_errors,
            "avg_response_time": self.avg_response_time,
            "total_requests": self.total_requests,
            "llm_chat_tasks": self.llm_chat_tasks,
            "llm_embedding_tasks": self.llm_embedding_tasks,
            "ocr_tasks": self.ocr_tasks
        }

    def get_max_concurrent(self, endpoint: str) -> int:
        """动态获取最大并发数"""
        if endpoint == "chat":
            base_concurrent = self.max_concurrent_chat
        else:
            base_concurrent = self.max_concurrent_embeddings

        return base_concurrent

    def get_load_score(self, endpoint: str) -> float:
        """计算负载得分，越低越好，基于GPU使用率和内存使用率"""
        if endpoint == "chat":
            queue_size = self.chat_queue.qsize()
            processing = self.chat_processing
            max_concurrent = self.max_concurrent_chat
        else:
            queue_size = self.embeddings_queue.qsize()
            processing = self.embeddings_processing
            max_concurrent = self.max_concurrent_embeddings

        # 基础负载：队列和处理中的请求
        queue_load = queue_size / self.max_queue_size
        processing_load = processing / max_concurrent

        # 获取GPU硬件使用率
        gpu_utilization_score = 0.0
        gpu_memory_score = 0.0

        try:
            # 从端口推断服务器索引 (server0->GPU0, server1->GPU1)
            server_index = int(self.url.split(':')[-1]) - 11434
            gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 1

            if 0 <= server_index < gpu_count:
                # 获取GPU硬件信息
                gpu_info = get_gpu_info_nvidia_smi(server_index)

                if gpu_info:
                    # 使用GPU使用率和内存使用率
                    utilization_percent = gpu_info.get('utilization_percent', 0)
                    memory_total = gpu_info.get('memory_total_mb', 1)
                    memory_used = gpu_info.get('memory_used_mb', 0)
                    memory_usage_percent = (memory_used / memory_total) * 100 if memory_total > 0 else 0

                    # 转换为0-1的得分（使用率越高得分越高）
                    gpu_utilization_score = utilization_percent / 100.0
                    gpu_memory_score = memory_usage_percent / 100.0

                    # 状态显示
                    if utilization_percent < 10 and memory_usage_percent < 30:
                        status_emoji = "🟢"  # 绿色：空闲
                    elif utilization_percent < 50 and memory_usage_percent < 70:
                        status_emoji = "🟡"  # 黄色：中等负载
                    else:
                        status_emoji = "🔴"  # 红色：高负载

                    logging.info(f"{status_emoji} GPU{server_index} 硬件状态 [{endpoint}]: "
                               f"使用率={utilization_percent:.1f}%, 内存={memory_usage_percent:.1f}%, "
                               f"队列={queue_size}/{self.max_queue_size}, 处理中={processing}/{max_concurrent}")
                else:
                    # 如果无法获取nvidia-smi信息，回退到PyTorch方法
                    allocated, reserved = get_gpu_memory_info(server_index)
                    memory_usage_percent = (allocated / max(reserved, 1)) * 100 if reserved > 0 else 0
                    gpu_memory_score = memory_usage_percent / 100.0

                    logging.info(f"⚠️  GPU{server_index} 使用PyTorch回退 [{endpoint}]: "
                               f"内存={memory_usage_percent:.1f}%, 队列={queue_size}/{self.max_queue_size}")

        except Exception as e:
            logging.debug(f"Error getting GPU hardware info for {self.url}: {e}")

        # 计算最终得分：队列25%，处理中25%，GPU使用率25%，GPU内存25%
        # 所有指标都是越低越好
        final_score = (queue_load * 0.25 + processing_load * 0.25 +
                      gpu_utilization_score * 0.25 + gpu_memory_score * 0.25)

        logging.info(f"服务器 {self.url} 最终负载得分: {final_score:.3f} "
                    f"(队列={queue_load:.3f}, 处理={processing_load:.3f}, "
                    f"GPU使用率={gpu_utilization_score:.3f}, GPU内存={gpu_memory_score:.3f})")

        return final_score
    

    
    def reset_processing_counters(self):
        """重置处理计数器，用于处理系统卡死情况"""
        if self.chat_processing > 0:
            logging.warning(f"🔄 强制重置服务器 {self.url} 的处理计数器: chat从 {self.chat_processing} 到 0")
            self.chat_processing = 0
        if self.embeddings_processing > 0:
            logging.warning(f"🔄 强制重置服务器 {self.url} 的处理计数器: embeddings从 {self.embeddings_processing} 到 0")
            self.embeddings_processing = 0



# 在文件顶部定义全局变量
SERVERS_CONFIG: Dict[str, UnifiedServerManager] = {}
authorized_users: Dict[str, str] = {}
server_cycle = None
server_lock = Lock()
recognizer = None
timefinder = TimeFinder()

# ⚠️  架构问题：与UnifiedServerManager的GPU计数功能重叠
# TODO: 统一GPU资源管理，避免数据不一致
class GPUResourceManager:
    """
    GPU资源管理器 - 存在功能重叠问题

    问题：
    - 与UnifiedServerManager的GPU任务计数重叠
    - 数据可能不同步
    - 职责边界不清晰

    建议：
    - 统一GPU状态管理
    - 明确谁负责什么
    """
    def __init__(self):
        self.gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 0
        # 优化的GPU分配策略：
        # 基于测试结果：Ollama单卡可以很好地并发处理chat和embedding
        # 所有GPU都可以运行LLM(chat+embedding) + OCR/Layout实例
        self.all_gpus = list(range(self.gpu_count))

        # 每个GPU的资源状态
        self.gpu_status = {}
        for i in range(self.gpu_count):
            self.gpu_status[i] = {
                'llm_chat_tasks': 0,     # LLM Chat任务数
                'llm_embedding_tasks': 0, # LLM Embedding任务数
                'ocr_tasks': 0,          # OCR任务数
                'layout_tasks': 0,       # Layout任务数
                'memory_used': 0,        # 内存使用量
                'last_cleanup': time.time(),
                'lock': Lock(),
                # 任务优先级权重（基于实际资源占用）
                'llm_chat_weight': 3.0,     # Chat任务权重高（计算密集）
                'llm_embedding_weight': 1.0, # Embedding任务权重低（快速完成）
                'ocr_weight': 1.5,          # OCR任务权重中等（显存+计算）
                'layout_weight': 1.0        # Layout任务权重低（显存占用小）
            }

        logging.info(f"🔧 GPU资源管理器初始化:")
        logging.info(f"  总GPU数量: {self.gpu_count}")
        logging.info(f"  策略: 所有GPU运行LLM(Chat+Embedding)+OCR+Layout混合负载")
        logging.info(f"  ✅ 测试确认: Ollama单卡并发性能良好 (加速比2.79x)")
        logging.info(f"  📊 权重配置: Chat=3.0, Embedding=1.0, OCR=1.5, Layout=1.0")

    def get_best_gpu_for_task(self, task_type: str = "ocr") -> int:
        """为任务选择最佳GPU - 基于GPU硬件使用率和内存使用率"""
        best_gpu = 0
        min_load_score = float('inf')

        for gpu_id in self.all_gpus:
            if gpu_id >= self.gpu_count:
                continue

            # 获取GPU硬件使用率
            gpu_utilization = 0.0
            memory_usage_percent = 0.0

            try:
                # 优先使用nvidia-smi获取准确的硬件信息
                gpu_info = get_gpu_info_nvidia_smi(gpu_id)
                if gpu_info:
                    gpu_utilization = gpu_info.get('utilization_percent', 0)
                    memory_total = gpu_info.get('memory_total_mb', 1)
                    memory_used = gpu_info.get('memory_used_mb', 0)
                    memory_usage_percent = (memory_used / memory_total) * 100 if memory_total > 0 else 0
                else:
                    # 回退到PyTorch方法
                    allocated, reserved = get_gpu_memory_info(gpu_id)
                    memory_usage_percent = (allocated / max(reserved, 1)) * 100 if reserved > 0 else 0
                    gpu_utilization = 0  # PyTorch无法获取GPU使用率

            except Exception as e:
                logging.debug(f"获取GPU {gpu_id} 硬件信息失败: {e}")
                # 使用默认值
                gpu_utilization = 0.0
                memory_usage_percent = 0.0

            # 简单的负载评分：GPU使用率50% + 内存使用率50%
            # 分数越低越好
            load_score = (gpu_utilization / 100.0) * 0.5 + (memory_usage_percent / 100.0) * 0.5

            # 内存使用率过高时增加惩罚
            if memory_usage_percent > 80:
                load_score += 1.0  # 大幅惩罚
            elif memory_usage_percent > 60:
                load_score += 0.3  # 中等惩罚

            # 状态显示
            if gpu_utilization < 10 and memory_usage_percent < 30:
                status_emoji = "🟢"  # 绿色：空闲
            elif gpu_utilization < 50 and memory_usage_percent < 70:
                status_emoji = "🟡"  # 黄色：中等负载
            else:
                status_emoji = "🔴"  # 红色：高负载

            logging.info(f"{status_emoji} GPU{gpu_id} 硬件评估 [{task_type}]: "
                       f"使用率={gpu_utilization:.1f}%, 内存={memory_usage_percent:.1f}%, "
                       f"负载得分={load_score:.3f}")

            if load_score < min_load_score:
                min_load_score = load_score
                best_gpu = gpu_id

        logging.info(f"🎯 为 {task_type} 任务选择GPU {best_gpu}，硬件负载得分: {min_load_score:.3f}")
        return best_gpu

    def acquire_gpu(self, gpu_id: int, task_type: str = "ocr") -> bool:
        """获取GPU资源"""
        if gpu_id >= self.gpu_count:
            return False

        with self.gpu_status[gpu_id]['lock']:
            status = self.gpu_status[gpu_id]

            # 检查GPU内存是否足够
            try:
                allocated, reserved = get_gpu_memory_info(gpu_id)
                memory_usage = allocated / max(reserved, 1) if reserved > 0 else 0

                # 动态内存阈值：LLM任务要求更高
                if task_type == "llm":
                    memory_threshold = 0.75  # LLM任务75%阈值
                else:
                    memory_threshold = 0.90  # OCR/Layout任务90%阈值（显存占用低）

                if memory_usage > memory_threshold:
                    logging.warning(f"GPU {gpu_id} 内存使用过高 ({memory_usage:.1%})，拒绝{task_type}任务")
                    return False
            except:
                pass

            # 更新任务计数
            if task_type == "layout":
                status['layout_tasks'] += 1
            else:  # ocr
                status['ocr_tasks'] += 1

            total_llm = status['llm_chat_tasks'] + status['llm_embedding_tasks']
            logging.info(f"🔒 GPU {gpu_id} 资源获取成功 [{task_type}]: "
                        f"LLM={total_llm}, OCR={status['ocr_tasks']}, Layout={status['layout_tasks']}")
            return True

    def release_gpu(self, gpu_id: int, task_type: str = "ocr"):
        """释放GPU资源"""
        if gpu_id >= self.gpu_count:
            return

        with self.gpu_status[gpu_id]['lock']:
            status = self.gpu_status[gpu_id]

            # 更新任务计数
            if task_type == "layout":
                status['layout_tasks'] = max(0, status['layout_tasks'] - 1)
            else:  # ocr
                status['ocr_tasks'] = max(0, status['ocr_tasks'] - 1)

            total_llm = status['llm_chat_tasks'] + status['llm_embedding_tasks']
            logging.info(f"🔓 GPU {gpu_id} 资源释放 [{task_type}]: "
                        f"LLM={total_llm}, OCR={status['ocr_tasks']}, Layout={status['layout_tasks']}")

            # 如果GPU上OCR/Layout任务都完成，执行轻量清理
            total_tasks = total_llm + status['ocr_tasks'] + status['layout_tasks']
            ocr_layout_tasks = status['ocr_tasks'] + status['layout_tasks']

            if ocr_layout_tasks == 0:  # OCR/Layout任务全部完成
                current_time = time.time()
                if current_time - status['last_cleanup'] > 30:  # 30秒清理一次
                    try:
                        torch.cuda.set_device(gpu_id)
                        # 轻量清理：只清理未使用的缓存，不影响LLM
                        torch.cuda.empty_cache()
                        status['last_cleanup'] = current_time
                        logging.info(f"🧹 GPU {gpu_id} OCR/Layout内存清理完成")
                    except:
                        pass

# ⚠️  架构问题：设计了路由但实际很多任务绕过它
# TODO: 要么真正使用路由，要么删除这个类
class TaskRouter:
    """
    任务队列路由系统 - 设计与实际使用不符

    问题：
    - 有全局队列但很多任务直接通过UnifiedServerManager处理
    - GPU工作器定义了但没有充分利用
    - 路由功能没有真正发挥作用

    建议：
    - 要么让所有任务都通过路由
    - 要么删除这个类，简化架构
    """
    def __init__(self, gpu_manager):
        self.gpu_manager = gpu_manager
        # 全局任务队列
        self.task_queue = asyncio.Queue(maxsize=100)  # 全局任务队列
        # 每个GPU的工作队列
        self.gpu_workers = {}
        # 任务结果存储
        self.task_results = {}
        # 路由统计
        self.route_stats = {
            'total_tasks': 0,
            'gpu_assignments': {i: 0 for i in range(gpu_manager.gpu_count)},
            'task_types': {'ocr': 0, 'layout': 0, 'llm': 0}
        }

        logging.info(f"🚀 任务路由器初始化完成")
        logging.info(f"  全局队列大小: 100")
        logging.info(f"  GPU工作器数量: {gpu_manager.gpu_count}")

    async def submit_task(self, task_id: str, task_type: str, task_data: dict) -> str:
        """提交任务到全局队列"""
        task = {
            'id': task_id,
            'type': task_type,
            'data': task_data,
            'submitted_at': time.time(),
            'retries': 0,
            'max_retries': 2
        }

        try:
            await self.task_queue.put(task)
            self.route_stats['total_tasks'] += 1
            self.route_stats['task_types'][task_type] += 1
            logging.info(f"📥 任务 {task_id} [{task_type}] 已提交到全局队列")
            return task_id
        except asyncio.QueueFull:
            logging.error(f"❌ 全局任务队列已满，拒绝任务 {task_id}")
            raise HTTPException(status_code=503, detail="任务队列已满，请稍后重试")

    async def get_task_result(self, task_id: str, timeout: float = 30.0) -> dict:
        """获取任务结果"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if task_id in self.task_results:
                result = self.task_results.pop(task_id)
                return result
            await asyncio.sleep(0.1)

        # 超时处理
        logging.warning(f"⏰ 任务 {task_id} 获取结果超时")
        return {
            'status': 'timeout',
            'error': f'任务执行超时 ({timeout}s)',
            'progress': 100
        }

    def get_route_stats(self) -> dict:
        """获取路由统计信息"""
        # 获取统一任务管理器的统计
        unified_stats = unified_task_manager.get_task_stats()

        # 计算处理中的任务数（从GPU状态统计）
        processing_tasks = sum(
            self.gpu_manager.gpu_status[i].get('llm_chat_tasks', 0) +
            self.gpu_manager.gpu_status[i].get('llm_embedding_tasks', 0) +
            self.gpu_manager.gpu_status[i].get('ocr_tasks', 0)
            for i in range(self.gpu_manager.gpu_count)
        )

        return {
            'total_tasks': unified_stats["total_tasks"],
            'queue_size': self.task_queue.qsize(),
            'processing': processing_tasks,
            'completed': unified_stats["completed_tasks"],
            'gpu_status': {
                str(i): {
                    'llm_tasks': self.gpu_manager.gpu_status[i].get('llm_chat_tasks', 0),
                    'embedding_tasks': self.gpu_manager.gpu_status[i].get('llm_embedding_tasks', 0),
                    'ocr_tasks': self.gpu_manager.gpu_status[i].get('ocr_tasks', 0)
                } for i in range(self.gpu_manager.gpu_count)
            }
        }

# GPU工作器类
class GPUWorker:
    def __init__(self, gpu_id: int, gpu_manager, task_router):
        self.gpu_id = gpu_id
        self.gpu_manager = gpu_manager
        self.task_router = task_router
        self.is_running = False
        self.current_task = None
        self.processed_tasks = 0
        self.failed_tasks = 0

    async def start(self):
        """启动GPU工作器"""
        self.is_running = True
        logging.info(f"🔧 GPU {self.gpu_id} 工作器启动")

        while self.is_running:
            try:
                # 从全局队列获取任务
                task = await asyncio.wait_for(
                    self.task_router.task_queue.get(),
                    timeout=1.0
                )

                # 检查GPU是否可用
                if not self.gpu_manager.acquire_gpu(self.gpu_id, task['type']):
                    # GPU不可用，将任务放回队列
                    await self.task_router.task_queue.put(task)
                    await asyncio.sleep(0.5)  # 短暂等待
                    continue

                # 执行任务
                self.current_task = task
                await self._execute_task(task)
                self.processed_tasks += 1

            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logging.error(f"❌ GPU {self.gpu_id} 工作器异常: {str(e)}")
                if self.current_task:
                    self.failed_tasks += 1
                    # 记录失败结果
                    self.task_router.task_results[self.current_task['id']] = {
                        'status': 'failed',
                        'error': f'GPU {self.gpu_id} 工作器异常: {str(e)}',
                        'progress': 100
                    }
                await asyncio.sleep(1.0)  # 异常后等待
            finally:
                if self.current_task:
                    self.gpu_manager.release_gpu(self.gpu_id, self.current_task['type'])
                    self.current_task = None

    async def _execute_task(self, task):
        """执行具体任务"""
        task_id = task['id']
        task_type = task['type']
        task_data = task['data']

        logging.info(f"🎯 GPU {self.gpu_id} 开始执行任务 {task_id} [{task_type}]")

        try:
            if task_type == 'ocr':
                result = await self._execute_ocr_task(task_data)
            elif task_type == 'layout':
                result = await self._execute_layout_task(task_data)
            elif task_type == 'llm':
                result = await self._execute_llm_task(task_data)
            else:
                raise ValueError(f"未知任务类型: {task_type}")

            # 记录成功结果
            self.task_router.task_results[task_id] = {
                'status': 'completed',
                'result': result,
                'progress': 100,
                'gpu_id': self.gpu_id
            }

            # 更新路由统计
            self.task_router.route_stats['gpu_assignments'][self.gpu_id] += 1

            logging.info(f"✅ GPU {self.gpu_id} 任务 {task_id} 执行完成")

        except Exception as e:
            logging.error(f"❌ GPU {self.gpu_id} 任务 {task_id} 执行失败: {str(e)}")

            # 重试逻辑
            task['retries'] += 1
            if task['retries'] < task['max_retries']:
                logging.info(f"🔄 任务 {task_id} 重试 ({task['retries']}/{task['max_retries']})")
                await self.task_router.task_queue.put(task)
            else:
                # 重试次数用完，记录失败结果
                self.task_router.task_results[task_id] = {
                    'status': 'failed',
                    'error': str(e),
                    'progress': 100,
                    'gpu_id': self.gpu_id,
                    'retries': task['retries']
                }

    async def _execute_ocr_task(self, task_data):
        """执行OCR任务"""
        try:
            # 设置GPU上下文
            torch.cuda.set_device(self.gpu_id)

            # 获取任务数据
            image = task_data['image']
            timeout = task_data.get('timeout', 30.0)
            enable_seal_hw = task_data.get('enable_seal_hw', False)

            # 转换为RGB格式用于模型推理
            img_array = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 步骤1: 版面分析 (获取Layout资源)
            if not self.gpu_manager.acquire_gpu(self.gpu_id, "layout"):
                logging.warning(f"GPU {self.gpu_id} Layout资源获取失败，跳过版面分析")
                layout_results = []
            else:
                try:
                    logging.info(f"GPU {self.gpu_id} 执行版面分析...")
                    layout_results = rec.rapid_layout_with_params(img_array, gpu_id=self.gpu_id)
                    logging.info(f"GPU {self.gpu_id} 检测到 {len(layout_results)} 个版面区域")
                finally:
                    self.gpu_manager.release_gpu(self.gpu_id, "layout")

            # 步骤2: 整页OCR识别 (使用已获取的OCR资源)
            logging.info(f"GPU {self.gpu_id} 执行整页OCR识别...")
            ocr_results = rec.rapid_ocr_with_params(img_array, gpu_id=self.gpu_id, version='v5')
            logging.info(f"GPU {self.gpu_id} 识别到 {len(ocr_results) if ocr_results else 0} 个文本区域")

            # 步骤3: 综合处理结果
            combined_results = rec.rapid_document_analysis(img_array, gpu_id=self.gpu_id)

            # 构建返回结果
            result = {
                "layout_results": layout_results,
                "ocr_results": ocr_results,
                "combined_results": combined_results,
                "gpu_id": self.gpu_id,
                "processing_time": time.time()
            }

            logging.info(f"✅ GPU {self.gpu_id} OCR任务执行完成")
            return result

        except Exception as e:
            logging.error(f"❌ GPU {self.gpu_id} OCR任务执行失败: {str(e)}")
            raise

    async def _execute_layout_task(self, task_data):
        """执行Layout任务"""
        # 这里调用原来的Layout处理逻辑
        await asyncio.sleep(0.1)  # 模拟处理时间
        return {"type": "layout", "gpu_id": self.gpu_id, "data": task_data}

    async def _execute_llm_task(self, task_data):
        """执行LLM任务"""
        # 这里调用原来的LLM处理逻辑
        await asyncio.sleep(0.1)  # 模拟处理时间
        return {"type": "llm", "gpu_id": self.gpu_id, "data": task_data}

    async def stop(self):
        """停止GPU工作器"""
        self.is_running = False
        logging.info(f"🛑 GPU {self.gpu_id} 工作器停止 (处理: {self.processed_tasks}, 失败: {self.failed_tasks})")

# 全局GPU资源管理器和任务路由器
gpu_manager = GPUResourceManager()
task_router = TaskRouter(gpu_manager)

# 定义GPU设备和对应的队列（保持向后兼容）
GPU_QUEUES = {}
GPU_LOCKS = {}
GPU_PROCESSES = {}

def setup_gpu_queues():
    """初始化GPU队列和锁"""
    global GPU_QUEUES, GPU_LOCKS

    # 使用GPU资源管理器的GPU数量
    gpu_count = gpu_manager.gpu_count
    logging.info(f"🔧 初始化GPU队列，GPU数量: {gpu_count}")

    # 清理现有的队列和锁
    GPU_QUEUES.clear()
    GPU_LOCKS.clear()

    for i in range(gpu_count):
        # 混合负载策略：所有GPU都支持OCR/Layout，队列大小适中
        maxsize = 6  # 适中的队列大小，平衡LLM和OCR/Layout需求

        GPU_QUEUES[i] = Queue(maxsize=maxsize)
        GPU_LOCKS[i] = Lock()
        logging.info(f"  ✓ GPU {i} 队列初始化完成 (maxsize={maxsize}, 支持LLM+OCR+Layout混合负载)")

async def gpu_health_monitor():
    """GPU健康监控任务"""
    while True:
        try:
            for gpu_id in range(gpu_manager.gpu_count):
                try:
                    # 检查GPU状态
                    allocated, reserved = get_gpu_memory_info(gpu_id)
                    memory_usage = allocated / max(reserved, 1) if reserved > 0 else 0

                    status = gpu_manager.gpu_status[gpu_id]

                    # 如果内存使用过高且没有任务，强制清理
                    total_llm = status['llm_chat_tasks'] + status['llm_embedding_tasks']
                    if memory_usage > 0.9 and total_llm == 0 and status['ocr_tasks'] == 0:
                        logging.warning(f"🧹 GPU {gpu_id} 内存使用过高 ({memory_usage:.1%})，执行强制清理")
                        torch.cuda.set_device(gpu_id)
                        torch.cuda.empty_cache()

                    # 检查是否有僵尸任务（任务计数异常）
                    current_time = time.time()
                    if current_time - status['last_cleanup'] > 300:  # 5分钟
                        if total_llm > 0 or status['ocr_tasks'] > 0:
                            logging.warning(f"🔄 GPU {gpu_id} 可能存在僵尸任务，重置计数器")
                            status['llm_chat_tasks'] = 0
                            status['llm_embedding_tasks'] = 0
                            status['ocr_tasks'] = 0

                except Exception as e:
                    logging.error(f"❌ GPU {gpu_id} 健康检查失败: {str(e)}")

            await asyncio.sleep(60)  # 每分钟检查一次

        except Exception as e:
            logging.error(f"❌ GPU健康监控任务异常: {str(e)}")
            await asyncio.sleep(60)

# GPU任务类型跟踪已移除，使用统一任务管理器

# Embedding模型配置 - 简化版本
EMBEDDING_MODEL_CONFIG = {
    "hngpt-embedding": {
        "supports_instructions": True,
        "default_instruction": "Represent this document for retrieval and similarity search:",
        "requires_normalization": True,
        "add_endoftext": True,
        "dimensions": 1024
    },
    "hngpt-embedding": {
        "supports_instructions": False,
        "default_instruction": "",
        "requires_normalization": False,
        "add_endoftext": False,
        "dimensions": None
    }
}

def prepare_embedding_text(text: str, model: str) -> str:
    """
    根据模型类型准备embedding文本 - 简化版本
    """
    model_config = EMBEDDING_MODEL_CONFIG.get(model, EMBEDDING_MODEL_CONFIG["hngpt-embedding"])

    # 如果模型支持指令感知，添加默认指令
    if model_config["supports_instructions"] and model_config["default_instruction"]:
        formatted_text = f"{model_config['default_instruction']}\n{text}"
    else:
        formatted_text = text

    # 添加结束标记（如果需要）
    if model_config["add_endoftext"]:
        formatted_text = f"{formatted_text}<|endoftext|>"

    return formatted_text

def normalize_embedding(embedding: List[float]) -> List[float]:
    """
    L2归一化embedding向量
    """
    if not embedding:
        return embedding

    np_embedding = np.array(embedding)
    norm = np.linalg.norm(np_embedding)
    if norm > 0:
        return (np_embedding / norm).tolist()
    return embedding

# 旧的GPU任务管理函数已删除，使用统一任务管理器和GPU资源管理器

# 修改初始化函数
async def initialize():
    global SERVERS_CONFIG, authorized_users, recognizer
    
    # 动态根据GPU数量配置服务器
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 1

    logging.info(f"🔍 GPU检测结果:")
    logging.info(f"  CUDA可用: {gpu_available}")
    logging.info(f"  检测到GPU数量: {gpu_count}")

    if gpu_available:
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            logging.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

    logging.info(f"配置 {gpu_count} 个Ollama服务器")

    # 根据GPU数量动态生成服务器配置
    SERVERS_CONFIG = {}
    base_port = 11434  # Ollama默认端口

    logging.info(f"🔧 开始配置服务器:")
    for i in range(gpu_count):
        server_name = f'server{i}'
        server_port = base_port - i  # 端口递减：11434, 11433, 11432, ...
        server_url = f'http://localhost:{server_port}'
        SERVERS_CONFIG[server_name] = UnifiedServerManager(url=server_url)
        logging.info(f"  ✓ {server_name} -> {server_url} (GPU {i})")

    logging.info(f"📊 服务器配置完成:")
    logging.info(f"  总服务器数: {len(SERVERS_CONFIG)}")
    logging.info(f"  服务器列表: {list(SERVERS_CONFIG.keys())}")
    logging.info(f"  端口范围: {base_port - gpu_count + 1} - {base_port}")

    # 如果没有GPU，至少配置一个服务器
    if not SERVERS_CONFIG:
        SERVERS_CONFIG['server0'] = UnifiedServerManager(url='http://localhost:11434')
        logging.info("No GPU detected, configured single server on CPU")
    
    # 初始化用户列表
    authorized_users = await get_authorized_users("authorized_users.txt")
    
    # # 初始化识别器，直接使用 HSRecognizer
    # recognizer = HSRecognizer(models_dir="models")
    
    logging.info(f"Initialized {len(SERVERS_CONFIG)} servers for load balancing")
    logging.info(f"Loaded {len(authorized_users)} authorized users")
    # logging.info("Initialized HSRecognizer")

    # 检查所有服务器的初始状态
    await check_all_servers_status()

async def check_all_servers_status():
    """检查所有配置的服务器状态"""
    logging.info(f"🔍 检查所有服务器状态:")

    for server_name, server_status in SERVERS_CONFIG.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{server_status.url}/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    logging.info(f"  ✅ {server_name} ({server_status.url}): 在线, {len(models)}个模型")
                else:
                    logging.warning(f"  ❌ {server_name} ({server_status.url}): HTTP {response.status_code}")
        except Exception as e:
            logging.error(f"  ❌ {server_name} ({server_status.url}): 连接失败 - {str(e)}")

    logging.info(f"📊 服务器状态检查完成")
    logging.info(f"  配置的服务器: {len(SERVERS_CONFIG)}")



@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logging.info("🚀 Starting application...")

    try:
        # 检查CUDA环境
        cuda_ok = check_cuda_environment()
        if not cuda_ok:
            logging.warning("⚠️ CUDA环境检查失败，将使用CPU模式运行")

        # 初始化应用
        await initialize()

        # 初始化 GPU 队列
        setup_gpu_queues()

        # 启动GPU健康监控任务
        gpu_monitor = asyncio.create_task(gpu_health_monitor())

        # 启动GPU工作器
        gpu_workers = []
        if gpu_manager.gpu_count > 0:
            for gpu_id in range(gpu_manager.gpu_count):
                try:
                    worker = GPUWorker(gpu_id, gpu_manager, task_router)
                    gpu_workers.append(worker)
                    asyncio.create_task(worker.start())
                    logging.info(f"✓ GPU {gpu_id} 工作器启动成功")
                except Exception as e:
                    logging.error(f"❌ GPU {gpu_id} 工作器启动失败: {str(e)}")
                    # 继续启动其他GPU工作器
                    continue

        logging.info(f"🚀 成功启动了 {len(gpu_workers)} 个GPU工作器")

        if len(gpu_workers) == 0:
            logging.warning("⚠️ 没有GPU工作器启动成功，系统将以CPU模式运行")

        yield

    except Exception as e:
        logging.error(f"❌ 应用启动过程中发生错误: {str(e)}")
        # 即使启动过程中有错误，也要尝试正常关闭
        yield

    # Shutdown
    logging.info("🔄 正在关闭应用...")

    try:
        # 取消GPU监控任务
        if 'gpu_monitor' in locals():
            gpu_monitor.cancel()
            try:
                await gpu_monitor
            except asyncio.CancelledError:
                pass
            except Exception as e:
                logging.error(f"关闭GPU监控任务时出错: {str(e)}")

        # 清理GPU工作器
        if 'gpu_workers' in locals():
            for worker in gpu_workers:
                try:
                    # 这里可以添加工作器的清理逻辑
                    pass
                except Exception as e:
                    logging.error(f"清理GPU工作器时出错: {str(e)}")

        logging.info("✓ 应用关闭完成")

    except Exception as e:
        logging.error(f"❌ 应用关闭过程中发生错误: {str(e)}")
    
    # 清理GPU资源
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

# 生成队列状态可视化的函数
def get_queue_status_bar(server_name, queue_size, processing, max_queue_size, endpoint_type="chat"):
    """生成ASCII进度条显示队列状态"""
    # 进度条宽度等于max_queue_size
    bar_width = max_queue_size
    
    # 根据状态决定显示
    if queue_size > 0 or processing > 0:
        # 有请求在处理中或队列中
        bar = '█' * bar_width
    else:
        # 空闲状态
        bar = '░' * bar_width
    
    # 计算利用率百分比
    utilization = 100 * (queue_size + processing) / max(max_queue_size, 1)
    
    return f"[{server_name}:{endpoint_type}] [{bar}] {queue_size}队列/{processing}处理中/最大{max_queue_size} ({utilization:.0f}%)"

# 优化：改进的服务器选择函数
async def get_best_server(endpoint: str = "chat") -> Tuple[str, UnifiedServerManager]:
    """获取可用于特定端点的最佳服务器"""
    if not SERVERS_CONFIG:
        raise HTTPException(
            status_code=503,
            detail="没有可用的服务器"
        )



    # 查找可用服务器并按负载排序
    available_servers = []

    # 检查所有服务器的容量
    for name, server in SERVERS_CONFIG.items():
        max_concurrent = server.get_max_concurrent(endpoint)

        # 检查服务器是否有容量
        has_capacity = False
        if endpoint == "chat":
            has_capacity = (server.chat_processing < max_concurrent and
                          server.chat_queue.qsize() < server.max_queue_size)
        else:  # embeddings
            has_capacity = (server.embeddings_processing < max_concurrent and
                          server.embeddings_queue.qsize() < server.max_queue_size)

        if has_capacity:
            load_score = server.get_load_score(endpoint)
            queue_status = get_queue_status_bar(
                name,
                server.chat_queue.qsize() if endpoint == "chat" else server.embeddings_queue.qsize(),
                server.chat_processing if endpoint == "chat" else server.embeddings_processing,
                server.max_queue_size,
                endpoint
            )

            available_servers.append((name, server, load_score))
            logging.info(f"✅ 可用{endpoint}服务器: {queue_status}, 负载得分: {load_score:.3f}")

    if not available_servers:
        raise HTTPException(
            status_code=503,
            detail=f"所有服务器的 {endpoint} 端点已满"
        )
    
    # 修复：按负载得分排序，选择负载最低的服务器
    # 确保所有服务器都有负载得分
    servers_with_scores = []
    for item in available_servers:
        if len(item) == 3:  # 包含负载得分
            name, server, load_score = item
            servers_with_scores.append((name, server, load_score))
        else:
            name, server = item
            # 如果没有负载得分，计算一个
            load_score = server.get_load_score(endpoint)
            servers_with_scores.append((name, server, load_score))

    # 按负载得分排序（从低到高）
    servers_with_scores.sort(key=lambda x: x[2])

    # 详细日志：显示所有服务器的负载得分
    logging.info(f"📊 {endpoint} 服务器负载得分排序:")
    for i, (name, server, score) in enumerate(servers_with_scores):
        queue_size = server.chat_queue.qsize() if endpoint == "chat" else server.embeddings_queue.qsize()
        processing = server.chat_processing if endpoint == "chat" else server.embeddings_processing
        max_concurrent = server.max_concurrent_chat if endpoint == "chat" else server.max_concurrent_embeddings
        logging.info(f"  {i+1}. {name}: 得分={score:.3f} (队列={queue_size}, 处理={processing}/{max_concurrent})")

    # 直接选择负载得分最低的服务器（基于GPU硬件使用率）
    selected = (servers_with_scores[0][0], servers_with_scores[0][1])  # 负载最低的服务器

    # 获取选中服务器的GPU硬件信息用于日志
    try:
        server_index = int(selected[1].url.split(':')[-1]) - 11434
        gpu_info_detail = get_gpu_info_nvidia_smi(server_index)
        if gpu_info_detail:
            utilization = gpu_info_detail.get('utilization_percent', 0)
            memory_used = gpu_info_detail.get('memory_used_mb', 0)
            memory_total = gpu_info_detail.get('memory_total_mb', 1)
            memory_percent = (memory_used / memory_total) * 100 if memory_total > 0 else 0
            gpu_info = f"GPU{server_index}[使用率:{utilization:.1f}%,内存:{memory_percent:.1f}%]"
        else:
            gpu_info = f"GPU{server_index}[信息获取失败]"
    except:
        gpu_info = "GPU?"

    logging.debug(f"🎯 基于硬件使用率选择服务器: {selected[0]} {gpu_info} 处理 {endpoint} 任务")

    queue_status = get_queue_status_bar(
        selected[0],
        selected[1].chat_queue.qsize() if endpoint == "chat" else selected[1].embeddings_queue.qsize(),
        selected[1].chat_processing if endpoint == "chat" else selected[1].embeddings_processing,
        selected[1].max_queue_size,
        endpoint
    )

    # 获取GPU信息用于日志
    try:
        server_index = int(selected[1].url.split(':')[-1]) - 11434
        gpu_status = task_router.gpu_manager.gpu_status.get(server_index, {})
        chat_tasks = gpu_status.get('llm_chat_tasks', 0)
        embed_tasks = gpu_status.get('llm_embedding_tasks', 0)
        gpu_info = f"GPU{server_index}[Chat:{chat_tasks},Embed:{embed_tasks}]"
    except:
        gpu_info = "GPU?"

    logging.debug(f"🎯 选择服务器: {selected[0]} {gpu_info} 处理 {endpoint} 任务 - {queue_status}")
    return selected

class RequestBody(BaseModel):
    # Add fields according to your API requirements
    pass

# 添加 ShutdownRequest 模型定义
class ShutdownRequest(BaseModel):
    confirm: bool = Field(..., description="Set to true to confirm shutdown")

class LoggerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        end_time = time.time()
        
        logging.info(
            f"Path: {request.url.path} "
            f"Method: {request.method} "
            f"Client: {request.client.host} "
            f"Time: {end_time - start_time:.3f}s"
        )
        
        return response

app = FastAPI(
    title="hngpt AI Model Server",
    version="0.1.0",
    lifespan=lifespan,
    docs_url=None,  # 禁用默认的 /docs 端点
    redoc_url=None  # 禁用默认的 /redoc 端点
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加安全中间件
app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])

# ============================================================================
# 新的统一GPU负载均衡器 - 解决架构混乱问题
# ============================================================================

class UnifiedGPULoadBalancer:
    """
    统一GPU负载均衡器

    功能：
    1. 基于nvidia-smi实时GPU状态选择最佳GPU
    2. 为不同类型请求分配合适的GPU
    3. 管理每个GPU的并发限制
    4. 统一的负载评分算法
    """

    def __init__(self):
        self.gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 1

        # 每种请求类型在单卡上的并发限制
        self.concurrent_limits = {
            "chat": 1,      # Chat请求：单卡并发1
            "embedding": 1, # Embedding请求：单卡并发1 (修改为1以避免资源竞争)
            "ocr": 1        # OCR请求：单卡并发1
        }

        # 每个GPU当前的并发计数
        self.current_loads = {}
        for gpu_id in range(self.gpu_count):
            self.current_loads[gpu_id] = {
                "chat": 0,
                "embedding": 0,
                "ocr": 0
            }

        # 锁保护并发计数
        self.lock = asyncio.Lock()

        logging.info(f"🎯 统一GPU负载均衡器初始化完成")
        logging.info(f"  GPU数量: {self.gpu_count}")
        logging.info(f"  并发限制: {self.concurrent_limits}")

    async def get_best_gpu(self, request_type: str) -> int:
        """
        为指定类型的请求选择最佳GPU

        Args:
            request_type: 请求类型 ("chat", "embedding", "ocr")

        Returns:
            最佳GPU的ID

        Raises:
            HTTPException: 如果没有可用的GPU
        """
        if request_type not in self.concurrent_limits:
            raise ValueError(f"未知请求类型: {request_type}")

        async with self.lock:
            available_gpus = []
            max_concurrent = self.concurrent_limits[request_type]

            for gpu_id in range(self.gpu_count):
                current_load = self.current_loads[gpu_id][request_type]

                # 检查是否还有并发槽位
                if current_load >= max_concurrent:
                    continue

                # 获取GPU硬件状态
                gpu_info = get_gpu_info_nvidia_smi(gpu_id)
                if not gpu_info:
                    continue

                # 计算负载得分
                load_score = self._calculate_load_score(gpu_id, gpu_info, request_type)

                available_gpus.append((gpu_id, load_score, current_load, gpu_info))

            if not available_gpus:
                raise HTTPException(
                    status_code=503,
                    detail=f"所有GPU的{request_type}槽位都已满"
                )

            # 按负载得分排序，选择负载最低的GPU
            available_gpus.sort(key=lambda x: x[1])
            best_gpu_id, best_score, current_load, gpu_info = available_gpus[0]

            # 记录选择结果
            utilization = gpu_info.get('utilization_percent', 0)
            memory_usage = (gpu_info.get('memory_used_mb', 0) /
                          max(gpu_info.get('memory_total_mb', 1), 1)) * 100

            logging.info(f"🎯 为{request_type}选择GPU {best_gpu_id}: "
                        f"负载得分={best_score:.3f}, "
                        f"当前并发={current_load}/{max_concurrent}, "
                        f"GPU使用率={utilization:.1f}%, "
                        f"内存使用率={memory_usage:.1f}%")

            return best_gpu_id

    def _calculate_load_score(self, gpu_id: int, gpu_info: dict, request_type: str) -> float:
        """
        计算GPU负载得分（越低越好）

        考虑因素：
        1. GPU硬件使用率 (40%)
        2. GPU内存使用率 (40%)
        3. 当前并发负载 (20%)
        """
        # GPU硬件指标
        utilization_percent = gpu_info.get('utilization_percent', 0)
        memory_total = gpu_info.get('memory_total_mb', 1)
        memory_used = gpu_info.get('memory_used_mb', 0)
        memory_usage_percent = (memory_used / memory_total) * 100 if memory_total > 0 else 0

        # 当前并发负载
        current_load = self.current_loads[gpu_id][request_type]
        max_load = self.concurrent_limits[request_type]
        load_ratio = current_load / max_load if max_load > 0 else 0

        # 综合得分计算
        hardware_score = (utilization_percent / 100.0) * 0.4
        memory_score = (memory_usage_percent / 100.0) * 0.4
        concurrency_score = load_ratio * 0.2

        final_score = hardware_score + memory_score + concurrency_score

        return final_score

    async def acquire_gpu_slot(self, gpu_id: int, request_type: str) -> bool:
        """
        获取GPU槽位

        Args:
            gpu_id: GPU ID
            request_type: 请求类型

        Returns:
            是否成功获取槽位
        """
        if request_type not in self.concurrent_limits:
            return False

        async with self.lock:
            current_load = self.current_loads[gpu_id][request_type]
            max_load = self.concurrent_limits[request_type]

            if current_load >= max_load:
                return False

            # 增加并发计数
            self.current_loads[gpu_id][request_type] += 1

            logging.debug(f"🔒 获取GPU {gpu_id} {request_type}槽位: "
                         f"{self.current_loads[gpu_id][request_type]}/{max_load}")

            return True

    async def release_gpu_slot(self, gpu_id: int, request_type: str):
        """
        释放GPU槽位

        Args:
            gpu_id: GPU ID
            request_type: 请求类型
        """
        if request_type not in self.concurrent_limits:
            return

        async with self.lock:
            if self.current_loads[gpu_id][request_type] > 0:
                self.current_loads[gpu_id][request_type] -= 1

                logging.debug(f"🔓 释放GPU {gpu_id} {request_type}槽位: "
                             f"{self.current_loads[gpu_id][request_type]}/{self.concurrent_limits[request_type]}")

    def get_gpu_status(self) -> Dict:
        """获取所有GPU的状态信息"""
        status = {
            "gpu_count": self.gpu_count,
            "concurrent_limits": self.concurrent_limits,
            "current_loads": self.current_loads.copy(),
            "gpu_details": {}
        }

        for gpu_id in range(self.gpu_count):
            gpu_info = get_gpu_info_nvidia_smi(gpu_id)
            if gpu_info:
                status["gpu_details"][gpu_id] = {
                    "name": gpu_info.get('name', 'Unknown'),
                    "utilization_percent": gpu_info.get('utilization_percent', 0),
                    "memory_usage_percent": (gpu_info.get('memory_used_mb', 0) /
                                           max(gpu_info.get('memory_total_mb', 1), 1)) * 100,
                    "temperature_c": gpu_info.get('temperature_c', 0),
                    "power_draw_w": gpu_info.get('power_draw_w', 0)
                }

        return status

    async def get_best_server_for_request(self, request_type: str) -> Tuple[str, int]:
        """
        为请求选择最佳服务器

        Args:
            request_type: 请求类型

        Returns:
            (server_name, gpu_id) 元组
        """
        gpu_id = await self.get_best_gpu(request_type)
        server_name = f"server{gpu_id}"

        return server_name, gpu_id


# 创建全局负载均衡器实例
unified_gpu_balancer = UnifiedGPULoadBalancer()

# ============================================================================
# 使用新负载均衡器的辅助函数
# ============================================================================

async def process_request_with_unified_balancer(request_type: str, request_data: dict) -> dict:
    """
    使用统一负载均衡器处理请求的示例函数

    Args:
        request_type: 请求类型 ("chat", "embedding", "ocr")
        request_data: 请求数据

    Returns:
        处理结果
    """
    # 1. 选择最佳GPU
    try:
        gpu_id = await unified_gpu_balancer.get_best_gpu(request_type)
    except HTTPException as e:
        logging.error(f"无法为{request_type}请求分配GPU: {e.detail}")
        raise e

    # 2. 获取GPU槽位
    if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, request_type):
        raise HTTPException(status_code=503, detail=f"GPU {gpu_id} {request_type}槽位获取失败")

    try:
        # 3. 执行实际请求
        logging.info(f"🚀 在GPU {gpu_id}上执行{request_type}请求")

        if request_type == "chat":
            # 调用实际的Chat处理逻辑
            server_url = f"http://localhost:{11434 - gpu_id}"
            result = {"type": "chat", "server_url": server_url, "gpu_id": gpu_id, "status": "completed"}

        elif request_type == "embedding":
            # 调用实际的Embedding处理逻辑
            server_url = f"http://localhost:{11434 - gpu_id}"
            result = {"type": "embedding", "server_url": server_url, "gpu_id": gpu_id, "status": "completed"}

        elif request_type == "ocr":
            # 调用实际的OCR处理逻辑
            result = {"type": "ocr", "gpu_id": gpu_id, "status": "completed"}

        else:
            raise ValueError(f"未知请求类型: {request_type}")

        logging.info(f"✅ GPU {gpu_id}上的{request_type}请求执行完成")
        return result

    except Exception as e:
        logging.error(f"❌ GPU {gpu_id}上的{request_type}请求执行失败: {str(e)}")
        raise e

    finally:
        # 4. 释放GPU槽位
        await unified_gpu_balancer.release_gpu_slot(gpu_id, request_type)

# ============================================================================
# 使用新负载均衡器的辅助函数
# ============================================================================

async def process_request_with_unified_balancer(request_type: str, request_data: dict) -> dict:
    """
    使用统一负载均衡器处理请求的示例函数

    Args:
        request_type: 请求类型 ("chat", "embedding", "ocr")
        request_data: 请求数据

    Returns:
        处理结果
    """
    # 1. 选择最佳GPU
    try:
        gpu_id = await unified_gpu_balancer.get_best_gpu(request_type)
    except HTTPException as e:
        logging.error(f"无法为{request_type}请求分配GPU: {e.detail}")
        raise e

    # 2. 获取GPU槽位
    if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, request_type):
        raise HTTPException(status_code=503, detail=f"GPU {gpu_id} {request_type}槽位获取失败")

    try:
        # 3. 执行实际请求
        logging.info(f"🚀 在GPU {gpu_id}上执行{request_type}请求")

        if request_type == "chat":
            # 这里调用实际的Chat处理逻辑
            server_url = f"http://localhost:{11434 - gpu_id}"
            result = await execute_chat_request(server_url, request_data)

        elif request_type == "embedding":
            # 这里调用实际的Embedding处理逻辑
            server_url = f"http://localhost:{11434 - gpu_id}"
            result = await execute_embedding_request(server_url, request_data)

        elif request_type == "ocr":
            # 这里调用实际的OCR处理逻辑
            result = await execute_ocr_request(gpu_id, request_data)

        else:
            raise ValueError(f"未知请求类型: {request_type}")

        logging.info(f"✅ GPU {gpu_id}上的{request_type}请求执行完成")
        return result

    except Exception as e:
        logging.error(f"❌ GPU {gpu_id}上的{request_type}请求执行失败: {str(e)}")
        raise e

    finally:
        # 4. 释放GPU槽位
        await unified_gpu_balancer.release_gpu_slot(gpu_id, request_type)

async def execute_chat_request(server_url: str, request_data: dict) -> dict:
    """执行Chat请求 - 调用实际的Chat处理逻辑"""
    try:
        # 构造标准的Chat请求格式
        messages = request_data.get("messages", [{"role": "user", "content": "Hello, this is a test message."}])
        model = request_data.get("model", "hngpt-mini:latest")

        chat_request = {
            "model": model,
            "messages": messages,
            "stream": False,
            "temperature": request_data.get("temperature", 0.7),
            "max_tokens": request_data.get("max_tokens", 1000)
        }

        # 调用Ollama API
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{server_url}/api/chat",
                json=chat_request
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "type": "chat",
                    "server_url": server_url,
                    "status": "completed",
                    "response": result.get("message", {}).get("content", ""),
                    "model": model
                }
            else:
                return {
                    "type": "chat",
                    "server_url": server_url,
                    "status": "error",
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

    except Exception as e:
        return {
            "type": "chat",
            "server_url": server_url,
            "status": "error",
            "error": str(e)
        }

async def execute_embedding_request(server_url: str, request_data: dict) -> dict:
    """执行Embedding请求 - 调用实际的Embedding处理逻辑"""
    try:
        # 构造标准的Embedding请求格式
        input_text = request_data.get("input", "This is a test embedding text.")
        model = request_data.get("model", "hngpt-embedding")

        embedding_request = {
            "model": model,
            "prompt": input_text
        }

        # 调用Ollama API
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{server_url}/api/embeddings",
                json=embedding_request
            )

            if response.status_code == 200:
                result = response.json()
                embeddings = result.get("embedding", [])
                return {
                    "type": "embedding",
                    "server_url": server_url,
                    "status": "completed",
                    "embedding_length": len(embeddings),
                    "model": model
                }
            else:
                return {
                    "type": "embedding",
                    "server_url": server_url,
                    "status": "error",
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

    except Exception as e:
        return {
            "type": "embedding",
            "server_url": server_url,
            "status": "error",
            "error": str(e)
        }

async def execute_ocr_request(gpu_id: int, request_data: dict) -> dict:
    """执行OCR请求 - 简化版本，避免复杂的图像处理"""
    try:
        # 为了测试负载均衡器，我们简化OCR处理
        # 实际使用时可以调用真正的OCR逻辑

        timeout = request_data.get("timeout", 30.0)
        enable_seal_hw = request_data.get("enable_seal_hw", False)

        # 模拟OCR处理时间
        await asyncio.sleep(0.1)  # 模拟处理延迟

        # 模拟OCR结果
        mock_result = {
            "text": "Test OCR result from GPU " + str(gpu_id),
            "confidence": 0.95,
            "processing_time": 0.1,
            "gpu_id": gpu_id,
            "settings": {
                "timeout": timeout,
                "enable_seal_hw": enable_seal_hw
            }
        }

        logging.info(f"✅ OCR请求在GPU {gpu_id}上模拟完成")

        return {
            "type": "ocr",
            "gpu_id": gpu_id,
            "status": "completed",
            "result": mock_result
        }

    except Exception as e:
        logging.error(f"OCR请求执行失败: {str(e)}")
        return {
            "type": "ocr",
            "gpu_id": gpu_id,
            "status": "error",
            "error": str(e)
        }

@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    csp_directives = [  
        "default-src 'self' 'unsafe-inline' 'unsafe-eval' *",  
        "img-src 'self' data: blob: * http://localhost:8889",  
        "style-src 'self' 'unsafe-inline' * http://localhost:8889",  
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' * http://localhost:8889",  
        "connect-src 'self' * http://localhost:8889",  
        "font-src 'self' data: * http://localhost:8889",  
        "frame-src 'self' * http://localhost:8889",  
        "object-src 'self' * http://localhost:8889"  
    ]
    # 将指令列表合并为单个字符串，确保使用分号和空格分隔
    csp_value = "; ".join(csp_directives)
    
    # 添加其他全头
    response.headers["Content-Security-Policy"] = csp_value
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    return response

app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义 Swagger UI 页面"""
    html = get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui/swagger-ui.css",
        swagger_favicon_url="/static/favicon.ico",
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,
            "deepLinking": True,
            "displayRequestDuration": True,
            "filter": True,
            "tryItOutEnabled": True,
            "persistAuthorization": True
        }
    )
    
    content = html.body.decode()
    
    # 添加自定义脚本
    custom_script = """
    <script>
        // 等待 Swagger UI 加载完成
        const interval = setInterval(() => {
            const authorizeBtn = document.querySelector('.swagger-ui .auth-wrapper button.authorize');
            if (authorizeBtn) {
                clearInterval(interval);
                
                // 创建 Supervisor 按钮
                const supervisorBtn = document.createElement('button');
                supervisorBtn.className = 'btn authorize';
                supervisorBtn.style.marginRight = '10px';
                supervisorBtn.onclick = () => {
                    const auth = localStorage.getItem('authorized');
                    if (auth) {
                        try {
                            const authData = JSON.parse(auth);
                            if (authData.HTTPBearer && authData.HTTPBearer.value === 'hngpt_admin@8888') {
                                // 直接在新窗口中打开，URL 中包含认证信息
                                window.open('/supervisor/?auth=' + encodeURIComponent('Bearer ' + authData.HTTPBearer.value), '_blank');
                            } else {
                                alert('Admin access required');
                            }
                        } catch (e) {
                            alert('Please authenticate first');
                        }
                    } else {
                        alert('Please authenticate first');
                    }
                };
                supervisorBtn.textContent = 'Open Supervisor';
                
                // 插入到 Authorize 按钮前面
                authorizeBtn.parentNode.insertBefore(supervisorBtn, authorizeBtn);
            }
        }, 100);
    </script>
    """
    
    content = content.replace('</body>', custom_script + '</body>')
    return HTMLResponse(content)

# 添加认证中转路由
@app.post("/supervisor/auth")
async def supervisor_auth(
    request: Request,
    response: Response
):
    form = await request.form()
    token = form.get('token')
    
    if token != 'hngpt_admin@8888':
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # 置认证 cookie
    response = RedirectResponse(url="/supervisor/")
    response.set_cookie(
        key="supervisor_auth",
        value=token,
        httponly=True,
        secure=False,  # 本地开为 False
        samesite='lax'
    )
    return response

# 修改 supervisor 代理由
@app.api_route("/supervisor/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def supervisor_proxy(
    request: Request, 
    path: str = "",
    auth: Optional[str] = Query(None)
):
    # 查认证
    token = None
    if auth:
        if auth.startswith('Bearer '):
            token = auth.split(' ')[1]
        else:
            token = auth
    
    if not token or token != "hngpt_admin@8888":
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # 构建 supervisor URL，移除查询参数中的 auth
    query_params = dict(request.query_params)
    query_params.pop('auth', None)
    
    # 处理文件参数
    file_name = query_params.pop('file', None)
    
    # 构建完整的 supervisor URL
    clean_path = path.strip('/')
    if file_name:
        supervisor_url = f"http://localhost:8889/{clean_path}/{file_name}"
    else:
        supervisor_url = f"http://localhost:8889/{clean_path}"
        if not supervisor_url.endswith('/'):
            supervisor_url += '/'
    
    logging.info(f"Proxying request to: {supervisor_url}")
    
    try:
        async with httpx.AsyncClient() as client:
            # 直接转发请求到 supervisor
            response = await client.request(
                method=request.method,
                url=supervisor_url,
                params=query_params,  # 使用剩余的查询参数
                headers={
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                },
                content=await request.body() if request.method != "GET" else None,
                follow_redirects=True
            )
            
            # 获取响应内容
            content = await response.aread()
            
            # 如果是 HTML 响应，修改资源路径和操作链接
            if response.headers.get('content-type', '').startswith('text/html'):
                content = content.decode('utf-8')
                
                # 修改资源路径，使用完的 supervisor URL
                content = content.replace('href="http://localhost:8889/', f'href="/supervisor/?auth={token}&path=')
                content = content.replace('src="http://localhost:8889/', f'src="/supervisor/?auth={token}&path=')
                
                # 修改相对路径
                content = content.replace('href="stylesheets/', f'href="/supervisor/stylesheets?auth={token}&file=')
                content = content.replace('src="images/', f'src="/supervisor/images?auth={token}&file=')
                
                # 修改操作链接
                content = content.replace('href="index.html?', f'href="/supervisor/?auth={token}&')
                content = content.replace('href="?', f'href="/supervisor/?auth={token}&')
                
                content = content.encode('utf-8')
            
            # 设置应头，不包含 content-length
            headers = {
                'Content-Type': response.headers.get('content-type', 'text/html'),
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
            
            # 根据文件类型设置确 content-type
            if supervisor_url.endswith('.css'):
                headers['Content-Type'] = 'text/css'
            elif supervisor_url.endswith('.js'):
                headers['Content-Type'] = 'application/javascript'
            elif supervisor_url.endswith('.png'):
                headers['Content-Type'] = 'image/png'
            elif supervisor_url.endswith('.gif'):
                headers['Content-Type'] = 'image/gif'
            
            # 使用 StreamingResponse 而不是 Response
            return StreamingResponse(
                content=iter([content]),
                status_code=response.status_code,
                headers=headers
            )
            
    except Exception as e:
        logging.error(f"Error proxying to supervisor: {str(e)}")
        raise HTTPException(status_code=502, detail=f"Failed to connect to supervisor: {str(e)}")

# 添加一个专门处理 /images 路径的路由
@app.get("/images/{file_path:path}")
async def proxy_supervisor_images(
    request: Request,
    file_path: str
):
    # 直接代 supervisor 的 images 径
    supervisor_url = f"http://localhost:8889/images/{file_path}"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                supervisor_url,
                headers={
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                },
                follow_redirects=True
            )
            
            # 设置正确的 content-type
            headers = {
                'Content-Type': 'image/gif' if file_path.endswith('.gif') else 'image/png',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
            
            return StreamingResponse(
                content=iter([await response.aread()]),
                status_code=response.status_code,
                headers=headers
            )
            
    except Exception as e:
        logging.error(f"Error proxying to supervisor images: {str(e)}")
        raise HTTPException(status_code=502, detail=f"Failed to connect to supervisor: {str(e)}")

@app.get("/docs/oauth2-redirect")
async def swagger_ui_redirect():
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>OAuth2 Redirect</title>
    </head>
    <body>
        <script>
            window.onload = function() {
                window.opener.swaggerUIRedirectOauth2(window.location.href);
                window.close();
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(html_content)


# 2. 添加首页路由
@app.get("/")
async def read_root():
    # 修改 index.html 中的资源路径
    with open("static/index.html", "r", encoding="utf-8") as f:
        content = f.read()
        # 替换所有资源引用路径，但要避免重复替换
        content = content.replace('href="', 'href="/static/')
        content = content.replace('src="', 'src="/static/')
        # 修复可能的双重 /static/ 问题
        content = content.replace('/static//static/', '/static/')
        # 不替换特定的 href
        content = content.replace('href="/static/http', 'href="http')
        return HTMLResponse(content)

# 3. 添加 favicon.ico 路由
@app.get("/favicon.ico")
async def get_favicon():
    return FileResponse("static/favicon.ico")

# 4. 添加SSE测试页面路由
@app.get("/test-sse")
async def test_sse_page():
    """返回SSE测试页面"""
    try:
        with open("static/test_sse.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>SSE test page not found</h1>", status_code=404)

# 添加日志中间件
app.add_middleware(LoggerMiddleware)

async def get_authorized_users(filename):
    try:
        with open(filename, 'r') as f:
            lines = f.readlines()
        authorized_users = {}
        for line in lines:
            line = line.strip()
            if line:  # 跳过空行
                try:
                    user, key = line.split(':')
                    authorized_users[key.strip()] = user.strip()
                except ValueError:
                    logging.warning(f"Skipping invalid line in users file: {line}")
        return authorized_users
    except Exception as e:
        logging.error(f"Error reading authorized users: {e}")
        return {}



# 添 OpenAI 兼容的模型
class OpenAIEmbeddingRequest(BaseModel):
    """OpenAI 格式的 Embedding 请求"""
    model: str
    input: Union[str, List[str]]  # 可以是单个字符串字符串列表
    encoding_format: Optional[str] = "float"
    user: Optional[str] = None

# 标准格式的 Embedding 请求
class EmbeddingRequest(BaseModel):
    """标准格式的 Embedding 请求"""
    model: str
    prompt: str
    encoding_format: Optional[str] = "float"
    user: Optional[str] = None


# 统一的请求模型
class UnifiedChatRequest(BaseModel):
    # OpenAI 格式字段
    model: str
    messages: Optional[List[Dict[str, str]]] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 2000
    stream: Optional[bool] = False
    
    # 传统格式字段
    prompt: Optional[str] = None
    context: Optional[str] = None
    system: Optional[str] = None
    
    try:
        # pydantic v2
        @validator('messages', 'prompt')
        def validate_message_or_prompt(cls, v, values):
            if not v and 'prompt' not in values and 'messages' not in values:
                raise ValueError("Either 'messages' or 'prompt' must be provided")
            return v
    except NameError:
        # pydantic v1
        @validator('messages', 'prompt')
        def validate_message_or_prompt(cls, v, values):
            if not v and 'prompt' not in values and 'messages' not in values:
                raise ValueError("Either 'messages' or 'prompt' must be provided")
            return v

# OpenAI 格式的请求模型
class ChatMessage(BaseModel):
    role: str
    content: str
    name: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    stream: Optional[bool] = False
    max_tokens: Optional[int] = None

# OpenAI 格式的响应模
class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Optional[str] = None

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{int(time.time()*1000)}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int]

# Add this new retry function for non-streaming requests
async def retry_with_backoff(func, max_retries=3, initial_delay=1):
    """
    Regular request retry decorator
    """
    last_error = None
    for attempt in range(max_retries):
        try:
            return await func()
        except httpx.ConnectError as e:
            last_error = e
            if attempt == max_retries - 1:
                break
            delay = initial_delay * (2 ** attempt)
            logging.warning(f"Connection failed (attempt {attempt + 1}/{max_retries}), retrying in {delay}s...")
            await asyncio.sleep(delay)
            continue
        except Exception as e:
            logging.error(f"Unexpected error in retry_with_backoff: {str(e)}")
            raise

    if last_error:
        raise last_error

# Update the embeddings endpoint to use the new retry function
@app.post("/v1/embeddings")
@app.post("/api/embeddings")
async def create_embeddings(
    request: Union[EmbeddingRequest, OpenAIEmbeddingRequest],
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    server_name = None
    server_status = None
    
    try:
        # 使用统一GPU负载均衡器选择最佳GPU
        gpu_id = await unified_gpu_balancer.get_best_gpu("embedding")
        server_url = f"http://localhost:{11434 - gpu_id}"

        # 获取GPU槽位
        if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "embedding"):
            raise HTTPException(status_code=503, detail="所有GPU的embedding槽位都已满")

        logging.info(f"🎯 Embedding请求分配到GPU {gpu_id} (服务器: {server_url})")

        # 为了兼容性，仍然获取旧的服务器状态
        server_name, server_status = await get_best_server("embeddings")

        # 准备任务数据
        if isinstance(request, OpenAIEmbeddingRequest):
            input_text = request.input[0] if isinstance(request.input, list) else request.input
        else:
            input_text = request.prompt

        # 根据模型类型准备文本
        prepared_text = prepare_embedding_text(input_text, request.model)

        embedding_request = {
            "model": "hngpt-embedding",  # 统一使用hngpt-embedding模型
            "prompt": prepared_text
        }



        # 直接处理Embedding请求（不使用任务管理器）
        logging.debug(f"🔤 开始处理Embedding请求，模型: {request.model}")

        # 更新服务器队列和处理计数
        server_status.embeddings_queue.put_nowait(1)
        server_status.embeddings_processing += 1

        try:
            # 发送请求到选定的GPU服务器
            async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:
                response = await client.post(
                    f"{server_url}/api/embeddings",
                    json=embedding_request,
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()
                ollama_response = response.json()

            # 获取原始embedding
            raw_embedding = ollama_response.get("embedding", [])

            # hngpt-embedding模型需要归一化
            if raw_embedding:
                processed_embedding = normalize_embedding(raw_embedding)
            else:
                processed_embedding = raw_embedding

            # 构造返回结果，使用原始请求的模型名称
            result = {
                "object": "list",
                "data": [
                    {
                        "object": "embedding",
                        "embedding": processed_embedding,
                        "index": 0
                    }
                ],
                "model": request.model,  # 返回用户请求的模型名称
                "usage": {
                    "prompt_tokens": len(input_text.split()),
                    "total_tokens": len(input_text.split())
                }
            }

            # 记录成功
            server_status.record_success("embeddings")
            logging.debug(f"✅ Embedding请求处理成功")
            return result

        finally:
            # 清理队列和处理计数
            try:
                server_status.embeddings_queue.get_nowait()
                server_status.embeddings_processing -= 1
            except:
                pass


            
    except Exception as e:
        logging.error(f"Embeddings 处理错误: {str(e)}")
        # 记录错误
        if server_status:
            server_status.record_error("embeddings")
            
        # 确保在错误情况下也清理队列和更新计数
        try:
            if server_status:
                server_status.embeddings_queue.get_nowait()
                server_status.embeddings_processing -= 1
                queue_status = get_queue_status_bar(
                    server_name, 
                    server_status.embeddings_queue.qsize(), 
                    server_status.embeddings_processing, 
                    server_status.max_queue_size, 
                    "embeddings"
                )
                logging.debug(f"队列状态: {queue_status}")
        except Exception as cleanup_error:
            logging.error(f"清理嵌入队列时出错: {str(cleanup_error)}")

        # 释放GPU槽位
        if 'gpu_id' in locals():
            await unified_gpu_balancer.release_gpu_slot(gpu_id, "embedding")

        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 释放GPU槽位
        if 'gpu_id' in locals():
            await unified_gpu_balancer.release_gpu_slot(gpu_id, "embedding")

        # 完成后更新计数
        try:
            if server_status:
                server_status.embeddings_queue.get_nowait()
                server_status.embeddings_processing -= 1

                # GPU任务计数由统一任务管理器处理
                queue_status = get_queue_status_bar(
                    server_name,
                    server_status.embeddings_queue.qsize(),
                    server_status.embeddings_processing,
                    server_status.max_queue_size,
                    "embeddings"
                )
                logging.debug(f"队列状态: {queue_status}")
        except Exception as e:
            # 如果队列已经在try块或错误处理中被清理，这里会抛出异常，可以忽略
            pass


# 统一的聊天接口 - 完全使用新的GPU负载均衡器
@app.post("/v1/chat/completions")
async def unified_chat_completion(
    request: Union[UnifiedChatRequest, ChatCompletionRequest],
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """使用统一GPU负载均衡器的Chat接口"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 使用统一GPU负载均衡器选择最佳GPU
    try:
        gpu_id = await unified_gpu_balancer.get_best_gpu("chat")
        server_url = f"http://localhost:{11434 - gpu_id}"

        # 获取GPU槽位
        if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "chat"):
            raise HTTPException(status_code=503, detail="所有GPU的chat槽位都已满")

        logging.info(f"🎯 Chat请求分配到GPU {gpu_id} (服务器: {server_url})")

        try:
            # 准备消息
            messages = []

            if isinstance(request, ChatCompletionRequest):
                messages = [msg.model_dump() for msg in request.messages]
            else:
                if request.system:
                    messages.append({"role": "system", "content": request.system})
                if request.messages:
                    messages.extend(request.messages)
                elif request.prompt:
                    messages.append({"role": "user", "content": request.prompt})

            # 构建Ollama请求
            ollama_request = {
                "model": request.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens or 2000
                }
            }

            start_time = time.time()

            # 发送请求到Ollama
            async with httpx.AsyncClient(timeout=httpx.Timeout(120.0)) as client:
                response = await client.post(
                    f"{server_url}/api/chat",
                    json=ollama_request,
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()
                result = response.json()

            # 记录响应时间
            response_time = time.time() - start_time
            logging.info(f"Path: /v1/chat/completions Method: POST GPU: {gpu_id} Time: {response_time:.3f}s")

            # 转换Ollama响应格式为OpenAI格式
            return ChatCompletionResponse(
                model=request.model,
                choices=[
                    ChatCompletionChoice(
                        index=0,
                        message=ChatMessage(
                            role="assistant",
                            content=result.get("message", {}).get("content", "")
                        ),
                        finish_reason=result.get("done_reason", "stop")
                    )
                ],
                usage={
                    "prompt_tokens": 0,  # Ollama不提供token计数
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            )

        except Exception as e:
            logging.error(f"Chat completion error on GPU {gpu_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Chat completion failed: {str(e)}")

        finally:
            # 释放GPU槽位
            await unified_gpu_balancer.release_gpu_slot(gpu_id, "chat")

    except HTTPException as e:
        raise e
    except Exception as e:
        logging.error(f"GPU负载均衡器选择失败: {str(e)}")
        raise HTTPException(status_code=503, detail="GPU负载均衡器选择失败")





# 为了保持向后兼容性，添加重定向路由
@app.post("/api/generate")
@app.post("/api/chat")
async def legacy_chat_endpoints(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """
    重定向旧的接口到新的统一接口
    """
    try:
        # 获取原始请求数据
        raw_data = await request.json()
        
        # 转换为 UnifiedChatRequest 格式
        chat_request = UnifiedChatRequest(
            model=raw_data.get("model", "hngpt"),
            messages=[
                {"role": "system", "content": raw_data.get("system", "")},
                {"role": "user", "content": raw_data.get("prompt", "")}
            ] if raw_data.get("system") or raw_data.get("prompt") else None,
            temperature=raw_data.get("temperature", 0.7),
            max_tokens=raw_data.get("max_tokens", 2000),
            stream=raw_data.get("stream", True),
            prompt=raw_data.get("prompt"),
            context=raw_data.get("context"),
            system=raw_data.get("system")
        )
        
        # 调用统一接口
        return await unified_chat_completion(
            request=chat_request,
            credentials=credentials
        )
    except Exception as e:
        logging.error(f"Error in legacy endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/monitor")
async def gpu_monitor():
    """GPU监控页面"""
    import os
    file_path = "static/gpu-monitor.html"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"监控页面文件不存在: {file_path}")
    return FileResponse(file_path)

# 任务状态查询API
@app.get("/api/task/{task_id}/status")
async def get_task_status(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """查询任务状态"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 从统一任务管理器获取任务状态
    task_info = unified_task_manager.get_task_status(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    return task_info

# 测试新负载均衡器的API端点
@app.post("/api/test/unified-balancer")
async def test_unified_balancer(
    request_type: str = Query(..., description="请求类型: chat, embedding, ocr"),
    request_data: dict = Body(default={}),
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """测试统一GPU负载均衡器"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    if request_type not in ["chat", "embedding", "ocr"]:
        raise HTTPException(status_code=400, detail="无效的请求类型")

    try:
        # 如果没有提供请求数据，使用默认测试数据
        if not request_data:
            if request_type == "chat":
                request_data = {
                    "messages": [{"role": "user", "content": "Hello, this is a test message."}],
                    "model": "hngpt-mini:latest"
                }
            elif request_type == "embedding":
                request_data = {
                    "input": "This is a test text for embedding.",
                    "model": "hngpt-embedding"
                }
            elif request_type == "ocr":
                request_data = {
                    "timeout": 30.0,
                    "enable_seal_hw": False
                }

        # 使用新的负载均衡器处理请求
        result = await process_request_with_unified_balancer(
            request_type=request_type,
            request_data=request_data
        )

        return {
            "status": "success",
            "request_type": request_type,
            "result": result,
            "balancer_status": unified_gpu_balancer.get_gpu_status()
        }

    except Exception as e:
        logging.error(f"测试统一负载均衡器失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "request_type": request_type
        }

# 新的统一负载均衡器状态API
@app.get("/api/gpu/status")
async def get_unified_gpu_status():
    """获取统一GPU负载均衡器状态 - 无需认证"""
    try:
        status = unified_gpu_balancer.get_gpu_status()
        return {
            "status": "success",
            "timestamp": time.time(),
            "balancer": "UnifiedGPULoadBalancer",
            "data": status
        }
    except Exception as e:
        logging.error(f"获取统一GPU状态失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/api/gpu/reset")
async def reset_gpu_status(credentials: HTTPAuthorizationCredentials = Depends(bearer)):
    """重置GPU状态 - 清理所有槽位"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        # 重置统一GPU负载均衡器状态
        for gpu_id in range(unified_gpu_balancer.gpu_count):
            for task_type in unified_gpu_balancer.concurrent_limits.keys():
                unified_gpu_balancer.current_loads[gpu_id][task_type] = 0

        logging.info("🔧 GPU状态已重置，所有槽位已清理")

        return {
            "status": "success",
            "message": "GPU状态已重置",
            "data": {
                "gpu_count": unified_gpu_balancer.gpu_count,
                "concurrent_limits": unified_gpu_balancer.concurrent_limits,
                "current_loads": unified_gpu_balancer.current_loads
            },
            "timestamp": time.time()
        }
    except Exception as e:
        logging.error(f"重置GPU状态失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        }

@app.get("/monitor/api/status")
async def monitor_status():
    """监控页面专用的状态API - 无需认证"""
    try:
        # GPU状态信息 - 使用nvidia-smi获取详细信息
        gpu_info = []
        for gpu_id in range(gpu_manager.gpu_count):
            try:
                # 使用nvidia-smi获取GPU详细信息
                gpu_data = get_gpu_info_nvidia_smi(gpu_id)
                status = gpu_manager.gpu_status[gpu_id]

                # 添加任务信息
                gpu_data.update({
                    "gpu_id": gpu_id,
                    "allocated_memory_mb": gpu_data['memory_used_mb'],
                    "reserved_memory_mb": gpu_data['memory_total_mb'],
                    "free_memory_mb": gpu_data['memory_free_mb'],
                    "memory_usage_percent": (gpu_data['memory_used_mb'] / max(gpu_data['memory_total_mb'], 1)) * 100,
                    "utilization": f"{gpu_data['utilization_percent']:.1f}%",
                    "llm_chat_tasks": status.get('llm_chat_tasks', 0),
                    "llm_embedding_tasks": status.get('llm_embedding_tasks', 0),
                    "ocr_tasks": status.get('ocr_tasks', 0),
                    "layout_tasks": status.get('layout_tasks', 0)
                })
                gpu_info.append(gpu_data)
            except Exception as e:
                gpu_info.append({
                    "gpu_id": gpu_id,
                    "name": "Unknown",
                    "error": str(e),
                    "allocated_memory_mb": 0,
                    "reserved_memory_mb": 0,
                    "memory_total_mb": 0,
                    "memory_used_mb": 0,
                    "memory_free_mb": 0,
                    "utilization_percent": 0,
                    "temperature_c": 0,
                    "power_draw_w": 0,
                    "llm_chat_tasks": 0,
                    "llm_embedding_tasks": 0,
                    "ocr_tasks": 0,
                    "layout_tasks": 0
                })

        # 使用统一任务管理器的统计信息
        unified_stats = unified_task_manager.get_task_stats()

        # 保持向后兼容的任务统计
        task_stats = {
            "total_tasks": unified_stats["total_tasks"],
            "processing_tasks": unified_stats["processing_tasks"],
            "completed_tasks": unified_stats["completed_tasks"],
            "failed_tasks": unified_stats["failed_tasks"]
        }

        # Ollama服务器状态
        ollama_servers = []
        for name, server in SERVERS_CONFIG.items():
            ollama_servers.append({
                "name": name,
                "url": server.url,
                "healthy": True,  # 简化：总是显示为健康
                "chat_healthy": True,
                "embeddings_healthy": True
            })

        return {
            "status": "running",
            "timestamp": time.time(),
            "gpu_info": gpu_info,
            "task_stats": task_stats,
            "ollama_servers": ollama_servers,
            "total_servers": len(SERVERS_CONFIG),
            "healthy_servers": len(SERVERS_CONFIG),  # 简化：所有服务器都视为健康
            "total_gpus": len(GPU_QUEUES),
            "busy_gpus": sum(1 for q in GPU_QUEUES.values() if q.qsize() > 0)
        }
    except Exception as e:
        logging.error(f"监控状态API错误: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

@app.get("/monitor/api/queue")
async def monitor_queue_stats():
    """监控页面专用的队列统计API - 无需认证"""
    try:
        return task_router.get_route_stats()
    except Exception as e:
        logging.error(f"监控队列API错误: {str(e)}")
        return {
            "error": str(e),
            "total_tasks": 0,
            "queue_size": 0,
            "gpu_status": {}
        }

@app.get("/ocr/status/{task_id}")
async def get_task_status(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """查询任务状态"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 检查任务队列结果
    if task_id in task_router.task_results:
        result = task_router.task_results[task_id]
        return {
            "task_id": task_id,
            "status": result['status'],
            "progress": result.get('progress', 100),
            "result": result.get('result'),
            "error": result.get('error'),
            "gpu_id": result.get('gpu_id')
        }

    # 检查传统任务结果（向后兼容）
    if task_id in task_results:
        result = task_results[task_id]
        return {
            "task_id": task_id,
            "status": result.status,
            "progress": result.progress,
            "result": result.result,
            "error": result.error
        }

    # 任务不存在
    raise HTTPException(status_code=404, detail="Task not found")

@app.get("/queue/stats")
async def get_queue_stats(
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """获取任务队列统计信息"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    return task_router.get_route_stats()

# 优化：添加系统状态监控端点
@app.get("/api/status")
async def system_status(credentials: HTTPAuthorizationCredentials = Depends(bearer)):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 服务器状态
    servers_status = {}
    for name, server in SERVERS_CONFIG.items():
        servers_status[name] = {
            "url": server.url,
            "is_healthy": True,  # 简化：总是显示为健康
            "chat_healthy": True,
            "embeddings_healthy": True,
            "chat_processing": server.chat_processing,
            "embeddings_processing": server.embeddings_processing,
            "chat_queue_size": server.chat_queue.qsize(),
            "embeddings_queue_size": server.embeddings_queue.qsize(),
            "max_queue_size": server.max_queue_size,
            "max_concurrent_chat": server.max_concurrent_chat,
            "max_concurrent_embeddings": server.max_concurrent_embeddings,
            "avg_response_time": round(server.avg_response_time, 3),
            "total_requests": server.total_requests,
            "chat_errors": server.chat_errors,
            "embeddings_errors": server.embeddings_errors,
            "load_score_chat": round(server.get_load_score("chat"), 3),
            "load_score_embeddings": round(server.get_load_score("embeddings"), 3)
        }

    # GPU状态 - 优化为监控页面格式
    gpu_info = []
    for gpu_id in range(gpu_manager.gpu_count):
        try:
            allocated, reserved = get_gpu_memory_info(gpu_id)
            status = gpu_manager.gpu_status[gpu_id]

            gpu_data = {
                "gpu_id": gpu_id,
                "name": torch.cuda.get_device_name(gpu_id) if torch.cuda.is_available() else "Unknown",
                "allocated_memory_mb": allocated,
                "reserved_memory_mb": reserved,
                "free_memory_mb": reserved - allocated,
                "memory_usage_percent": (allocated / max(reserved, 1)) * 100,
                "utilization": f"{(allocated / max(reserved, 1)) * 100:.1f}%" if reserved > 0 else "0%",
                "llm_chat_tasks": status.get('llm_chat_tasks', 0),
                "llm_embedding_tasks": status.get('llm_embedding_tasks', 0),
                "ocr_tasks": status.get('ocr_tasks', 0),
                "layout_tasks": status.get('layout_tasks', 0)
            }
            gpu_info.append(gpu_data)
        except Exception as e:
            gpu_info.append({
                "gpu_id": gpu_id,
                "name": "Unknown",
                "error": str(e),
                "allocated_memory_mb": 0,
                "reserved_memory_mb": 0,
                "llm_tasks": 0,
                "ocr_tasks": 0,
                "layout_tasks": 0
            })

    # 任务统计
    task_stats = {
        "total_tasks": len(task_results),
        "processing_tasks": len([t for t in task_results.values() if t.status == "processing"]),
        "completed_tasks": len([t for t in task_results.values() if t.status == "completed"]),
        "failed_tasks": len([t for t in task_results.values() if t.status == "failed"])
    }

    # Ollama服务器状态
    ollama_servers = []
    for name, server in SERVERS_CONFIG.items():
        ollama_servers.append({
            "name": name,
            "url": server.url,
            "healthy": True,  # 简化：总是显示为健康
            "chat_healthy": True,
            "embeddings_healthy": True
        })

    return {
        "status": "running",
        "timestamp": time.time(),
        "gpu_info": gpu_info,
        "task_stats": task_stats,
        "ollama_servers": ollama_servers,
        "servers": servers_status,
        "total_servers": len(SERVERS_CONFIG),
        "healthy_servers": len(SERVERS_CONFIG),  # 简化：所有服务器都视为健康
        "total_gpus": len(GPU_QUEUES),
        "busy_gpus": sum(1 for q in GPU_QUEUES.values() if q.qsize() > 0)
    }

# 添加重新加载用户列表的端点
@app.post("/api/admin/reload-users")
async def reload_users(
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token != "admin_key":
        raise HTTPException(
            status_code=403,
            detail="Only admin can reload users"
        )
    
    try:
        global authorized_users
        authorized_users = await get_authorized_users("authorized_users.txt")
        logging.info("User list reloaded successfully")
        return {"status": "success", "message": "User list reloaded"}
    except Exception as e:
        logging.error(f"Failed to reload users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 修改 Base64Request 模型增加文件名和页码字段
class Base64Request(BaseModel):
    """Base64 OCR 请求模型"""
    image: str
    file_name: Optional[str] = ""
    page_no: Optional[int] = 1
    wait: bool = False
    timeout: float = 30.0
    test_mode: Optional[bool] = False
    enable_seal_hw: bool = False

    try:
        # pydantic v2
        @validator('timeout')
        def validate_timeout(cls, v):
            if v <= 0:
                raise ValueError("Timeout must be positive")
            return v
    except NameError:
        # pydantic v1
        @validator('timeout')
        def validate_timeout(cls, v):
            if v <= 0:
                raise ValueError("Timeout must be positive")
            return v

# 添加日期提取函数
def extract_dates(text: str) -> List[str]:
    """从文本中提取日期"""
    # 匹配常见的日期格式
    date_patterns = [
        r'(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})[日]?',  # 2019年7月1日 或 2019-07-01
        r'(\d{4})\.(\d{1,2})\.(\d{1,2})',              # 2019.7.1
    ]
    
    dates = []
    for pattern in date_patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            year, month, day = match.groups()
            # 格式化日期
            try:
                date_str = f"{int(year):04d}-{int(month):02d}-{int(day):02d} 00:00:00"
                dates.append(date_str)
            except ValueError:
                continue
    
    return sorted(list(set(dates)))  # 去重并排序

# 统一任务结果类
@dataclass
class TaskResult:
    """统一任务结果"""
    task_id: str
    task_type: str  # 'llm', 'embedding', 'ocr'
    status: str = "processing"  # 'processing', 'completed', 'failed', 'timeout'
    result: Optional[dict] = None
    error: Optional[str] = None
    progress: int = 0
    created_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    gpu_id: Optional[int] = None

# 旧的UnifiedTaskManager已整合到UnifiedServerManager中



# ⚠️  架构问题：名不副实，只处理OCR但被当作"统一任务管理器"
# TODO: 要么真正统一所有任务管理，要么改名为OCRTaskManager
class SimpleTaskManager:
    """
    简化的任务管理器 - 名不副实的问题类

    问题：
    - 名字叫"统一任务管理器"但只处理OCR任务
    - 与UnifiedServerManager的任务管理功能重叠
    - 职责不清晰，容易误导

    建议：
    - 改名为OCRTaskManager更准确
    - 或者真正统一所有任务管理
    - 避免与UnifiedServerManager功能重叠
    """

    def __init__(self):
        self.task_results: Dict[str, TaskResult] = {}
        self.lock = asyncio.Lock()

    def generate_task_id(self, task_type: str) -> str:
        """生成唯一的任务ID"""
        import uuid
        return f"{task_type}_{uuid.uuid4().hex[:8]}"

    async def create_task(self, task_type: str, task_data: dict, gpu_id: Optional[int] = None) -> str:
        """创建新任务并返回task_id"""
        async with self.lock:
            task_id = self.generate_task_id(task_type)

            # 创建任务结果对象
            task_result = TaskResult(
                task_id=task_id,
                task_type=task_type,
                status="processing",
                progress=0,
                gpu_id=gpu_id
            )
            self.task_results[task_id] = task_result

            logging.info(f"📝 创建任务 {task_id} [{task_type}]")

            # 实际启动任务执行
            if task_type == "ocr":
                # 创建一个假的asyncio.Task对象用于兼容性
                fake_task = asyncio.create_task(self._execute_ocr_task(task_id, task_data))
                # 更新全局ocr_tasks字典以支持结果查询
                global ocr_tasks
                ocr_tasks[task_id] = fake_task
                logging.info(f"📝 已将任务 {task_id} 添加到全局ocr_tasks")
            elif task_type == "llm":
                # LLM任务由UnifiedServerManager处理，这里只记录
                logging.info(f"📝 LLM任务 {task_id} 已创建，由UnifiedServerManager处理")
            elif task_type == "embedding":
                # Embedding任务由UnifiedServerManager处理，这里只记录
                logging.info(f"📝 Embedding任务 {task_id} 已创建，由UnifiedServerManager处理")
            else:
                logging.warning(f"未知任务类型: {task_type}")

            return task_id

    async def get_task_result(self, task_id: str, timeout: float = 30.0) -> Optional[TaskResult]:
        """获取任务结果，支持超时等待"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if task_id in self.task_results:
                result = self.task_results[task_id]
                if result.status in ["completed", "failed", "timeout"]:
                    return result
            await asyncio.sleep(0.1)

        # 超时处理
        if task_id in self.task_results:
            self.task_results[task_id].status = "timeout"
            self.task_results[task_id].error = f"任务执行超时 ({timeout}s)"
            self.task_results[task_id].progress = 100
            return self.task_results[task_id]

        return None

    async def _execute_ocr_task(self, task_id: str, task_data: dict):
        """执行OCR任务"""
        # 声明全局变量
        global task_results
        try:
            # 更新任务状态
            if task_id in self.task_results:
                self.task_results[task_id].status = "processing"
                self.task_results[task_id].progress = 10

            # 获取任务数据
            image = task_data['image']
            timeout = task_data.get('timeout', 30.0)
            enable_seal_hw = task_data.get('enable_seal_hw', False)

            logging.info(f"Starting OCR processing for task {task_id}, image shape: {image.shape}")

            # 直接调用现有的OCR处理函数
            await process_ocr_background(image, task_id)

            # process_ocr_background会更新全局task_results，我们需要从那里获取结果
            if task_id in task_results:
                global_result = task_results[task_id]
                if global_result.status == "completed":
                    # 将全局结果复制到SimpleTaskManager的结果中
                    if task_id in self.task_results:
                        self.task_results[task_id].status = "completed"
                        self.task_results[task_id].progress = 100
                        self.task_results[task_id].result = global_result.result
                        self.task_results[task_id].completed_at = time.time()

                        # 同时确保全局task_results也有正确的结果
                        task_results[task_id] = self.task_results[task_id]
                else:
                    # 如果全局结果显示失败，也要更新本地结果
                    if task_id in self.task_results:
                        self.task_results[task_id].status = global_result.status
                        self.task_results[task_id].progress = global_result.progress
                        self.task_results[task_id].error = global_result.error
                        self.task_results[task_id].completed_at = time.time()

                        # 同时确保全局task_results也有正确的结果
                        task_results[task_id] = self.task_results[task_id]

            logging.info(f"✅ OCR任务 {task_id} 完成")

        except Exception as e:
            logging.error(f"❌ OCR任务 {task_id} 失败: {e}")
            if task_id in self.task_results:
                self.task_results[task_id].status = "failed"
                self.task_results[task_id].progress = 100
                self.task_results[task_id].error = str(e)
                self.task_results[task_id].completed_at = time.time()

                # 同时更新全局task_results
                task_results[task_id] = self.task_results[task_id]

    def get_task_status(self, task_id: str) -> Optional[dict]:
        """获取单个任务的状态信息"""
        if task_id not in self.task_results:
            return None

        task_result = self.task_results[task_id]
        return {
            "task_id": task_result.task_id,
            "task_type": task_result.task_type,
            "status": task_result.status,
            "progress": task_result.progress,
            "created_at": task_result.created_at,
            "completed_at": task_result.completed_at,
            "gpu_id": task_result.gpu_id,
            "result": task_result.result,
            "error": task_result.error
        }

    def get_task_stats(self) -> dict:
        """获取任务统计信息"""
        stats = {
            "total_tasks": len(self.task_results),
            "processing_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "task_types": {"llm": 0, "embedding": 0, "ocr": 0}
        }

        for task_result in self.task_results.values():
            if task_result.status == "processing":
                stats["processing_tasks"] += 1
                stats["task_types"][task_result.task_type] += 1
            elif task_result.status == "completed":
                stats["completed_tasks"] += 1
            elif task_result.status == "failed":
                stats["failed_tasks"] += 1

        return stats

# 创建全局任务管理器实例
unified_task_manager = SimpleTaskManager()

# 保持向后兼容的全局变量
ocr_tasks: Dict[str, asyncio.Task] = {}
task_results: Dict[str, TaskResult] = {}  # 修改这里不使用 defaultdict

# 添加一个资源锁
ocr_lock = Lock()


# 修改后台 OCR 处理函数
async def process_ocr_background(image: np.ndarray, task_id: str):
    """后台执行 OCR 处理，使用版面分析和OCR进行综合文档处理"""
    try:
        logging.info(f"Starting OCR processing for task {task_id}, image shape: {image.shape}")
        task_results[task_id] = TaskResult(
            task_id=task_id,
            task_type="ocr",
            status="processing",
            progress=10
        )
        
        # 使用GPU资源管理器选择最佳GPU
        gpu_id = gpu_manager.get_best_gpu_for_task("ocr")

        # 尝试获取GPU资源
        if not gpu_manager.acquire_gpu(gpu_id, "ocr"):
            # 如果获取失败，尝试其他GPU
            for fallback_gpu in range(gpu_manager.gpu_count):
                if gpu_manager.acquire_gpu(fallback_gpu, "ocr"):
                    gpu_id = fallback_gpu
                    break
            else:
                # 所有GPU都不可用
                task_results[task_id] = TaskResult(
                    task_id=task_id,
                    task_type="ocr",
                    status="failed",
                    error="所有GPU资源都不可用",
                    progress=100
                )
                return

        logging.info(f"🎯 为任务 {task_id} 分配GPU {gpu_id}")

        # 将任务加入GPU队列（保持向后兼容）
        if gpu_id in GPU_QUEUES:
            GPU_QUEUES[gpu_id].put_nowait(1)
        
        try:
            with GPU_LOCKS[gpu_id]:
                torch.cuda.set_device(gpu_id)
                torch.cuda.empty_cache()
                
                allocated, reserved = get_gpu_memory_info(gpu_id)
                logging.info(f"GPU {gpu_id} initial memory - Allocated: {allocated:.2f}MB, Reserved: {reserved:.2f}MB")
                
                # 使用版面分析和OCR进行综合文档处理
                try:
                    # 设置当前任务的GPU上下文，但不修改全局环境变量
                    torch.cuda.set_device(gpu_id)

                    # 转换为RGB格式用于模型推理
                    img_array = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    task_results[task_id] = TaskResult(
                        task_id=task_id,
                        task_type="ocr",
                        status="processing",
                        progress=20
                    )

                    # 步骤1: 版面分析 (获取Layout资源)
                    if not gpu_manager.acquire_gpu(gpu_id, "layout"):
                        logging.warning(f"GPU {gpu_id} Layout资源获取失败，跳过版面分析")
                        layout_results = []
                    else:
                        try:
                            logging.info("执行版面分析...")
                            layout_results = rec.rapid_layout_with_params(img_array, gpu_id=gpu_id)
                            logging.info(f"检测到 {len(layout_results)} 个版面区域")
                        finally:
                            gpu_manager.release_gpu(gpu_id, "layout")

                    task_results[task_id] = TaskResult(
                        task_id=task_id,
                        task_type="ocr",
                        status="processing",
                        progress=40
                    )

                    # 步骤2: 整页OCR识别 (使用已获取的OCR资源)
                    logging.info("执行整页OCR识别...")
                    ocr_results = rec.rapid_ocr_with_params(img_array, gpu_id=gpu_id, version='v5')
                    logging.info(f"识别到 {len(ocr_results) if ocr_results else 0} 个文本区域")
                    task_results[task_id] = TaskResult(
                        task_id=task_id,
                        task_type="ocr",
                        status="processing",
                        progress=60
                    )

                    # 步骤3: 提取页面内容
                    logging.info("提取页面内容...")
                    result = extract_page_content_by_layout(layout_results, ocr_results, image.shape)
                    task_results[task_id] = TaskResult(
                        task_id=task_id,
                        task_type="ocr",
                        status="processing",
                        progress=80
                    )

                    logging.info(f"页面内容长度: {len(result['pageContent'])} 字符")
                    logging.info(f"印章内容: {result['sealContent']}")
                    logging.info(f"手写内容: {result['handWriteContent']}")
                    logging.info(f"日期内容: {result['dateContent']}")

                except Exception as e:
                    logging.error(f"Document processing error on GPU {gpu_id}: {str(e)}")
                    raise
                
                task_results[task_id] = TaskResult(
                    task_id=task_id,
                    task_type="ocr",
                    status="completed",
                    result=result,
                    progress=100
                )
                
                logging.info(f"OCR completed for task {task_id}")
                
        finally:
            # 释放GPU资源
            gpu_manager.release_gpu(gpu_id, "ocr")

            # 释放队列资源（保持向后兼容）
            try:
                if gpu_id in GPU_QUEUES:
                    GPU_QUEUES[gpu_id].get_nowait()
                logging.info(f"✅ 任务 {task_id} GPU {gpu_id} 资源释放完成")
            except Exception as e:
                logging.error(f"❌ 释放GPU {gpu_id} 队列资源失败: {str(e)}")

            # 记录最终内存状态
            try:
                allocated, reserved = get_gpu_memory_info(gpu_id)
                logging.info(f"📊 GPU {gpu_id} 最终内存状态 - 已分配: {allocated:.1f}MB, 已保留: {reserved:.1f}MB")
            except Exception as e:
                logging.debug(f"获取GPU {gpu_id} 内存信息失败: {str(e)}")
            
    except Exception as e:
        error_msg = str(e)
        logging.error(f"❌ OCR任务 {task_id} 失败: {error_msg}")

        # 根据错误类型提供更详细的错误信息
        if "CUDA" in error_msg or "GPU" in error_msg:
            error_msg = f"GPU资源错误: {error_msg}"
        elif "memory" in error_msg.lower() or "out of memory" in error_msg.lower():
            error_msg = f"内存不足: {error_msg}"
        elif "timeout" in error_msg.lower():
            error_msg = f"处理超时: {error_msg}"

        task_results[task_id] = TaskResult(
            task_id=task_id,
            task_type="ocr",
            status="failed",
            error=error_msg,
            progress=100
        )

# 修改 OCR 文件上传接口
@app.post("/ocr/file/")
async def ocr_file(
    file: UploadFile,
    wait: str = Form(default="false", pattern="^(true|false)$"),
    timeout: str = Form(default="30.0", pattern="^(?:[0-9]*[.])?[0-9]+$"),
    enable_seal_hw: str = Form(default="false", pattern="^(true|false)$"),
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        logging.info(f"[OCR] Processing file upload: {file.filename}")
        if not file.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            raise HTTPException(status_code=400, detail="Unsupported file format")
        
        # 验证和转换参
        try:
            wait_bool = wait.lower() == "true"
            timeout_float = float(timeout)        
            if timeout_float <= 0:
                raise HTTPException(
                    status_code=422,
                    detail="Timeout must be positive"
                )
        except ValueError:
            raise HTTPException(
                status_code=422,
                detail="Invalid parameter format"
            )
        
        # 读取件内容
        contents = await file.read()
        logging.info(f"Read file content length: {len(contents)}")
        
        try:
            img = process_image_pil(contents)
            logging.info(f"Successfully processed image: {img.shape}")
        except Exception as e:
            logging.error(f"Failed to process image: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")

        # 使用统一GPU负载均衡器选择最佳GPU
        gpu_id = await unified_gpu_balancer.get_best_gpu("ocr")

        # 获取GPU槽位
        if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "ocr"):
            raise HTTPException(status_code=503, detail="所有GPU的OCR槽位都已满")

        logging.info(f"🎯 OCR file请求分配到GPU {gpu_id}")

        try:
            # 准备任务数据
            task_data = {
                'image': img,
                'timeout': timeout_float,
                'enable_seal_hw': enable_seal_hw.lower() == "true",
                'gpu_id': gpu_id
            }

            # 使用统一任务管理器创建任务
            task_id = await unified_task_manager.create_task('ocr', task_data)
            logging.info(f"Created OCR file task ID: {task_id} on GPU {gpu_id}")

            if wait_bool:
                # 同步处理 - 等待任务完成
                result = await unified_task_manager.get_task_result(task_id, timeout_float)

                if result and result.status == "completed":
                    logging.info(f"✅ 任务 {task_id} 完成")
                    return JSONResponse(content=result.result)
                elif result and result.status == "timeout":
                    logging.warning(f"⏰ 任务 {task_id} 超时")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "timeout",
                            "message": result.error,
                            "progress": 100
                        },
                        status_code=408
                    )
                else:
                    error_msg = result.error if result else "任务未找到"
                    logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "failed",
                            "message": error_msg,
                            "progress": 100
                        },
                        status_code=500
                    )
            else:
                # 异步处理 - 立即返回任务ID
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "processing",
                        "message": "任务已提交到队列，请使用/ocr/status/{task_id}查询结果",
                        "progress": 0
                    },
                    status_code=202
                )

        except Exception as inner_e:
            logging.error(f"[OCR] Inner error in file: {str(inner_e)}")
            raise inner_e

        finally:
            # 释放GPU槽位
            await unified_gpu_balancer.release_gpu_slot(gpu_id, "ocr")
            
    except Exception as e:
        logging.error(f"[OCR] Error in ocr_file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 修改 ocr_base64 接口，使用统一GPU负载均衡器
@app.post("/ocr/base64/")
async def ocr_base64(
    request: Base64Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    gpu_id = None
    try:
        logging.info("[OCR] Processing base64 request")
        img = process_image_pil(request.image)

        # 使用统一GPU负载均衡器选择最佳GPU
        gpu_id = await unified_gpu_balancer.get_best_gpu("ocr")

        # 获取GPU槽位
        if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "ocr"):
            raise HTTPException(status_code=503, detail="所有GPU的OCR槽位都已满")

        logging.info(f"🎯 OCR base64请求分配到GPU {gpu_id}")

        try:
            # 准备任务数据
            task_data = {
                'image': img,
                'timeout': request.timeout,
                'enable_seal_hw': False,
                'gpu_id': gpu_id
            }

            # 使用统一任务管理器创建任务
            task_id = await unified_task_manager.create_task('ocr', task_data)
            logging.info(f"Created OCR base64 task ID: {task_id} on GPU {gpu_id}")

            if request.wait:
                # 同步处理 - 等待任务完成
                result = await unified_task_manager.get_task_result(task_id, request.timeout)

                if result and result.status == "completed":
                    logging.info(f"✅ 任务 {task_id} 完成")
                    return JSONResponse(content=result.result)
                elif result and result.status == "timeout":
                    logging.warning(f"⏰ 任务 {task_id} 超时")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "timeout",
                            "message": result.error,
                            "progress": 100
                        },
                        status_code=408
                    )
                else:
                    error_msg = result.error if result else "任务未找到"
                    logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "failed",
                            "message": error_msg,
                            "progress": 100
                        },
                        status_code=500
                    )
            else:
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "processing",
                        "message": "OCR processing started",
                        "progress": 0
                    },
                    status_code=202
                )

        except Exception as inner_e:
            logging.error(f"[OCR] Inner error in base64: {str(inner_e)}")
            raise inner_e

        finally:
            # 释放GPU槽位
            if gpu_id is not None:
                await unified_gpu_balancer.release_gpu_slot(gpu_id, "ocr")

    except Exception as e:
        logging.error(f"[OCR] Error in ocr_base64: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ocr/binary/")
async def ocr_binary(
    image_data: bytes = Body(..., media_type="application/octet-stream"),
    wait: bool = Query(default=False),
    timeout: float = Query(default=30.0, gt=0),
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        logging.info(f"[OCR] Processing binary request content length:{len(image_data)}")
        img = process_image_pil(image_data)

        # 使用统一GPU负载均衡器选择最佳GPU
        gpu_id = await unified_gpu_balancer.get_best_gpu("ocr")

        # 获取GPU槽位
        if not await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "ocr"):
            raise HTTPException(status_code=503, detail="所有GPU的OCR槽位都已满")

        logging.info(f"🎯 OCR请求分配到GPU {gpu_id}")

        try:
            # 准备任务数据
            task_data = {
                'image': img,
                'timeout': timeout,
                'enable_seal_hw': False,
                'gpu_id': gpu_id
            }

            # 使用统一任务管理器创建任务
            task_id = await unified_task_manager.create_task('ocr', task_data)
            logging.info(f"Created OCR binary task ID: {task_id} on GPU {gpu_id}")

            if wait:
                # 同步处理 - 等待任务完成
                result = await unified_task_manager.get_task_result(task_id, timeout)

                if result and result.status == "completed":
                    logging.info(f"✅ 任务 {task_id} 完成")
                    return JSONResponse(content=result.result)
                elif result and result.status == "timeout":
                    logging.warning(f"⏰ 任务 {task_id} 超时")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "timeout",
                            "message": result.error,
                            "progress": 100
                        },
                        status_code=408
                    )
                else:
                    error_msg = result.error if result else "任务未找到"
                    logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "failed",
                            "message": error_msg,
                            "progress": 100
                        },
                        status_code=500
                    )
            else:
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "processing",
                        "message": "OCR processing started",
                        "progress": 0
                    },
                    status_code=202
                )

        except Exception as inner_e:
            logging.error(f"[OCR] Inner error: {str(inner_e)}")
            raise inner_e

        finally:
            # 释放GPU槽位
            await unified_gpu_balancer.release_gpu_slot(gpu_id, "ocr")
            
    except Exception as e:
        logging.error(f"[OCR] Error in ocr_binary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加简化的OCR接口，兼容测试脚本
@app.post("/ocr")
async def ocr_simple(
    request: Base64Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """简化的OCR接口，直接调用ocr_base64"""
    return await ocr_base64(request, credentials)

@app.get("/ocr/result/{task_id}")
async def get_ocr_result(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    if task_id not in ocr_tasks:
        return JSONResponse(
            content={
                "status": "not_found",
                "message": "Task not found"
            },
            status_code=404
        )
    
    try:
        task = ocr_tasks[task_id]
        result = task_results.get(task_id)
        
        if not result:
            return JSONResponse(
                content={
                    "status": "not_found",
                    "message": "Task result not found"
                },
                status_code=404
            )
        
        if task.done():
            # 清理任务
            ocr_tasks.pop(task_id, None)
            result = task_results.pop(task_id, None)
            return JSONResponse(content=asdict(result))
        else:
            return JSONResponse(
                content={
                    "status": "processing",
                    "message": "OCR still processing",
                    "progress": result.progress
                },
                status_code=202
            )
    except Exception as e:
        logging.error(f"Error getting OCR result: {str(e)}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

# 修改 Swagger UI 相关的配置和路由
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义 Swagger UI 页面"""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",  # 修改路径
        swagger_css_url="/static/swagger-ui/swagger-ui.css",      # 修改路
        swagger_favicon_url="/static/favicon.ico",
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,  # 隐 Models 部分
            "deepLinking": True,  # 启用深度链接
            "displayRequestDuration": True,  # 显示请求持续时间
            "filter": True,  # 启用过滤功能
            "tryItOutEnabled": True  # 启用 "Try it out" 功能
        },
        init_oauth={
            "clientId": "your-client-id",
            "clientSecret": "your-client-secret",
            "realm": "your-realms",
            "appName": "your-app-name",
            "scopeSeparator": " ",
            "scopes": "openid profile email",
            "additionalQueryStringParams": {}
        }
    )

def process_image_pil(image_data: Union[bytes, str]) -> np.ndarray:
    """处理图像数据，支持base64和二进制格式"""
    try:
        # 处理base64数据
        if isinstance(image_data, str):
            try:
                if ',' in image_data:
                    image_data = image_data.split(',', 1)[1]
                image_data = base64.b64decode(image_data.strip())
                logging.info(f"Base64 decoded length: {len(image_data)}")
            except Exception as e:
                raise ValueError(f"Invalid base64 data: {str(e)}")
        
        if not isinstance(image_data, bytes):
            raise ValueError(f"Expected bytes or base64 string, got {type(image_data)}")
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        if len(nparr) == 0:
            raise ValueError("Empty image data")
            
        # 使用OpenCV解码图像
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if img is None:
            raise ValueError("Failed to decode image")
            
        logging.info(f"Original image shape: {img.shape}")
        
        # 基本格式转换
        if len(img.shape) == 2:  # 灰度图像
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
            logging.info("Converted grayscale to BGR")
        elif img.shape[2] == 4:  # RGBA图像
            # 使用白色背景进行alpha通道混合
            alpha = img[:, :, 3] / 255.0
            white_background = np.ones_like(img[:, :, :3]) * 255
            img_rgb = img[:, :, :3]
            img = (alpha[:, :, np.newaxis] * img_rgb + (1 - alpha[:, :, np.newaxis]) * white_background).astype(np.uint8)
            logging.info("Converted RGBA to BGR with white background")
        
        # 调整图像大小到960x960，保持宽高比
        height, width = img.shape[:2]
        target_size = 960
        
        # 计算缩放比例
        scale = min(target_size / width, target_size / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # 使用INTER_AREA进行缩小，INTER_LINEAR进行放大
        interpolation = cv2.INTER_AREA if scale < 1 else cv2.INTER_LINEAR
        img = cv2.resize(img, (new_width, new_height), interpolation=interpolation)
        
        # 创建960x960的白色背景
        square_img = np.full((target_size, target_size, 3), 255, dtype=np.uint8)
        
        # 将调整后的图像放在中心位置
        y_offset = (target_size - new_height) // 2
        x_offset = (target_size - new_width) // 2
        square_img[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = img
        
        logging.info(f"Resized image shape: {square_img.shape}")
        
        # 检查图像是否为空或全黑
        if square_img.size == 0 or np.mean(square_img) < 5:
            raise ValueError("Image is empty or completely black")
            
        # 返回RGB格式的图像
        return cv2.cvtColor(square_img, cv2.COLOR_BGR2RGB)
        
    except Exception as e:
        logging.error(f"Error in process_image_pil: {str(e)}")
        raise ValueError(f"Image processing failed: {str(e)}")



# 添加重试装饰器函数
async def retry_with_backoff_stream(generator_func, max_retries=3, initial_delay=1):
    """
    流式请求的重试装饰器
    """
    last_error = None
    for attempt in range(max_retries):
        try:
            async for chunk in generator_func():
                yield chunk
            return
        except httpx.ConnectError as e:
            last_error = e
            if attempt == max_retries - 1:
                break
            delay = initial_delay * (2 ** attempt)
            logging.warning(f"Connection failed (attempt {attempt + 1}/{max_retries}), retrying in {delay}s...")
            await asyncio.sleep(delay)
            continue
        except Exception as e:
            logging.error(f"Unexpected error in retry_with_backoff_stream: {str(e)}")
            raise

    if last_error:
        error_response = {
            "error": f"Failed to connect after {max_retries} attempts: {str(last_error)}"
        }
        yield f"data: {json.dumps(error_response)}\n\n"

#  stream_chat_completion 函数中的错误处理
async def stream_chat_completion(request: dict, server_info: dict):
    """Stream chat completion responses"""
    async def make_request():
        try:
            logging.info(f"开始流式请求到服务器: {server_info['url']}, 模型: {request.get('model')}")
            logging.debug(f"请求内容: {json.dumps(request)}")
            
            async with httpx.AsyncClient() as client:
                async with client.stream(
                    'POST',
                    f"{server_info['url']}/api/chat",
                    json=request,
                    timeout=30.0
                ) as response:
                    logging.info(f"流式请求获得响应: {response.status_code}")
                    
                    # 如果响应不成功，记录详细错误信息
                    if response.status_code != 200:
                        error_body = await response.aread()
                        error_text = error_body.decode('utf-8', errors='replace')
                        logging.error(f"流式请求失败: 状态码 {response.status_code}, 错误: {error_text}")
                        yield f"data: {{\"error\": \"服务器返回错误: {response.status_code}\", \"details\": \"{error_text}\"}}\n\n"
                        return
                    
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                # 检查是否已经是 SSE 格式
                                if line.startswith('data: '):
                                    yield line + '\n\n'
                                    continue
                                    
                                chunk = json.loads(line)
                                logging.debug(f"Received chunk: {chunk}")  # 添加调试日志
                                
                                # 转换为 OpenAI 格式的 SSE
                                if chunk.get("message") and chunk["message"].get("content"):
                                    # Ollama 格式转换为 OpenAI 格式
                                    content = chunk["message"]["content"]
                                    openai_chunk = {
                                        "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                                        "object": "chat.completion.chunk",
                                        "created": int(time.time()),
                                        "model": request.get("model", "hngpt"),
                                        "choices": [{
                                            "index": 0,
                                            "delta": {
                                                "content": content
                                            },
                                            "finish_reason": "stop" if chunk.get('done', False) else None
                                        }]
                                    }
                                    yield f"data: {json.dumps(openai_chunk)}\n\n"
                                    logging.debug(f"Sent OpenAI chunk: {openai_chunk}")

                                    if chunk.get('done', False):
                                        yield "data: [DONE]\n\n"
                                        logging.info("Stream completed")
                                elif chunk.get("choices"):
                                    # 已经是 OpenAI 格式
                                    yield f"data: {json.dumps(chunk)}\n\n"
                                    if chunk['choices'][0].get('finish_reason'):
                                        yield "data: [DONE]\n\n"
                                else:
                                    # 处理其他可能的格式或空内容
                                    logging.debug(f"Skipping chunk without content: {chunk}")
                            except json.JSONDecodeError as e:
                                logging.error(f"Failed to parse streaming response: {line}, error: {str(e)}")
                                continue
        except Exception as e:
            error_message = f"流式请求异常: {str(e)}, 类型: {type(e).__name__}"
            logging.error(error_message)
            yield f"data: {{\"error\": \"{error_message}\"}}\n\n"
    
    try:
        async for chunk in retry_with_backoff_stream(make_request):
            yield chunk
    except Exception as e:
        error_message = f"流式响应处理异常: {str(e)}, 类型: {type(e).__name__}"
        logging.error(error_message)
        yield f"data: {{\"error\": \"{error_message}\"}}\n\n"

# 添加一个关闭函数
def shutdown():
    logging.info("Shutting down server...")
    # 发送 SIGTERM 信号给当前进程
    pid = os.getpid()
    if sys.platform == "win32":
        # Windows 平台使用 taskkill
        os.system(f"taskkill /F /PID {pid}")
    else:
        # Unix 平台使用 kill
        os.kill(pid, signal.SIGTERM)

# 添加关闭接口
@app.post("/shutdown")
async def shutdown_server(
    request: ShutdownRequest,
    background_tasks: BackgroundTasks,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token != "hngpt_admin@8888":
        raise HTTPException(
            status_code=403,
            detail="Only admin can shutdown the server"
        )
    
    if not request.confirm:
        raise HTTPException(
            status_code=400,
            detail="Shutdown requires confirmation"
        )
    
    logging.warning("Server shutdown initiated by admin")
    background_tasks.add_task(shutdown)
    return {"message": "Server is shutting down..."}

# 添加识别请求模型
class RecognizeRequest(BaseModel):
    image: str  # base64编码的图像

# 添加识别接口
@app.post("/api/recognize")
async def recognize_image(
    request: RecognizeRequest,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """
    识别图像中的印章和手写文字
    """
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    try:
        # 解码base64图像
        image_data = base64.b64decode(request.image)
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise HTTPException(status_code=400, detail="Invalid image data")
            
        # 执行识别
        seals, handwritings = recognizer.recognize(img)
        
        return {
            "status": "success",
            "seals": seals,
            "handwritings": handwritings
        }
        
    except Exception as e:
        logging.error(f"Recognition error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def check_cuda_environment():
    """检查CUDA环境并处理TensorRT初始化问题"""
    try:
        # 基本CUDA检查
        cuda_available = torch.cuda.is_available()
        cuda_version = None
        cudnn_version = None

        logging.info(f"🔍 CUDA环境检查:")
        logging.info(f"  CUDA available: {cuda_available}")

        if cuda_available:
            try:
                cuda_version = torch.version.cuda
                gpu_count = torch.cuda.device_count()
                logging.info(f"  CUDA version: {cuda_version}")
                logging.info(f"  GPU count: {gpu_count}")

                # 检查每个GPU的基本信息
                for i in range(gpu_count):
                    try:
                        gpu_name = torch.cuda.get_device_name(i)
                        gpu_props = torch.cuda.get_device_properties(i)
                        gpu_memory = gpu_props.total_memory / 1024**3
                        logging.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

                        # 尝试设置设备并分配少量内存测试
                        torch.cuda.set_device(i)
                        test_tensor = torch.zeros(1, device=f'cuda:{i}')
                        del test_tensor
                        torch.cuda.empty_cache()
                        logging.info(f"  ✓ GPU {i} 基本功能测试通过")

                    except Exception as gpu_e:
                        logging.warning(f"  ⚠️ GPU {i} 测试失败: {str(gpu_e)}")

                # 检查cuDNN
                if hasattr(torch.backends, 'cudnn'):
                    cudnn_enabled = torch.backends.cudnn.enabled
                    if cudnn_enabled and hasattr(torch.backends.cudnn, 'version'):
                        cudnn_version = torch.backends.cudnn.version()
                        logging.info(f"  cuDNN version: {cudnn_version}")
                    else:
                        logging.warning("  ⚠️ cuDNN 不可用或版本信息获取失败")

            except Exception as cuda_e:
                logging.error(f"  ❌ CUDA详细检查失败: {str(cuda_e)}")
                # 即使详细检查失败，如果基本CUDA可用，仍然返回True
                # 但会在日志中记录警告
                logging.warning("  ⚠️ CUDA基本可用但详细检查失败，可能存在驱动或环境问题")

        # 检查TensorRT相关环境
        try:
            import tensorrt as trt
            trt_version = trt.__version__
            logging.info(f"  TensorRT version: {trt_version}")

            # 检查TensorRT Logger是否可以正常创建
            logger = trt.Logger(trt.Logger.WARNING)
            logging.info("  ✓ TensorRT Logger 创建成功")

        except ImportError:
            logging.warning("  ⚠️ TensorRT 未安装或不可用")
        except Exception as trt_e:
            logging.warning(f"  ⚠️ TensorRT 检查失败: {str(trt_e)}")

        # 检查PyCUDA
        try:
            import pycuda.driver as cuda
            # 不在这里初始化CUDA，避免与其他库冲突
            logging.info("  ✓ PyCUDA 可用")
        except ImportError:
            logging.warning("  ⚠️ PyCUDA 未安装或不可用")
        except Exception as pycuda_e:
            logging.warning(f"  ⚠️ PyCUDA 检查失败: {str(pycuda_e)}")

        return cuda_available

    except Exception as e:
        logging.error(f"❌ CUDA环境检查过程中发生严重错误: {str(e)}")
        logging.error("  建议检查CUDA驱动、PyTorch安装和GPU硬件状态")
        # 发生严重错误时返回False，强制使用CPU模式
        return False

def get_available_device():
    """获取可用的设备（GPU或CPU）- 使用GPU资源管理器"""
    if torch.cuda.is_available():
        # 使用GPU资源管理器获取最佳GPU
        try:
            gpu_id = task_router.gpu_manager.acquire_gpu("ocr")
            return f"cuda:{gpu_id}"
        except Exception as e:
            logging.warning(f"Failed to acquire GPU: {e}, using CPU")
            return "cpu"
    else:
        logging.warning("CUDA not available, using CPU")
        return "cpu"

# 添加 get_gpu_memory_info 函数
def get_gpu_memory_info(gpu_id):
    """获取GPU显存使用情况，使用PyTorch和nvidia-ml-py"""
    try:
        # 使用PyTorch获取已分配和已保留的内存
        torch.cuda.set_device(gpu_id)
        allocated = torch.cuda.memory_allocated(gpu_id) / (1024 * 1024)  # MB
        reserved = torch.cuda.memory_reserved(gpu_id) / (1024 * 1024)    # MB

        # 如果reserved为0，尝试获取GPU总内存
        if reserved == 0:
            try:
                # 方法1: 使用torch.cuda.get_device_properties
                props = torch.cuda.get_device_properties(gpu_id)
                total_memory = props.total_memory / (1024 * 1024)  # MB

                # 如果没有保留内存，使用已分配内存和总内存
                if allocated > 0:
                    reserved = max(reserved, allocated)
                else:
                    # 尝试分配一小块内存来激活GPU上下文
                    dummy = torch.zeros(1, device=f'cuda:{gpu_id}')
                    reserved = torch.cuda.memory_reserved(gpu_id) / (1024 * 1024)
                    del dummy
                    torch.cuda.empty_cache()

                # 如果仍然为0，使用总内存作为参考
                if reserved == 0:
                    reserved = total_memory

                # 确保reserved至少等于总内存，这样显示才有意义
                if reserved < total_memory * 0.1:  # 如果reserved太小，使用总内存
                    reserved = total_memory

            except Exception as e2:
                logging.debug(f"Failed to get total memory for GPU {gpu_id}: {str(e2)}")
                # 如果都失败了，使用一个合理的默认值
                if allocated > 0:
                    reserved = max(allocated, 1024)  # 至少1GB
                else:
                    reserved = 11264  # RTX 2080 Ti 的典型显存大小 11GB

        return allocated, reserved

    except Exception as e:
        logging.error(f"Error getting GPU memory info for GPU {gpu_id}: {str(e)}")
        # 返回默认值而不是0,0
        return 0, 11264  # 假设是11GB显存的GPU

def get_gpu_info_nvidia_smi(gpu_id):
    """使用nvidia-smi获取GPU详细信息"""
    try:
        import subprocess

        # 使用nvidia-smi获取GPU信息
        cmd = [
            'nvidia-smi',
            '--query-gpu=index,name,memory.total,memory.used,memory.free,utilization.gpu,temperature.gpu,power.draw,power.limit',
            '--format=csv,noheader,nounits',
            f'--id={gpu_id}'
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            line = result.stdout.strip()
            if line:
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 9:
                    return {
                        'index': int(parts[0]),
                        'name': parts[1],
                        'memory_total_mb': float(parts[2]),
                        'memory_used_mb': float(parts[3]),
                        'memory_free_mb': float(parts[4]),
                        'utilization_percent': float(parts[5]) if parts[5] != 'N/A' else 0,
                        'temperature_c': float(parts[6]) if parts[6] != 'N/A' else 0,
                        'power_draw_w': float(parts[7]) if parts[7] != 'N/A' else 0,
                        'power_limit_w': float(parts[8]) if parts[8] != 'N/A' else 0
                    }

        # 如果nvidia-smi失败，回退到PyTorch方法
        logging.warning(f"nvidia-smi failed for GPU {gpu_id}, using PyTorch fallback")
        return get_gpu_memory_info_fallback(gpu_id)

    except Exception as e:
        logging.error(f"获取GPU {gpu_id} nvidia-smi信息失败: {str(e)}")
        return get_gpu_memory_info_fallback(gpu_id)

def get_gpu_memory_info_fallback(gpu_id):
    """PyTorch回退方法获取GPU内存信息"""
    try:
        torch.cuda.set_device(gpu_id)
        allocated = torch.cuda.memory_allocated(gpu_id) / (1024 * 1024)  # MB
        reserved = torch.cuda.memory_reserved(gpu_id) / (1024 * 1024)    # MB

        # 获取GPU总内存
        props = torch.cuda.get_device_properties(gpu_id)
        total_memory = props.total_memory / (1024 * 1024)  # MB

        return {
            'index': gpu_id,
            'name': props.name,
            'memory_total_mb': total_memory,
            'memory_used_mb': allocated,
            'memory_free_mb': total_memory - allocated,
            'utilization_percent': 0,  # PyTorch无法获取利用率
            'temperature_c': 0,
            'power_draw_w': 0,
            'power_limit_w': 0
        }

    except Exception as e:
        logging.error(f"获取GPU {gpu_id} 回退信息失败: {str(e)}")
        return {
            'index': gpu_id,
            'name': 'Unknown GPU',
            'memory_total_mb': 11264,  # 默认11GB
            'memory_used_mb': 0,
            'memory_free_mb': 11264,
            'utilization_percent': 0,
            'temperature_c': 0,
            'power_draw_w': 0,
            'power_limit_w': 0
        }

if __name__ == "__main__":
    setup_logging("log")
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8888)
