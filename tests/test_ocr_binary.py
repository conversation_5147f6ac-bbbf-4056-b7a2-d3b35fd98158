#!/usr/bin/env python3
"""
测试OCR binary端点
"""

import requests
import time

def test_ocr_binary():
    """测试OCR binary端点"""
    print("🧪 测试OCR binary端点")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
        
        print(f"📄 测试图片: {test_image_path}")
        print(f"📏 图片大小: {len(image_data)} 字节")
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        # 测试同步模式
        params = {
            "wait": "true",
            "timeout": "30.0"
        }
        
        print(f"\n🚀 发送OCR binary请求（同步模式）...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params,
            timeout=60
        )
        
        response_time = time.time() - start_time
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ OCR成功!")
                print(f"📝 结果类型: {type(result)}")
                if isinstance(result, dict):
                    print(f"📝 结果键: {list(result.keys())}")
                    # 只显示部分结果，避免输出过长
                    if 'text' in result:
                        text = result['text'][:200] + "..." if len(str(result['text'])) > 200 else result['text']
                        print(f"📝 识别文本: {text}")
                    else:
                        print(f"📝 完整结果: {result}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
        elif response.status_code == 408:
            print(f"⏰ OCR超时")
            print(f"📄 响应: {response.text}")
        else:
            print(f"❌ OCR失败!")
            print(f"📄 错误响应: {response.text}")
            
        # 测试异步模式
        print(f"\n🚀 发送OCR binary请求（异步模式）...")
        params_async = {
            "wait": "false",
            "timeout": "30.0"
        }
        
        response_async = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params_async,
            timeout=10
        )
        
        print(f"📊 异步响应状态码: {response_async.status_code}")
        if response_async.status_code == 202:
            result = response_async.json()
            print(f"📝 任务ID: {result.get('task_id')}")
            print(f"📝 状态: {result.get('status')}")
        else:
            print(f"📄 异步响应: {response_async.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr_binary()
