#!/usr/bin/env python3
"""
单GPU并发能力测试脚本
测试OCR、Chat、Embedding三类请求的最佳并发数
"""

import asyncio
import aiohttp
import time
import json
import base64
import cv2
import numpy as np
from typing import Dict
import logging
import random
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class ConcurrencyTester:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
    def create_test_image(self) -> str:
        """创建测试图片的base64编码"""
        # 创建一个简单的测试图片
        img = np.ones((200, 400, 3), dtype=np.uint8) * 255
        cv2.putText(img, "Test OCR Image", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 编码为base64
        _, buffer = cv2.imencode('.jpg', img)
        img_base64 = base64.b64encode(buffer).decode('utf-8')
        return img_base64

    async def test_chat_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """测试单个Chat请求"""
        payload = {
            "model": "hngpt-mini:latest",
            "messages": [{"role": "user", "content": f"Hello, this is test request {request_id}"}],
            "stream": False
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                if response.status == 200:
                    return {"success": True, "response_time": response_time, "request_id": request_id, "status_code": 200}
                else:
                    return {"success": False, "status_code": response.status, "response_time": response_time, "request_id": request_id}
        except Exception as e:
            response_time = time.time() - start_time
            return {"success": False, "error": str(e), "response_time": response_time, "request_id": request_id, "status_code": 0}

    async def test_embedding_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """测试单个Embedding请求"""
        payload = {
            "model": "hngpt-embedding",
            "input": f"Test embedding text for request {request_id}"
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/embeddings",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                if response.status == 200:
                    return {"success": True, "response_time": response_time, "request_id": request_id, "status_code": 200}
                else:
                    return {"success": False, "status_code": response.status, "response_time": response_time, "request_id": request_id}
        except Exception as e:
            response_time = time.time() - start_time
            return {"success": False, "error": str(e), "response_time": response_time, "request_id": request_id, "status_code": 0}

    async def test_ocr_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """测试单个OCR请求"""
        img_base64 = self.create_test_image()
        payload = {
            "image": img_base64,
            "enable_seal_hw": False
        }

        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/ocr",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                if response.status == 200:
                    return {"success": True, "response_time": response_time, "request_id": request_id, "status_code": 200}
                else:
                    return {"success": False, "status_code": response.status, "response_time": response_time, "request_id": request_id}
        except Exception as e:
            response_time = time.time() - start_time
            return {"success": False, "error": str(e), "response_time": response_time, "request_id": request_id, "status_code": 0}

    async def test_concurrent_requests(self, request_type: str, concurrent_count: int) -> Dict:
        """测试指定类型和并发数的请求"""
        logging.info(f"🧪 测试 {request_type} 请求，并发数: {concurrent_count}")
        
        async with aiohttp.ClientSession() as session:
            # 创建并发任务
            if request_type == "chat":
                tasks = [self.test_chat_request(session, i) for i in range(concurrent_count)]
            elif request_type == "embedding":
                tasks = [self.test_embedding_request(session, i) for i in range(concurrent_count)]
            elif request_type == "ocr":
                tasks = [self.test_ocr_request(session, i) for i in range(concurrent_count)]
            else:
                raise ValueError(f"Unknown request type: {request_type}")
            
            # 执行并发请求
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # 分析结果
            success_count = 0
            error_count = 0
            total_response_time = 0
            errors = []
            
            for result in results:
                if isinstance(result, Exception):
                    error_count += 1
                    errors.append(str(result))
                elif result.get("success", False):
                    success_count += 1
                    total_response_time += result["response_time"]
                else:
                    error_count += 1
                    errors.append(result)
            
            avg_response_time = total_response_time / max(success_count, 1)
            success_rate = success_count / concurrent_count * 100
            
            return {
                "request_type": request_type,
                "concurrent_count": concurrent_count,
                "success_count": success_count,
                "error_count": error_count,
                "success_rate": success_rate,
                "avg_response_time": avg_response_time,
                "total_time": total_time,
                "throughput": success_count / total_time,
                "errors": errors[:3]  # 只保留前3个错误
            }

    async def find_optimal_concurrency(self, request_type: str, max_concurrent: int = 20) -> Dict:
        """找到指定请求类型的最佳并发数"""
        logging.info(f"🔍 开始寻找 {request_type} 的最佳并发数 (最大测试: {max_concurrent})")
        
        results = []
        optimal_concurrent = 1
        best_score = 0
        
        # 从1开始逐步增加并发数
        for concurrent in range(1, max_concurrent + 1):
            try:
                result = await self.test_concurrent_requests(request_type, concurrent)
                results.append(result)
                
                # 计算综合得分：成功率 * 吞吐量 - 错误率惩罚
                score = (result["success_rate"] / 100) * result["throughput"] - (result["error_count"] * 0.1)
                
                logging.info(f"  并发{concurrent}: 成功率{result['success_rate']:.1f}%, "
                           f"吞吐量{result['throughput']:.2f}req/s, "
                           f"平均响应{result['avg_response_time']:.2f}s, "
                           f"得分{score:.3f}")
                
                # 如果成功率低于80%，停止测试
                if result["success_rate"] < 80:
                    logging.warning(f"  成功率过低({result['success_rate']:.1f}%)，停止测试")
                    break
                
                # 更新最佳并发数
                if score > best_score:
                    best_score = score
                    optimal_concurrent = concurrent
                
                # 短暂等待，避免对服务器造成过大压力
                await asyncio.sleep(2)
                
            except Exception as e:
                logging.error(f"  并发{concurrent}测试失败: {e}")
                break
        
        return {
            "request_type": request_type,
            "optimal_concurrent": optimal_concurrent,
            "best_score": best_score,
            "all_results": results
        }

    async def test_mixed_concurrent_requests(self, total_requests: int = 30, concurrent_level: int = 10) -> Dict:
        """随机乱序并发测试3种请求类型"""
        logging.info(f"🎲 开始随机混合并发测试 - 总请求数: {total_requests}, 并发级别: {concurrent_level}")

        # 创建随机请求序列
        request_types = ["chat", "embedding", "ocr"]
        request_sequence = []

        # 生成随机请求序列，确保每种类型都有足够的样本
        for i in range(total_requests):
            request_type = random.choice(request_types)
            request_sequence.append((request_type, i + 1))

        # 打乱顺序
        random.shuffle(request_sequence)

        logging.info(f"📋 生成的随机请求序列:")
        type_counts = defaultdict(int)
        for req_type, req_id in request_sequence:
            type_counts[req_type] += 1

        for req_type, count in type_counts.items():
            logging.info(f"  {req_type.upper()}: {count} 个请求")

        # 执行混合并发测试
        results = {
            "total_requests": total_requests,
            "concurrent_level": concurrent_level,
            "request_sequence": request_sequence,
            "type_counts": dict(type_counts),
            "results": []
        }

        # 分批执行并发请求
        semaphore = asyncio.Semaphore(concurrent_level)

        async def execute_request(req_type: str, req_id: int) -> Dict:
            """执行单个请求"""
            async with semaphore:
                session_timeout = aiohttp.ClientTimeout(total=60)
                async with aiohttp.ClientSession(timeout=session_timeout) as session:
                    if req_type == "chat":
                        return await self.test_chat_request(session, req_id)
                    elif req_type == "embedding":
                        return await self.test_embedding_request(session, req_id)
                    elif req_type == "ocr":
                        return await self.test_ocr_request(session, req_id)

        # 创建所有任务
        tasks = []
        for req_type, req_id in request_sequence:
            task = execute_request(req_type, req_id)
            tasks.append((req_type, req_id, task))

        # 执行所有任务并收集结果
        logging.info(f"🚀 开始执行 {len(tasks)} 个混合并发请求...")
        start_time = time.time()

        completed_results = []
        for req_type, req_id, task in tasks:
            try:
                result = await task
                # 确保结果包含所有必需的字段
                if result is None:
                    result = {}

                result["request_type"] = req_type
                result["request_id"] = req_id

                # 确保有success字段
                if "success" not in result:
                    result["success"] = result.get("status_code", 0) == 200

                # 确保有response_time字段
                if "response_time" not in result:
                    result["response_time"] = 0

                # 确保有status_code字段
                if "status_code" not in result:
                    result["status_code"] = 0

                completed_results.append(result)

                # 实时显示进度
                if len(completed_results) % 5 == 0:
                    logging.info(f"📊 已完成 {len(completed_results)}/{len(tasks)} 个请求")

            except Exception as e:
                error_result = {
                    "request_type": req_type,
                    "request_id": req_id,
                    "success": False,
                    "error": str(e),
                    "response_time": 0,
                    "status_code": 0
                }
                completed_results.append(error_result)
                logging.error(f"❌ 请求失败 [{req_type}#{req_id}]: {e}")

        total_time = time.time() - start_time
        results["results"] = completed_results
        results["total_time"] = total_time

        # 分析结果
        self.analyze_mixed_results(results)

        return results

    def analyze_mixed_results(self, results: Dict):
        """分析混合测试结果"""
        logging.info(f"\n{'='*60}")
        logging.info("📊 随机混合并发测试结果分析")
        logging.info(f"{'='*60}")

        total_requests = results["total_requests"]
        total_time = results["total_time"]
        completed_results = results["results"]

        # 按请求类型分组统计
        type_stats = defaultdict(lambda: {
            "total": 0,
            "success": 0,
            "failed": 0,
            "response_times": [],
            "status_codes": []
        })

        for i, result in enumerate(completed_results):
            # 调试：打印结果结构
            if i < 3:  # 只打印前3个结果用于调试
                logging.debug(f"调试结果 {i}: {result}")

            req_type = result.get("request_type", "unknown")
            type_stats[req_type]["total"] += 1

            # 安全地检查success字段
            if "success" not in result:
                logging.warning(f"结果缺少success字段: {result}")
                success = False
            else:
                success = result.get("success", False)

            if success:
                type_stats[req_type]["success"] += 1
                response_time = result.get("response_time", 0)
                if response_time > 0:
                    type_stats[req_type]["response_times"].append(response_time)
            else:
                type_stats[req_type]["failed"] += 1

            type_stats[req_type]["status_codes"].append(result.get("status_code", 0))

        # 输出统计结果
        logging.info(f"⏱️  总执行时间: {total_time:.2f}s")
        logging.info(f"🚀 平均QPS: {total_requests / total_time:.2f} 请求/秒")
        logging.info(f"🎯 并发级别: {results['concurrent_level']}")

        logging.info(f"\n📈 各请求类型详细统计:")
        for req_type, stats in type_stats.items():
            success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            avg_response_time = np.mean(stats["response_times"]) if stats["response_times"] else 0

            logging.info(f"\n  {req_type.upper()}:")
            logging.info(f"    总请求数: {stats['total']}")
            logging.info(f"    成功数: {stats['success']}")
            logging.info(f"    失败数: {stats['failed']}")
            logging.info(f"    成功率: {success_rate:.1f}%")
            logging.info(f"    平均响应时间: {avg_response_time:.3f}s")

            if stats["response_times"]:
                logging.info(f"    最快响应: {min(stats['response_times']):.3f}s")
                logging.info(f"    最慢响应: {max(stats['response_times']):.3f}s")

        # 分析状态码分布
        logging.info(f"\n📊 HTTP状态码分布:")
        all_status_codes = [result.get("status_code", 0) for result in completed_results]
        status_code_counts = defaultdict(int)
        for code in all_status_codes:
            status_code_counts[code] += 1

        for code, count in sorted(status_code_counts.items()):
            percentage = (count / len(all_status_codes)) * 100
            logging.info(f"    {code}: {count} 次 ({percentage:.1f}%)")

    async def test_all_request_types(self):
        """测试所有请求类型的最佳并发数"""
        logging.info("🚀 开始测试所有请求类型的并发能力")
        
        request_types = ["embedding", "ocr", "chat"]  # 按预期难度排序
        results = {}
        
        for request_type in request_types:
            logging.info(f"\n{'='*60}")
            logging.info(f"测试 {request_type.upper()} 请求类型")
            logging.info(f"{'='*60}")
            
            # 根据请求类型设置不同的最大测试并发数
            if request_type == "chat":
                max_concurrent = 8  # Chat请求资源消耗大，测试范围小一些
            elif request_type == "ocr":
                max_concurrent = 12  # OCR中等资源消耗
            else:  # embedding
                max_concurrent = 16  # Embedding相对轻量
            
            result = await self.find_optimal_concurrency(request_type, max_concurrent)
            results[request_type] = result
            
            logging.info(f"✅ {request_type} 最佳并发数: {result['optimal_concurrent']}")
            
            # 等待一段时间再测试下一个类型
            await asyncio.sleep(5)
        
        # 输出总结
        logging.info(f"\n{'='*60}")
        logging.info("📊 单GPU并发能力测试总结")
        logging.info(f"{'='*60}")
        
        for request_type, result in results.items():
            logging.info(f"{request_type.upper()}: 最佳并发数 = {result['optimal_concurrent']}, "
                        f"最佳得分 = {result['best_score']:.3f}")
        
        return results

async def main():
    """主函数"""
    import sys

    tester = ConcurrencyTester()

    # 检查命令行参数
    test_mode = "mixed"  # 默认使用混合测试
    if len(sys.argv) > 1:
        if sys.argv[1] == "separate":
            test_mode = "separate"
        elif sys.argv[1] == "mixed":
            test_mode = "mixed"
        else:
            logging.info("用法: python test_concurrent_capacity.py [mixed|separate]")
            logging.info("  mixed: 随机混合并发测试 (默认)")
            logging.info("  separate: 分别测试各类型最佳并发数")
            return

    try:
        # 测试服务器连接
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{tester.base_url}/") as response:
                    if response.status != 200:
                        logging.error("服务器连接失败，请确保服务器正在运行")
                        return
            except:
                logging.error("无法连接到服务器，请确保服务器正在运行")
                return
    except Exception as e:
        logging.error(f"无法连接到服务器: {e}")
        return

    # 根据模式执行测试
    if test_mode == "mixed":
        logging.info("🎲 执行随机混合并发测试模式")

        # 可以通过命令行参数调整测试参数
        total_requests = 30
        concurrent_level = 10

        if len(sys.argv) > 2:
            try:
                total_requests = int(sys.argv[2])
            except ValueError:
                logging.warning("无效的总请求数参数，使用默认值30")

        if len(sys.argv) > 3:
            try:
                concurrent_level = int(sys.argv[3])
            except ValueError:
                logging.warning("无效的并发级别参数，使用默认值10")

        results = await tester.test_mixed_concurrent_requests(total_requests, concurrent_level)
        filename = "mixed_concurrent_results.json"

    else:  # separate mode
        logging.info("🔧 执行分别测试模式")
        results = await tester.test_all_request_types()
        filename = "concurrent_capacity_results.json"

    # 保存结果到文件
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    logging.info(f"\n✅ 测试完成，结果已保存到 {filename}")

    if test_mode == "mixed":
        logging.info("\n💡 提示:")
        logging.info("  - 可以使用 'python test_concurrent_capacity.py mixed 50 15' 来测试50个请求，并发级别15")
        logging.info("  - 可以使用 'python test_concurrent_capacity.py separate' 来分别测试各类型最佳并发数")

if __name__ == "__main__":
    asyncio.run(main())
