#!/usr/bin/env python3
"""
最终的混合并发测试脚本
"""

import asyncio
import aiohttp
import time
import json
import random
from collections import defaultdict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MixedConcurrencyTester:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }

    async def test_chat_request(self, session, request_id):
        """测试Chat请求"""
        payload = {
            "model": "hngpt",
            "messages": [{"role": "user", "content": f"Hello {request_id}"}],
            "stream": False
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions", 
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                return {
                    "success": response.status == 200,
                    "response_time": response_time,
                    "status_code": response.status,
                    "request_id": request_id
                }
        except Exception as e:
            return {
                "success": False,
                "response_time": time.time() - start_time,
                "status_code": 0,
                "error": str(e),
                "request_id": request_id
            }

    async def test_embedding_request(self, session, request_id):
        """测试Embedding请求"""
        payload = {
            "model": "hngpt-embedding",
            "input": f"test text {request_id}"
        }

        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/embeddings",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                return {
                    "success": response.status == 200,
                    "response_time": response_time,
                    "status_code": response.status,
                    "request_id": request_id
                }
        except Exception as e:
            return {
                "success": False,
                "response_time": time.time() - start_time,
                "status_code": 0,
                "error": str(e),
                "request_id": request_id
            }

    async def test_ocr_request(self, session, request_id):
        """测试OCR请求 - 使用/ocr/binary/端点"""
        import os

        # 使用指定的测试文件
        test_image_path = "/workspace/hngpt/tests/document.png"

        try:
            # 检查文件是否存在
            if not os.path.exists(test_image_path):
                return {
                    "success": False,
                    "response_time": 0,
                    "status_code": 0,
                    "error": f"测试图片文件不存在: {test_image_path}",
                    "request_id": request_id
                }

            # 读取二进制图片数据
            with open(test_image_path, "rb") as image_file:
                image_data = image_file.read()

            # 准备请求头
            headers = {
                "Authorization": f"Bearer {self.headers['Authorization'].split(' ')[1]}",
                "Content-Type": "application/octet-stream"
            }

            # 使用查询参数设置同步模式，增加超时时间
            params = {
                "wait": "true",
                "timeout": "60.0"  # 增加到60秒
            }

            start_time = time.time()
            async with session.post(
                f"{self.base_url}/ocr/binary/",
                data=image_data,
                headers=headers,
                params=params,
                timeout=90  # 增加HTTP请求超时时间
            ) as response:
                response_time = time.time() - start_time

                # OCR同步模式：200=成功，408=超时但任务在运行
                success = response.status in [200, 408]  # 408也算部分成功

                return {
                    "success": success,
                    "response_time": response_time,
                    "status_code": response.status,
                    "request_id": request_id
                }

        except Exception as e:
            return {
                "success": False,
                "response_time": time.time() - start_time if 'start_time' in locals() else 0,
                "status_code": 0,
                "error": str(e),
                "request_id": request_id
            }

    async def run_mixed_test(self, total_requests=15, concurrent_level=6):
        """运行混合并发测试"""
        logging.info(f"🎲 开始随机混合并发测试 - 总请求数: {total_requests}, 并发级别: {concurrent_level}")
        
        # 生成随机请求序列，包含所有3种类型
        request_types = ["chat", "embedding", "ocr"]
        request_sequence = []
        
        for i in range(total_requests):
            request_type = random.choice(request_types)
            request_sequence.append((request_type, i + 1))
        
        random.shuffle(request_sequence)
        
        # 统计请求类型
        type_counts = defaultdict(int)
        for req_type, req_id in request_sequence:
            type_counts[req_type] += 1
        
        logging.info(f"📋 生成的随机请求序列:")
        for req_type, count in type_counts.items():
            logging.info(f"  {req_type.upper()}: {count} 个请求")
        
        # 执行并发测试
        semaphore = asyncio.Semaphore(concurrent_level)
        
        async def execute_request(req_type, req_id):
            async with semaphore:
                session_timeout = aiohttp.ClientTimeout(total=60)
                async with aiohttp.ClientSession(timeout=session_timeout) as session:
                    if req_type == "chat":
                        result = await self.test_chat_request(session, req_id)
                    elif req_type == "embedding":
                        result = await self.test_embedding_request(session, req_id)
                    elif req_type == "ocr":
                        result = await self.test_ocr_request(session, req_id)
                    
                    result["request_type"] = req_type
                    return result
        
        # 创建所有任务
        tasks = []
        for req_type, req_id in request_sequence:
            task = execute_request(req_type, req_id)
            tasks.append(task)
        
        # 执行所有任务
        logging.info(f"🚀 开始执行 {len(tasks)} 个混合并发请求...")
        start_time = time.time()
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logging.error(f"❌ 任务 {i} 异常: {result}")
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "response_time": 0,
                    "status_code": 0,
                    "request_type": "unknown",
                    "request_id": i
                })
            else:
                processed_results.append(result)
        
        # 分析结果
        self.analyze_results(processed_results, total_time, concurrent_level)
        
        return {
            "total_requests": total_requests,
            "concurrent_level": concurrent_level,
            "total_time": total_time,
            "results": processed_results,
            "type_counts": dict(type_counts)
        }
    
    def analyze_results(self, results, total_time, concurrent_level):
        """分析测试结果"""
        logging.info(f"\n{'='*60}")
        logging.info("📊 随机混合并发测试结果分析")
        logging.info(f"{'='*60}")
        
        total_requests = len(results)
        
        # 按请求类型分组统计
        type_stats = defaultdict(lambda: {
            "total": 0,
            "success": 0,
            "failed": 0,
            "response_times": [],
            "status_codes": []
        })
        
        for result in results:
            req_type = result.get("request_type", "unknown")
            type_stats[req_type]["total"] += 1
            
            if result.get("success", False):
                type_stats[req_type]["success"] += 1
                response_time = result.get("response_time", 0)
                if response_time > 0:
                    type_stats[req_type]["response_times"].append(response_time)
            else:
                type_stats[req_type]["failed"] += 1
            
            type_stats[req_type]["status_codes"].append(result.get("status_code", 0))
        
        # 输出统计结果
        logging.info(f"⏱️  总执行时间: {total_time:.2f}s")
        logging.info(f"🚀 平均QPS: {total_requests / total_time:.2f} 请求/秒")
        logging.info(f"🎯 并发级别: {concurrent_level}")
        
        logging.info(f"\n📈 各请求类型详细统计:")
        for req_type, stats in type_stats.items():
            success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            avg_response_time = sum(stats["response_times"]) / len(stats["response_times"]) if stats["response_times"] else 0
            
            logging.info(f"\n  {req_type.upper()}:")
            logging.info(f"    总请求数: {stats['total']}")
            logging.info(f"    成功数: {stats['success']}")
            logging.info(f"    失败数: {stats['failed']}")
            logging.info(f"    成功率: {success_rate:.1f}%")
            logging.info(f"    平均响应时间: {avg_response_time:.3f}s")
            
            if stats["response_times"]:
                logging.info(f"    最快响应: {min(stats['response_times']):.3f}s")
                logging.info(f"    最慢响应: {max(stats['response_times']):.3f}s")

async def main():
    """主函数"""
    import sys
    
    # 解析命令行参数
    total_requests = 15
    concurrent_level = 6
    
    if len(sys.argv) > 1:
        try:
            total_requests = int(sys.argv[1])
        except ValueError:
            logging.warning("无效的总请求数参数，使用默认值15")
    
    if len(sys.argv) > 2:
        try:
            concurrent_level = int(sys.argv[2])
        except ValueError:
            logging.warning("无效的并发级别参数，使用默认值6")
    
    # 检查服务器连接
    tester = MixedConcurrencyTester()
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{tester.base_url}/") as response:
                if response.status != 200:
                    logging.error("服务器连接失败，请确保服务器正在运行")
                    return
    except Exception as e:
        logging.error(f"无法连接到服务器: {e}")
        return
    
    # 运行测试
    results = await tester.run_mixed_test(total_requests, concurrent_level)
    
    # 保存结果
    filename = "mixed_concurrent_test_results.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logging.info(f"\n✅ 测试完成，结果已保存到 {filename}")
    logging.info(f"\n💡 提示: 可以使用 'python test_mixed_final.py 20 8' 来测试20个请求，并发级别8")

if __name__ == "__main__":
    asyncio.run(main())
