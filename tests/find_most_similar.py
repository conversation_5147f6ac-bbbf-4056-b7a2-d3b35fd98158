#!/usr/bin/env python3
"""
找出与第一个句子最相似的句子
使用ncosine余弦相似度函数计算
"""

import requests
import numpy as np

# 配置
BASE_URL = "http://localhost:8888"
TOKEN = "startfrom2023"
HEADERS = {"Authorization": f"Bearer {TOKEN}"}

# 测试文本
TEXTS = [
    "Llamas eat bananas",      # 基准句子
    "Llamas in pyjamas",       # 候选句子1
    "A bowl of fruit salad",   # 候选句子2
    "A sleeping dress"         # 候选句子3
]

def ncosine(a, b):
    """计算余弦相似度"""
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

def get_embedding(text, model):
    """获取embedding向量"""
    response = requests.post(f"{BASE_URL}/v1/embeddings",
        headers=HEADERS,
        json={"model": model, "input": text}
    )
    
    if response.status_code == 200:
        return response.json()["data"][0]["embedding"]
    else:
        print(f"❌ 获取embedding失败: {response.status_code}")
        return None

def find_most_similar_sentence(model_name):
    """找出与第一个句子最相似的句子"""
    print(f"\n🔍 使用 {model_name} 模型分析")
    print("=" * 50)
    
    # 获取基准句子的embedding
    base_text = TEXTS[0]
    print(f"📌 基准句子: '{base_text}'")
    
    base_embedding = get_embedding(base_text, model_name)
    if not base_embedding:
        print("❌ 无法获取基准句子的embedding")
        return None
    
    print(f"✅ 基准embedding: {len(base_embedding)}维, L2范数={np.linalg.norm(base_embedding):.4f}")
    
    # 计算与其他句子的相似度
    similarities = []
    print(f"\n📊 相似度计算结果:")
    
    for i in range(1, len(TEXTS)):
        candidate_text = TEXTS[i]
        candidate_embedding = get_embedding(candidate_text, model_name)
        
        if candidate_embedding:
            similarity = ncosine(base_embedding, candidate_embedding)
            similarities.append((i, similarity, candidate_text))
            
            print(f"  句子{i+1}: {similarity:.6f} - '{candidate_text}'")
        else:
            print(f"  句子{i+1}: 获取失败 - '{candidate_text}'")
    
    if not similarities:
        print("❌ 没有成功计算任何相似度")
        return None
    
    # 找出最相似的句子
    most_similar = max(similarities, key=lambda x: x[1])
    most_similar_idx, max_similarity, most_similar_text = most_similar
    
    print(f"\n🏆 最相似的句子:")
    print(f"  句子{most_similar_idx+1}: '{most_similar_text}'")
    print(f"  相似度: {max_similarity:.6f}")
    
    # 排序显示所有相似度
    sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)
    print(f"\n📈 相似度排序 (从高到低):")
    for rank, (idx, sim, text) in enumerate(sorted_similarities, 1):
        print(f"  {rank}. 句子{idx+1}: {sim:.6f} - '{text}'")
    
    return most_similar

def compare_models():
    """对比两个模型的结果"""
    print("🚀 与第一个句子最相似的句子分析")
    print("=" * 60)
    
    print(f"📝 测试句子:")
    for i, text in enumerate(TEXTS):
        if i == 0:
            print(f"  {i+1}. {text} 【基准句子】")
        else:
            print(f"  {i+1}. {text}")
    
    # 测试两个模型
    models = ["hngpt-embedding", "hngpt-embedding"]
    results = {}
    
    for model in models:
        result = find_most_similar_sentence(model)
        if result:
            results[model] = result
    
    # 对比结果
    if len(results) == 2:
        print(f"\n🔄 模型对比:")
        print("=" * 50)
        
        hngpt-embedding_result = results.get("hngpt-embedding")
        hngpt_result = results.get("hngpt-embedding")
        
        if hngpt-embedding_result and hngpt_result:
            print(f"hngpt-embedding 选择:")
            print(f"  句子{hngpt-embedding_result[0]+1}: '{hngpt-embedding_result[2]}'")
            print(f"  相似度: {hngpt-embedding_result[1]:.6f}")
            
            print(f"\nhngpt-embedding 选择:")
            print(f"  句子{hngpt_result[0]+1}: '{hngpt_result[2]}'")
            print(f"  相似度: {hngpt_result[1]:.6f}")
            
            # 分析差异
            if hngpt-embedding_result[0] == hngpt_result[0]:
                print(f"\n✅ 两个模型选择了相同的最相似句子")
                similarity_diff = abs(hngpt-embedding_result[1] - hngpt_result[1])
                print(f"   相似度差异: {similarity_diff:.6f}")
            else:
                print(f"\n⚠️ 两个模型选择了不同的最相似句子")
                print(f"   这可能反映了模型在语义理解上的差异")
            
            # 分析相似度数值
            if hngpt_result[1] > hngpt-embedding_result[1]:
                print(f"\n📈 hngpt-embedding 给出了更高的相似度分数")
            elif hngpt-embedding_result[1] > hngpt_result[1]:
                print(f"\n📈 hngpt-embedding 给出了更高的相似度分数")
            else:
                print(f"\n🤝 两个模型给出了相同的相似度分数")

def analyze_semantic_relationships():
    """分析语义关系"""
    print(f"\n🧠 语义关系分析:")
    print("=" * 30)
    
    print(f"预期分析:")
    print(f"  'Llamas eat bananas' vs 'Llamas in pyjamas':")
    print(f"    - 都包含 'Llamas'，应该有较高相似度")
    print(f"  'Llamas eat bananas' vs 'A bowl of fruit salad':")
    print(f"    - 都与食物相关 ('bananas', 'fruit')，可能有中等相似度")
    print(f"  'Llamas eat bananas' vs 'A sleeping dress':")
    print(f"    - 语义关系最远，应该相似度最低")
    
    print(f"\n💡 理想情况下:")
    print(f"  最相似: 'Llamas in pyjamas' (共同主语)")
    print(f"  次相似: 'A bowl of fruit salad' (食物关联)")
    print(f"  最不相似: 'A sleeping dress' (无明显关联)")

def main():
    """主函数"""
    analyze_semantic_relationships()
    compare_models()
    
    print(f"\n✅ 分析完成!")
    print(f"\n💡 说明:")
    print(f"  - ncosine函数: np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))")
    print(f"  - 相似度范围: -1 到 1 (1表示完全相同)")
    print(f"  - hngpt-embedding使用指令优化，可能在语义理解上表现更好")

if __name__ == "__main__":
    main()
