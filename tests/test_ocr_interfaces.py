#!/usr/bin/env python3
"""
测试所有OCR接口
"""

import asyncio
import aiohttp
import logging
import base64
import io
from PIL import Image

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OCRInterfacesTester:
    def __init__(self):
        self.base_url = "http://localhost:8888"
        self.headers = {"Authorization": "Bearer startfrom2023"}
        
        # 创建测试图像
        self.test_image_data = self.create_test_image()
        self.test_image_base64 = base64.b64encode(self.test_image_data).decode('utf-8')
    
    def create_test_image(self):
        """创建一个简单的测试图像"""
        # 创建一个简单的白色背景图像，上面有黑色文字
        img = Image.new('RGB', (200, 100), color='white')
        
        # 将图像转换为字节
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='PNG')
        return img_byte_arr.getvalue()
    
    async def test_ocr_binary(self):
        """测试 /ocr/binary/ 接口"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/ocr/binary/?wait=true&timeout=30.0",
                    data=self.test_image_data,
                    headers={
                        **self.headers,
                        "Content-Type": "application/octet-stream"
                    }
                ) as response:
                    if response.status in [200, 202]:
                        result = await response.json()
                        logging.info("✅ OCR Binary接口测试成功")
                        return True
                    else:
                        text = await response.text()
                        logging.error(f"❌ OCR Binary接口失败: {response.status} - {text}")
                        return False
        except Exception as e:
            logging.error(f"❌ OCR Binary接口异常: {e}")
            return False
    
    async def test_ocr_base64(self):
        """测试 /ocr/base64/ 接口"""
        try:
            payload = {
                "image": self.test_image_base64,
                "file_name": "test.png",
                "page_no": 1,
                "wait": True,
                "timeout": 30.0,
                "test_mode": False,
                "enable_seal_hw": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/ocr/base64/",
                    json=payload,
                    headers={
                        **self.headers,
                        "Content-Type": "application/json"
                    }
                ) as response:
                    if response.status in [200, 202]:
                        result = await response.json()
                        logging.info("✅ OCR Base64接口测试成功")
                        return True
                    else:
                        text = await response.text()
                        logging.error(f"❌ OCR Base64接口失败: {response.status} - {text}")
                        return False
        except Exception as e:
            logging.error(f"❌ OCR Base64接口异常: {e}")
            return False
    
    async def test_ocr_file(self):
        """测试 /ocr/file/ 接口"""
        try:
            # 创建multipart/form-data
            data = aiohttp.FormData()
            data.add_field('file', 
                          io.BytesIO(self.test_image_data), 
                          filename='test.png', 
                          content_type='image/png')
            data.add_field('wait', 'true')
            data.add_field('timeout', '30.0')
            data.add_field('enable_seal_hw', 'false')
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/ocr/file/",
                    data=data,
                    headers=self.headers
                ) as response:
                    if response.status in [200, 202]:
                        result = await response.json()
                        logging.info("✅ OCR File接口测试成功")
                        return True
                    else:
                        text = await response.text()
                        logging.error(f"❌ OCR File接口失败: {response.status} - {text}")
                        return False
        except Exception as e:
            logging.error(f"❌ OCR File接口异常: {e}")
            return False
    
    async def get_gpu_status(self):
        """获取GPU状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/gpu/status") as response:
                    if response.status == 200:
                        result = await response.json()
                        data = result.get("data", {})
                        logging.info("📊 GPU状态:")
                        logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
                        logging.info(f"  当前负载: {data.get('current_loads', {})}")
                        return data
                    else:
                        logging.error(f"❌ 获取GPU状态失败: {response.status}")
                        return None
        except Exception as e:
            logging.error(f"❌ 获取GPU状态异常: {e}")
            return None
    
    async def run_tests(self):
        """运行所有测试"""
        logging.info("🚀 开始OCR接口综合测试")
        
        # 检查服务器连接
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.base_url) as response:
                    if response.status == 200:
                        logging.info("✅ 服务器连接成功")
                    else:
                        logging.error("❌ 服务器连接失败")
                        return
        except Exception as e:
            logging.error(f"❌ 无法连接到服务器: {e}")
            return
        
        # 获取初始GPU状态
        logging.info("\n📊 初始GPU状态:")
        await self.get_gpu_status()
        
        # 测试各个接口
        results = {}
        
        logging.info("\n🔍 测试OCR Binary接口...")
        results['binary'] = await self.test_ocr_binary()
        
        logging.info("\n🔍 测试OCR Base64接口...")
        results['base64'] = await self.test_ocr_base64()
        
        logging.info("\n🔍 测试OCR File接口...")
        results['file'] = await self.test_ocr_file()
        
        # 获取最终GPU状态
        logging.info("\n📊 最终GPU状态:")
        await self.get_gpu_status()
        
        # 总结结果
        logging.info("\n" + "="*60)
        logging.info("📊 OCR接口测试总结")
        logging.info("="*60)
        
        success_count = sum(results.values())
        total_count = len(results)
        
        for interface, success in results.items():
            status = "✅" if success else "❌"
            logging.info(f"  {status} OCR {interface.upper()} 接口")
        
        logging.info(f"\n总成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            logging.info("🎉 所有OCR接口测试成功！")
        else:
            logging.warning("⚠️ 部分OCR接口测试失败")

async def main():
    tester = OCRInterfacesTester()
    await tester.run_tests()

if __name__ == "__main__":
    asyncio.run(main())
