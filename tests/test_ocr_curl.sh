#!/bin/bash
"""
使用curl测试OCR binary端点
"""

echo "🧪 使用curl测试OCR binary端点"

# 检查测试图片是否存在
TEST_IMAGE="tests/document.png"
if [ ! -f "$TEST_IMAGE" ]; then
    echo "❌ 测试图片不存在: $TEST_IMAGE"
    exit 1
fi

echo "📄 使用测试图片: $TEST_IMAGE"
echo "📏 图片大小: $(wc -c < "$TEST_IMAGE") 字节"

echo ""
echo "🚀 测试1: OCR同步模式（30秒超时）"
echo "命令: curl -X POST 'http://localhost:8888/ocr/binary/?wait=true&timeout=30' ..."

time curl -X 'POST' \
  'http://localhost:8888/ocr/binary/?wait=true&timeout=30' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer startfrom2023' \
  -H 'Content-Type: application/octet-stream' \
  --data-binary "@$TEST_IMAGE" \
  -w "\n📊 HTTP状态码: %{http_code}\n⏱️  总时间: %{time_total}s\n" \
  -s

echo ""
echo "="*60
echo ""
echo "🚀 测试2: OCR异步模式"
echo "命令: curl -X POST 'http://localhost:8888/ocr/binary/?wait=false&timeout=60' ..."

RESPONSE=$(curl -X 'POST' \
  'http://localhost:8888/ocr/binary/?wait=false&timeout=60' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer startfrom2023' \
  -H 'Content-Type: application/octet-stream' \
  --data-binary "@$TEST_IMAGE" \
  -w "HTTP_CODE:%{http_code}" \
  -s)

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "📊 HTTP状态码: $HTTP_CODE"
echo "📝 响应内容: $RESPONSE_BODY"

# 如果是202状态码，尝试提取task_id并查询结果
if [ "$HTTP_CODE" = "202" ]; then
    TASK_ID=$(echo "$RESPONSE_BODY" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
    if [ -n "$TASK_ID" ]; then
        echo "📝 任务ID: $TASK_ID"
        echo ""
        echo "⏳ 等待5秒后查询任务状态..."
        sleep 5
        
        echo "🔍 查询任务结果: curl -X GET 'http://localhost:8888/ocr/result/$TASK_ID' ..."
        curl -X 'GET' \
          "http://localhost:8888/ocr/result/$TASK_ID" \
          -H 'accept: application/json' \
          -H 'Authorization: Bearer startfrom2023' \
          -w "\n📊 查询状态码: %{http_code}\n" \
          -s
    fi
fi

echo ""
echo "="*60
echo ""
echo "🚀 测试3: 使用其他测试图片"

# 测试seal-hand.png
if [ -f "tests/seal-hand.png" ]; then
    echo "📄 测试图片: tests/seal-hand.png"
    echo "📏 图片大小: $(wc -c < "tests/seal-hand.png") 字节"
    
    time curl -X 'POST' \
      'http://localhost:8888/ocr/binary/?wait=true&timeout=30' \
      -H 'accept: application/json' \
      -H 'Authorization: Bearer startfrom2023' \
      -H 'Content-Type: application/octet-stream' \
      --data-binary "@tests/seal-hand.png" \
      -w "\n📊 HTTP状态码: %{http_code}\n⏱️  总时间: %{time_total}s\n" \
      -s
fi

echo ""
echo "✅ curl测试完成"
