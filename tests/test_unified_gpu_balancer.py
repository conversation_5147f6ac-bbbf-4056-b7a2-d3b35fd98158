#!/usr/bin/env python3
"""
统一GPU负载均衡器测试脚本
测试新的UnifiedGPULoadBalancer类的功能
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class UnifiedBalancerTester:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
    async def get_gpu_status(self) -> Dict:
        """获取GPU状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/gpu/status") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logging.error(f"获取GPU状态失败: {response.status}")
                        return {}
        except Exception as e:
            logging.error(f"获取GPU状态异常: {e}")
            return {}

    def get_test_request_data(self, request_type: str) -> Dict:
        """获取测试请求数据"""
        if request_type == "chat":
            return {
                "messages": [{"role": "user", "content": "Hello, this is a test message for load balancing."}],
                "model": "hngpt-mini:latest",
                "temperature": 0.7,
                "max_tokens": 100
            }
        elif request_type == "embedding":
            return {
                "input": "This is a test text for embedding load balancing.",
                "model": "hngpt-embedding"
            }
        elif request_type == "ocr":
            # 简化的OCR测试数据
            return {
                "timeout": 30.0,
                "enable_seal_hw": False
            }
        else:
            return {"test": True}

    async def test_balancer_request(self, request_type: str) -> Dict:
        """测试负载均衡器请求"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/api/test/unified-balancer"
                params = {"request_type": request_type}

                # 添加请求数据到请求体
                request_data = self.get_test_request_data(request_type)

                async with session.post(url, params=params, json=request_data, headers=self.headers) as response:
                    result = await response.json()
                    result["status_code"] = response.status
                    return result
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type,
                "status_code": 0
            }

    async def test_concurrent_requests(self, request_type: str, count: int) -> List[Dict]:
        """测试并发请求"""
        logging.info(f"🧪 测试{request_type}并发请求，数量: {count}")
        
        # 创建并发任务
        tasks = []
        for i in range(count):
            task = self.test_balancer_request(request_type)
            tasks.append(task)
        
        # 执行并发请求
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "request_id": i,
                    "status": "exception",
                    "error": str(result),
                    "request_type": request_type
                })
            else:
                result["request_id"] = i
                processed_results.append(result)
        
        logging.info(f"✅ {request_type}并发测试完成，耗时: {total_time:.2f}s")
        return processed_results

    async def test_mixed_requests(self, total_requests: int = 20) -> Dict:
        """测试混合请求类型"""
        logging.info(f"🎲 开始混合请求测试，总数: {total_requests}")
        
        # 创建混合请求序列
        request_types = ["chat", "embedding", "ocr"]
        tasks = []
        
        for i in range(total_requests):
            request_type = request_types[i % len(request_types)]
            task = self.test_balancer_request(request_type)
            tasks.append((request_type, i, task))
        
        # 执行混合请求
        start_time = time.time()
        results = []
        
        for request_type, request_id, task in tasks:
            try:
                result = await task
                result["request_id"] = request_id
                result["request_type"] = request_type
                results.append(result)
                
                # 每5个请求显示进度
                if len(results) % 5 == 0:
                    logging.info(f"📊 已完成 {len(results)}/{total_requests} 个请求")
                    
            except Exception as e:
                results.append({
                    "request_id": request_id,
                    "request_type": request_type,
                    "status": "exception",
                    "error": str(e)
                })
        
        total_time = time.time() - start_time
        
        return {
            "total_requests": total_requests,
            "total_time": total_time,
            "results": results
        }

    def analyze_results(self, test_results: Dict):
        """分析测试结果"""
        results = test_results["results"]
        total_time = test_results["total_time"]
        total_requests = test_results["total_requests"]
        
        # 按请求类型统计
        type_stats = {}
        gpu_assignments = {}
        
        for result in results:
            request_type = result.get("request_type", "unknown")
            
            if request_type not in type_stats:
                type_stats[request_type] = {
                    "total": 0,
                    "success": 0,
                    "failed": 0,
                    "gpu_assignments": {}
                }
            
            type_stats[request_type]["total"] += 1
            
            if result.get("status") == "success":
                type_stats[request_type]["success"] += 1
                
                # 统计GPU分配
                gpu_id = result.get("result", {}).get("gpu_id")
                if gpu_id is not None:
                    if gpu_id not in type_stats[request_type]["gpu_assignments"]:
                        type_stats[request_type]["gpu_assignments"][gpu_id] = 0
                    type_stats[request_type]["gpu_assignments"][gpu_id] += 1
                    
                    if gpu_id not in gpu_assignments:
                        gpu_assignments[gpu_id] = 0
                    gpu_assignments[gpu_id] += 1
            else:
                type_stats[request_type]["failed"] += 1
        
        # 输出分析结果
        logging.info(f"\n{'='*60}")
        logging.info("📊 统一GPU负载均衡器测试结果分析")
        logging.info(f"{'='*60}")
        
        logging.info(f"⏱️  总执行时间: {total_time:.2f}s")
        logging.info(f"🚀 平均QPS: {total_requests / total_time:.2f} 请求/秒")
        
        logging.info(f"\n📈 按请求类型统计:")
        for request_type, stats in type_stats.items():
            success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            logging.info(f"\n  {request_type.upper()}:")
            logging.info(f"    总数: {stats['total']}")
            logging.info(f"    成功: {stats['success']}")
            logging.info(f"    失败: {stats['failed']}")
            logging.info(f"    成功率: {success_rate:.1f}%")
            
            if stats["gpu_assignments"]:
                logging.info(f"    GPU分配:")
                for gpu_id, count in stats["gpu_assignments"].items():
                    logging.info(f"      GPU {gpu_id}: {count} 次")
        
        logging.info(f"\n🎯 GPU总体分配统计:")
        for gpu_id, count in sorted(gpu_assignments.items()):
            percentage = (count / total_requests) * 100
            logging.info(f"  GPU {gpu_id}: {count} 次 ({percentage:.1f}%)")

    async def run_comprehensive_test(self):
        """运行综合测试"""
        logging.info("🚀 开始统一GPU负载均衡器综合测试")
        
        # 1. 获取初始GPU状态
        initial_status = await self.get_gpu_status()
        if initial_status.get("status") == "success":
            logging.info("📊 初始GPU状态获取成功")
            balancer_data = initial_status.get("data", {})
            logging.info(f"  GPU数量: {balancer_data.get('gpu_count', 0)}")
            logging.info(f"  并发限制: {balancer_data.get('concurrent_limits', {})}")
        else:
            logging.error("❌ 无法获取初始GPU状态")
            return
        
        # 2. 测试单个请求类型
        for request_type in ["chat", "embedding", "ocr"]:
            logging.info(f"\n{'='*40}")
            logging.info(f"测试 {request_type.upper()} 请求")
            logging.info(f"{'='*40}")
            
            # 测试单个请求
            result = await self.test_balancer_request(request_type)
            if result.get("status") == "success":
                logging.info(f"✅ {request_type}单个请求测试成功")
                gpu_id = result.get("result", {}).get("gpu_id")
                logging.info(f"  分配到GPU: {gpu_id}")
            else:
                logging.error(f"❌ {request_type}单个请求测试失败: {result.get('error')}")
            
            await asyncio.sleep(1)  # 短暂等待
        
        # 3. 测试混合并发请求
        logging.info(f"\n{'='*40}")
        logging.info("测试混合并发请求")
        logging.info(f"{'='*40}")
        
        mixed_results = await self.test_mixed_requests(total_requests=30)
        self.analyze_results(mixed_results)
        
        # 4. 获取最终GPU状态
        final_status = await self.get_gpu_status()
        if final_status.get("status") == "success":
            logging.info(f"\n📊 最终GPU状态:")
            balancer_data = final_status.get("data", {})
            current_loads = balancer_data.get("current_loads", {})
            for gpu_id, loads in current_loads.items():
                logging.info(f"  GPU {gpu_id}: {loads}")
        
        logging.info(f"\n✅ 统一GPU负载均衡器综合测试完成")

async def main():
    """主函数"""
    tester = UnifiedBalancerTester()
    
    # 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{tester.base_url}/") as response:
                if response.status != 200:
                    logging.error("服务器连接失败，请确保服务器正在运行")
                    return
    except Exception as e:
        logging.error(f"无法连接到服务器: {e}")
        return
    
    # 运行综合测试
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
