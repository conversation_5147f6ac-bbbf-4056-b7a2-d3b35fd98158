#!/usr/bin/env python3
"""
快速测试统一GPU负载均衡器
修复JSON序列化问题后的验证脚本
"""

import asyncio
import aiohttp
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_gpu_status():
    """测试GPU状态获取"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/api/gpu/status") as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ GPU状态获取成功")
                    data = result.get("data", {})
                    logging.info(f"  GPU数量: {data.get('gpu_count', 0)}")
                    logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
                    return True
                else:
                    logging.error(f"❌ GPU状态获取失败: {response.status}")
                    return False
    except Exception as e:
        logging.error(f"❌ GPU状态获取异常: {e}")
        return False

async def test_request_type(request_type: str):
    """测试特定类型的请求"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    
    # 准备测试数据
    if request_type == "chat":
        test_data = {
            "messages": [{"role": "user", "content": "Hello, test message"}],
            "model": "hngpt-mini:latest"
        }
    elif request_type == "embedding":
        test_data = {
            "input": "Test embedding text",
            "model": "hngpt-embedding"
        }
    elif request_type == "ocr":
        test_data = {
            "timeout": 30.0,
            "enable_seal_hw": False
        }
    else:
        test_data = {}
    
    try:
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:8888/api/test/unified-balancer"
            params = {"request_type": request_type}
            
            async with session.post(url, params=params, json=test_data, headers=headers) as response:
                result = await response.json()
                
                if response.status == 200 and result.get("status") == "success":
                    gpu_id = result.get("result", {}).get("gpu_id")
                    logging.info(f"✅ {request_type.upper()} 测试成功 - GPU {gpu_id}")
                    return True
                else:
                    error = result.get("error", "Unknown error")
                    logging.error(f"❌ {request_type.upper()} 测试失败: {error}")
                    return False
                    
    except Exception as e:
        logging.error(f"❌ {request_type.upper()} 测试异常: {e}")
        return False

async def main():
    """主函数"""
    logging.info("🚀 开始快速测试统一GPU负载均衡器")
    
    # 1. 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/") as response:
                if response.status == 200:
                    logging.info("✅ 服务器连接成功")
                else:
                    logging.error("❌ 服务器连接失败")
                    return
    except Exception as e:
        logging.error(f"❌ 无法连接到服务器: {e}")
        return
    
    # 2. 测试GPU状态
    if not await test_gpu_status():
        logging.error("❌ GPU状态测试失败，终止测试")
        return
    
    # 3. 测试各种请求类型
    success_count = 0
    total_tests = 3
    
    for request_type in ["chat", "embedding", "ocr"]:
        if await test_request_type(request_type):
            success_count += 1
        await asyncio.sleep(0.5)  # 短暂等待
    
    # 4. 总结
    logging.info(f"\n{'='*50}")
    logging.info("📊 测试总结")
    logging.info(f"{'='*50}")
    logging.info(f"成功测试: {success_count}/{total_tests}")
    logging.info(f"成功率: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        logging.info("🎉 所有测试通过！统一GPU负载均衡器工作正常")
    else:
        logging.warning("⚠️  部分测试失败，请检查日志")

if __name__ == "__main__":
    asyncio.run(main())
