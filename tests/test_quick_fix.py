#!/usr/bin/env python3
"""
快速测试修复后的负载均衡器
验证Chat、Embedding接口的基本功能
"""

import asyncio
import aiohttp
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_gpu_status():
    """测试GPU状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/api/gpu/status") as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ GPU状态获取成功")
                    data = result.get("data", {})
                    logging.info(f"  GPU数量: {data.get('gpu_count', 0)}")
                    logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
                    logging.info(f"  当前负载: {data.get('current_loads', {})}")
                    return True
                else:
                    logging.error(f"❌ GPU状态获取失败: {response.status}")
                    return False
    except Exception as e:
        logging.error(f"❌ GPU状态获取异常: {e}")
        return False

async def test_chat():
    """测试Chat接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "hngpt-mini:latest",
        "messages": [{"role": "user", "content": "Hello, test chat"}],
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/chat/completions", 
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ Chat接口测试成功")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ Chat接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ Chat接口异常: {e}")
        return False

async def test_unified_chat():
    """测试统一Chat接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "hngpt-mini:latest",
        "messages": [{"role": "user", "content": "Hello, test unified chat"}],
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/chat/completions/test",
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ 测试Chat接口测试成功")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ 统一Chat接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ 统一Chat接口异常: {e}")
        return False

async def test_embedding():
    """测试Embedding接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "hngpt-embedding",
        "input": "Test embedding text"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/embeddings", 
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ Embedding接口测试成功")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ Embedding接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ Embedding接口异常: {e}")
        return False

async def main():
    """主函数"""
    logging.info("🚀 开始快速修复验证测试")
    
    # 1. 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/") as response:
                if response.status == 200:
                    logging.info("✅ 服务器连接成功")
                else:
                    logging.error("❌ 服务器连接失败")
                    return
    except Exception as e:
        logging.error(f"❌ 无法连接到服务器: {e}")
        return
    
    # 2. 测试GPU状态
    logging.info("\n📊 测试GPU状态...")
    if not await test_gpu_status():
        logging.error("❌ GPU状态测试失败")
        return
    
    # 3. 测试各个接口
    tests = [
        ("Chat接口", test_chat),
        ("统一Chat接口", test_unified_chat),
        ("Embedding接口", test_embedding)
    ]
    
    results = []
    for name, test_func in tests:
        logging.info(f"\n🔍 测试{name}...")
        success = await test_func()
        results.append((name, success))
        await asyncio.sleep(1)  # 等待1秒
    
    # 4. 再次检查GPU状态
    logging.info("\n📊 最终GPU状态...")
    await test_gpu_status()
    
    # 5. 总结
    logging.info(f"\n{'='*50}")
    logging.info("📊 测试总结")
    logging.info(f"{'='*50}")
    
    success_count = sum(1 for _, success in results if success)
    total_tests = len(results)
    
    for name, success in results:
        status = "✅" if success else "❌"
        logging.info(f"  {status} {name}")
    
    logging.info(f"\n成功: {success_count}/{total_tests}")
    logging.info(f"成功率: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        logging.info("🎉 所有测试通过！修复成功")
    else:
        logging.warning("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
