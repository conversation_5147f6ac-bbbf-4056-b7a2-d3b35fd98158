#!/usr/bin/env python3
"""
API使用示例：演示如何调用app.py中的/v1/embeddings接口
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8888"
TOKEN = "startfrom2023"  # 替换为实际token

def call_embedding_api_openai_format(text, model):
    """使用OpenAI格式调用embedding API"""
    
    headers = {"Authorization": f"Bearer {TOKEN}"}
    
    # OpenAI兼容格式
    payload = {
        "model": model,
        "input": text,
        "encoding_format": "float"
    }
    
    print(f"📤 请求 ({model}):")
    print(f"  URL: {BASE_URL}/v1/embeddings")
    print(f"  Headers: {headers}")
    print(f"  Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/embeddings",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"\n📥 响应:")
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            embedding = result["data"][0]["embedding"]
            
            print(f"  ✅ 成功")
            print(f"  - 模型: {result['model']}")
            print(f"  - 维度: {len(embedding)}")
            print(f"  - 前5个值: {embedding[:5]}")
            print(f"  - 使用情况: {result.get('usage', {})}")
            
            return embedding
        else:
            print(f"  ❌ 失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"  ❌ 异常: {e}")
        return None

def call_embedding_api_standard_format(text, model):
    """使用标准格式调用embedding API"""
    
    headers = {"Authorization": f"Bearer {TOKEN}"}
    
    # 标准格式
    payload = {
        "model": model,
        "prompt": text,
        "encoding_format": "float"
    }
    
    print(f"📤 请求 ({model}) - 标准格式:")
    print(f"  Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/embeddings",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            embedding = result["data"][0]["embedding"]
            print(f"  ✅ 成功 - 维度: {len(embedding)}")
            return embedding
        else:
            print(f"  ❌ 失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 异常: {e}")
        return None

def demo_basic_usage():
    """演示基本用法"""
    print("🚀 基本用法演示")
    print("=" * 40)
    
    test_text = "Llamas eat bananas"
    
    # 测试 hngpt-embedding
    print(f"\n1️⃣ 测试 hngpt-embedding 模型:")
    hngpt_emb = call_embedding_api_openai_format(test_text, "hngpt-embedding")
    
    # 测试 hngpt-embedding
    print(f"\n2️⃣ 测试 hngpt-embedding 模型:")
    hngpt-embedding_emb = call_embedding_api_openai_format(test_text, "hngpt-embedding")
    
    # 对比结果
    if hngpt_emb and hngpt-embedding_emb:
        import numpy as np
        
        hngpt_norm = np.linalg.norm(hngpt_emb)
        hngpt-embedding_norm = np.linalg.norm(hngpt-embedding_emb)
        
        print(f"\n📊 结果对比:")
        print(f"  hngpt-embedding L2范数: {hngpt_norm:.4f}")
        print(f"  hngpt-embedding L2范数: {hngpt-embedding_norm:.4f}")
        
        # 计算相似度
        hngpt_normalized = np.array(hngpt_emb) / hngpt_norm
        hngpt-embedding_normalized = np.array(hngpt-embedding_emb) / hngpt-embedding_norm
        similarity = np.dot(hngpt_normalized, hngpt-embedding_normalized)
        
        print(f"  模型间相似度: {similarity:.4f}")

def demo_batch_processing():
    """演示批量处理"""
    print(f"\n🔄 批量处理演示")
    print("=" * 40)
    
    texts = [
        "Llamas eat bananas",
        "Llamas in pyjamas", 
        "A bowl of fruit salad",
        "A sleeping dress"
    ]
    
    for model in ["hngpt-embedding", "hngpt-embedding"]:
        print(f"\n📦 批量处理 - {model}:")
        
        embeddings = []
        for i, text in enumerate(texts):
            print(f"  处理文本 {i+1}: '{text}'")
            emb = call_embedding_api_openai_format(text, model)
            if emb:
                embeddings.append(emb)
                print(f"    ✅ 完成")
            else:
                print(f"    ❌ 失败")
        
        print(f"  📊 批量结果: {len(embeddings)}/{len(texts)} 成功")

def demo_error_handling():
    """演示错误处理"""
    print(f"\n⚠️ 错误处理演示")
    print("=" * 40)
    
    # 测试不存在的模型
    print(f"\n1️⃣ 测试不存在的模型:")
    call_embedding_api_openai_format("test", "non-existent-model")
    
    # 测试空文本
    print(f"\n2️⃣ 测试空文本:")
    call_embedding_api_openai_format("", "hngpt-embedding")
    
    # 测试错误的token
    print(f"\n3️⃣ 测试错误的token:")
    global TOKEN
    original_token = TOKEN
    TOKEN = "invalid_token"
    call_embedding_api_openai_format("test", "hngpt-embedding")
    TOKEN = original_token

def demo_different_formats():
    """演示不同的请求格式"""
    print(f"\n📝 不同格式演示")
    print("=" * 40)
    
    test_text = "Llamas eat bananas"
    
    # OpenAI格式
    print(f"\n1️⃣ OpenAI格式:")
    call_embedding_api_openai_format(test_text, "hngpt-embedding")
    
    # 标准格式
    print(f"\n2️⃣ 标准格式:")
    call_embedding_api_standard_format(test_text, "hngpt-embedding")

def main():
    """主函数"""
    print("📚 app.py /v1/embeddings API 使用示例")
    print("=" * 50)
    
    print(f"🔧 配置:")
    print(f"  服务地址: {BASE_URL}")
    print(f"  认证token: {TOKEN}")
    print(f"  支持模型: hngpt-embedding, hngpt-embedding")
    
    # 运行各种演示
    demo_basic_usage()
    demo_batch_processing()
    demo_different_formats()
    demo_error_handling()
    
    print(f"\n✅ 演示完成!")
    print(f"\n💡 使用提示:")
    print(f"  - hngpt-embedding: 自动指令优化 + L2归一化")
    print(f"  - hngpt-embedding: 简单处理，兼容现有系统")
    print(f"  - 支持OpenAI兼容格式和标准格式")
    print(f"  - 记得替换实际的认证token")

if __name__ == "__main__":
    main()
