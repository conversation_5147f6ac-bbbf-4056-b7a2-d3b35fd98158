#!/usr/bin/env python3
"""
测试Ollama的并发处理能力
直接测试Ollama API，不通过app.py
"""

import asyncio
import aiohttp
import time
from datetime import datetime

# Ollama配置
OLLAMA_URL = "http://localhost:11434"

async def test_ollama_chat(session, message, request_id):
    """直接测试Ollama chat API"""
    start_time = time.time()
    
    payload = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": message}],
        "stream": False,
        "options": {
            "num_predict": 50  # 限制输出长度
        }
    }
    
    try:
        async with session.post(
            f"{OLLAMA_URL}/api/chat",
            json=payload,
            timeout=aiohttp.ClientTimeout(total=60)
        ) as response:
            response_time = time.time() - start_time
            
            if response.status == 200:
                result = await response.json()
                content = result["message"]["content"]
                return f"Chat-{request_id}: ✅ {response_time:.2f}s - {content[:50]}..."
            else:
                error = await response.text()
                return f"Chat-{request_id}: ❌ {response_time:.2f}s - {error[:50]}..."
                
    except Exception as e:
        response_time = time.time() - start_time
        return f"Chat-{request_id}: ❌ {response_time:.2f}s - {str(e)[:50]}..."

async def test_ollama_embedding(session, text, model, request_id):
    """直接测试Ollama embedding API"""
    start_time = time.time()
    
    payload = {
        "model": model,
        "prompt": text
    }
    
    try:
        async with session.post(
            f"{OLLAMA_URL}/api/embeddings",
            json=payload,
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            response_time = time.time() - start_time
            
            if response.status == 200:
                result = await response.json()
                dim = len(result["embedding"])
                return f"Emb-{request_id}({model}): ✅ {response_time:.2f}s - {dim}维"
            else:
                error = await response.text()
                return f"Emb-{request_id}({model}): ❌ {response_time:.2f}s - {error[:30]}..."
                
    except Exception as e:
        response_time = time.time() - start_time
        return f"Emb-{request_id}({model}): ❌ {response_time:.2f}s - {str(e)[:30]}..."

async def test_ollama_concurrent():
    """测试Ollama并发能力"""
    print("🔍 Ollama并发测试 (直接API)")
    print("=" * 50)
    
    chat_messages = [
        "你好",
        "什么是AI？", 
        "解释机器学习",
        "深度学习应用",
        "NLP发展趋势",
        "计算机视觉",
        "强化学习",
        "神经网络原理"
    ]
    
    embedding_texts = [
        "Llamas eat bananas",
        "Machine learning",
        "Deep neural networks", 
        "Natural language processing",
        "Computer vision",
        "Reinforcement learning"
    ]
    
    async with aiohttp.ClientSession() as session:
        
        # 测试1: 纯Chat并发
        print(f"\n🧪 测试1: 纯Chat并发 (5个)")
        print(f"-" * 30)
        
        tasks = []
        start_time = time.time()
        
        for i in range(5):
            message = chat_messages[i]
            task = test_ollama_chat(session, message, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送5个Chat请求到Ollama...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        await asyncio.sleep(2)
        
        # 测试2: 纯Embedding并发
        print(f"\n🧪 测试2: 纯Embedding并发 (6个)")
        print(f"-" * 30)
        
        tasks = []
        start_time = time.time()
        
        for i in range(6):
            text = embedding_texts[i % len(embedding_texts)]
            model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
            task = test_ollama_embedding(session, text, model, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送6个Embedding请求到Ollama...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        await asyncio.sleep(2)
        
        # 测试3: 混合并发 (Chat + Embedding)
        print(f"\n🧪 测试3: 混合并发 (3个Chat + 4个Embedding)")
        print(f"-" * 30)
        
        tasks = []
        start_time = time.time()
        
        # 3个Chat
        for i in range(3):
            message = chat_messages[i]
            task = test_ollama_chat(session, message, i+1)
            tasks.append(task)
        
        # 4个Embedding
        for i in range(4):
            text = embedding_texts[i]
            model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
            task = test_ollama_embedding(session, text, model, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送7个混合请求到Ollama...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        await asyncio.sleep(2)
        
        # 测试4: 高并发Chat (测试是否阻塞)
        print(f"\n🧪 测试4: 高并发Chat (8个) - 测试阻塞")
        print(f"-" * 30)
        
        tasks = []
        start_time = time.time()
        
        for i in range(8):
            message = chat_messages[i]
            task = test_ollama_chat(session, message, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送8个Chat请求到Ollama...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 完成，总耗时: {total_time:.2f}s")
        
        success_count = 0
        for result in results:
            if isinstance(result, str):
                print(f"  {result}")
                if "✅" in result:
                    success_count += 1
            else:
                print(f"  异常: {result}")
        
        print(f"\n📊 高并发Chat结果: {success_count}/8 成功")

async def test_sequential_vs_concurrent():
    """对比顺序执行vs并发执行"""
    print(f"\n🔄 顺序 vs 并发对比测试")
    print(f"=" * 40)
    
    messages = ["你好", "什么是AI？", "解释机器学习"]
    
    async with aiohttp.ClientSession() as session:
        
        # 顺序执行
        print(f"\n📝 顺序执行 (3个Chat请求):")
        start_time = time.time()
        
        for i, message in enumerate(messages):
            result = await test_ollama_chat(session, message, i+1)
            print(f"  {result}")
        
        sequential_time = time.time() - start_time
        print(f"  顺序总耗时: {sequential_time:.2f}s")
        
        await asyncio.sleep(2)
        
        # 并发执行
        print(f"\n⚡ 并发执行 (3个Chat请求):")
        start_time = time.time()
        
        tasks = []
        for i, message in enumerate(messages):
            task = test_ollama_chat(session, message, i+1)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        
        for result in results:
            print(f"  {result}")
        
        print(f"  并发总耗时: {concurrent_time:.2f}s")
        
        # 分析
        print(f"\n📊 性能对比:")
        print(f"  顺序执行: {sequential_time:.2f}s")
        print(f"  并发执行: {concurrent_time:.2f}s")
        print(f"  性能提升: {sequential_time/concurrent_time:.1f}x")
        
        if concurrent_time < sequential_time * 0.8:
            print(f"  ✅ Ollama支持真正的并发处理")
        elif concurrent_time < sequential_time:
            print(f"  🟡 Ollama支持部分并发处理")
        else:
            print(f"  ❌ Ollama可能不支持并发，或存在阻塞")

async def main():
    """主函数"""
    print("🔍 Ollama并发能力测试")
    print("=" * 30)
    print(f"目标: {OLLAMA_URL}")
    
    await test_ollama_concurrent()
    await test_sequential_vs_concurrent()
    
    print(f"\n✅ Ollama测试完成!")
    print(f"\n💡 分析要点:")
    print(f"  - 如果并发比顺序快很多 → Ollama支持并发")
    print(f"  - 如果并发时间≈最慢单个请求 → 真正并发")
    print(f"  - 如果并发时间≈所有请求之和 → 串行处理")

if __name__ == "__main__":
    asyncio.run(main())
