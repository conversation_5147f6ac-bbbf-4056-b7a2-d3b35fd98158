#!/usr/bin/env python3
"""
简单的混合并发测试
"""

import asyncio
import aiohttp
import time
import json
import random
from collections import defaultdict

async def test_chat_request(session, request_id):
    """测试Chat请求"""
    payload = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": f"Hello {request_id}"}],
        "stream": False
    }
    
    start_time = time.time()
    try:
        async with session.post("http://localhost:8888/v1/chat/completions", 
                               json=payload, 
                               headers={"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"},
                               timeout=30) as response:
            response_time = time.time() - start_time
            if response.status == 200:
                await response.json()
                return {"success": True, "response_time": response_time, "status_code": 200}
            else:
                return {"success": False, "response_time": response_time, "status_code": response.status}
    except Exception as e:
        return {"success": False, "response_time": time.time() - start_time, "status_code": 0, "error": str(e)}

async def test_embedding_request(session, request_id):
    """测试Embedding请求"""
    payload = {
        "model": "hngpt-embedding",
        "input": f"test text {request_id}"
    }
    
    start_time = time.time()
    try:
        async with session.post("http://localhost:8888/v1/embeddings", 
                               json=payload, 
                               headers={"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"},
                               timeout=30) as response:
            response_time = time.time() - start_time
            if response.status == 200:
                await response.json()
                return {"success": True, "response_time": response_time, "status_code": 200}
            else:
                return {"success": False, "response_time": response_time, "status_code": response.status}
    except Exception as e:
        return {"success": False, "response_time": time.time() - start_time, "status_code": 0, "error": str(e)}

async def mixed_concurrent_test():
    """混合并发测试"""
    print("🎲 开始随机混合并发测试")
    
    # 生成随机请求序列
    request_types = ["chat", "embedding"]  # 暂时不包括OCR，因为它有问题
    total_requests = 12
    concurrent_level = 6
    
    request_sequence = []
    for i in range(total_requests):
        request_type = random.choice(request_types)
        request_sequence.append((request_type, i + 1))
    
    random.shuffle(request_sequence)
    
    print(f"📋 生成的随机请求序列:")
    type_counts = defaultdict(int)
    for req_type, req_id in request_sequence:
        type_counts[req_type] += 1
    
    for req_type, count in type_counts.items():
        print(f"  {req_type.upper()}: {count} 个请求")
    
    # 执行并发测试
    semaphore = asyncio.Semaphore(concurrent_level)
    
    async def execute_request(req_type, req_id):
        async with semaphore:
            session_timeout = aiohttp.ClientTimeout(total=60)
            async with aiohttp.ClientSession(timeout=session_timeout) as session:
                if req_type == "chat":
                    result = await test_chat_request(session, req_id)
                elif req_type == "embedding":
                    result = await test_embedding_request(session, req_id)
                
                result["request_type"] = req_type
                result["request_id"] = req_id
                return result
    
    # 创建所有任务
    tasks = []
    for req_type, req_id in request_sequence:
        task = execute_request(req_type, req_id)
        tasks.append(task)
    
    # 执行所有任务
    print(f"🚀 开始执行 {len(tasks)} 个混合并发请求...")
    start_time = time.time()
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    total_time = time.time() - start_time
    
    # 分析结果
    print(f"\n📊 测试结果分析:")
    print(f"⏱️  总执行时间: {total_time:.2f}s")
    print(f"🚀 平均QPS: {total_requests / total_time:.2f} 请求/秒")
    print(f"🎯 并发级别: {concurrent_level}")
    
    # 按类型统计
    type_stats = defaultdict(lambda: {"total": 0, "success": 0, "failed": 0, "response_times": []})
    
    for result in results:
        if isinstance(result, Exception):
            print(f"❌ 任务异常: {result}")
            continue
            
        req_type = result["request_type"]
        type_stats[req_type]["total"] += 1
        
        if result["success"]:
            type_stats[req_type]["success"] += 1
            type_stats[req_type]["response_times"].append(result["response_time"])
        else:
            type_stats[req_type]["failed"] += 1
    
    print(f"\n📈 各请求类型详细统计:")
    for req_type, stats in type_stats.items():
        success_rate = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
        avg_response_time = sum(stats["response_times"]) / len(stats["response_times"]) if stats["response_times"] else 0
        
        print(f"\n  {req_type.upper()}:")
        print(f"    总请求数: {stats['total']}")
        print(f"    成功数: {stats['success']}")
        print(f"    失败数: {stats['failed']}")
        print(f"    成功率: {success_rate:.1f}%")
        print(f"    平均响应时间: {avg_response_time:.3f}s")

if __name__ == "__main__":
    asyncio.run(mixed_concurrent_test())
