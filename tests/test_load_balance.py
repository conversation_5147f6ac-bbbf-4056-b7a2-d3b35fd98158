#!/usr/bin/env python3
"""
测试负载均衡脚本
用于验证GPU分配是否正确工作
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import List, Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LoadBalanceTest:
    def __init__(self, base_url: str = "http://localhost:8888", token: str = "startfrom2023"):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
    async def get_server_status(self) -> Dict:
        """获取服务器状态"""
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{self.base_url}/api/status", headers=self.headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"获取状态失败: {response.status}")
                        return {}
            except Exception as e:
                logger.error(f"获取状态异常: {e}")
                return {}
    
    async def send_chat_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """发送聊天请求"""
        payload = {
            "model": "hngpt-mini:latest",
            "messages": [
                {"role": "user", "content": f"请简单介绍一下人工智能，这是测试请求 #{request_id}"}
            ],
            "stream": False,
            "temperature": 0.7
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions", 
                                  headers=self.headers, 
                                  json=payload,
                                  timeout=aiohttp.ClientTimeout(total=60)) as response:
                end_time = time.time()
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "request_id": request_id,
                        "status": "success",
                        "response_time": end_time - start_time,
                        "server_info": result.get("server_info", "unknown")
                    }
                else:
                    return {
                        "request_id": request_id,
                        "status": "error",
                        "error_code": response.status,
                        "response_time": end_time - start_time
                    }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "status": "exception",
                "error": str(e),
                "response_time": end_time - start_time
            }
    
    async def send_embedding_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """发送嵌入请求"""
        payload = {
            "model": "hngpt-embedding",
            "input": f"这是测试文本 #{request_id}，用于测试嵌入模型的负载均衡"
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/embeddings", 
                                  headers=self.headers, 
                                  json=payload,
                                  timeout=aiohttp.ClientTimeout(total=30)) as response:
                end_time = time.time()
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "request_id": request_id,
                        "status": "success",
                        "response_time": end_time - start_time,
                        "embedding_length": len(result.get("data", [{}])[0].get("embedding", []))
                    }
                else:
                    return {
                        "request_id": request_id,
                        "status": "error",
                        "error_code": response.status,
                        "response_time": end_time - start_time
                    }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "status": "exception",
                "error": str(e),
                "response_time": end_time - start_time
            }
    
    async def run_concurrent_test(self, num_requests: int = 20, request_type: str = "chat"):
        """运行并发测试"""
        logger.info(f"开始并发测试: {num_requests} 个 {request_type} 请求")
        
        # 获取初始状态
        initial_status = await self.get_server_status()
        logger.info("初始服务器状态:")
        self.print_server_status(initial_status)
        
        # 发送并发请求
        async with aiohttp.ClientSession() as session:
            if request_type == "chat":
                tasks = [self.send_chat_request(session, i) for i in range(num_requests)]
            else:
                tasks = [self.send_embedding_request(session, i) for i in range(num_requests)]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 分析结果
        success_count = sum(1 for r in results if isinstance(r, dict) and r.get("status") == "success")
        error_count = len(results) - success_count
        
        if success_count > 0:
            avg_response_time = sum(r.get("response_time", 0) for r in results 
                                  if isinstance(r, dict) and r.get("status") == "success") / success_count
        else:
            avg_response_time = 0
        
        logger.info(f"测试结果: 成功 {success_count}/{num_requests}, 平均响应时间: {avg_response_time:.2f}s")
        
        # 获取最终状态
        await asyncio.sleep(2)  # 等待状态更新
        final_status = await self.get_server_status()
        logger.info("最终服务器状态:")
        self.print_server_status(final_status)
        
        return results
    
    def print_server_status(self, status: Dict):
        """打印服务器状态"""
        if not status:
            logger.warning("无法获取服务器状态")
            return

        gpu_info = status.get("gpu_info", [])
        ollama_servers = status.get("ollama_servers", [])

        logger.info("=" * 80)
        logger.info("GPU硬件状态:")
        for gpu in gpu_info:
            gpu_id = gpu.get("gpu_id", "?")
            gpu_name = gpu.get("name", "Unknown")
            utilization = gpu.get("utilization_percent", 0)
            memory_usage = gpu.get("memory_usage_percent", 0)
            chat_tasks = gpu.get("llm_chat_tasks", 0)
            embed_tasks = gpu.get("llm_embedding_tasks", 0)
            ocr_tasks = gpu.get("ocr_tasks", 0)

            # 状态图标
            if utilization < 10 and memory_usage < 30:
                status_icon = "🟢"  # 空闲
            elif utilization < 50 and memory_usage < 70:
                status_icon = "🟡"  # 中等负载
            else:
                status_icon = "🔴"  # 高负载

            logger.info(f"  {status_icon} GPU {gpu_id} ({gpu_name}): "
                       f"使用率={utilization:.1f}%, 内存={memory_usage:.1f}%, "
                       f"任务[Chat={chat_tasks}, Embed={embed_tasks}, OCR={ocr_tasks}]")

        logger.info("Ollama服务器状态:")
        for server in ollama_servers:
            name = server.get("name", "unknown")
            url = server.get("url", "unknown")
            healthy = "✅" if server.get("healthy", False) else "❌"
            chat_healthy = "✅" if server.get("chat_healthy", False) else "❌"
            embed_healthy = "✅" if server.get("embeddings_healthy", False) else "❌"
            logger.info(f"  {name} ({url}): 总体={healthy}, Chat={chat_healthy}, Embed={embed_healthy}")
        logger.info("=" * 80)

async def main():
    """主函数"""
    test = LoadBalanceTest()
    
    # 测试聊天请求负载均衡
    logger.info("🚀 开始测试聊天请求负载均衡")
    chat_results = await test.run_concurrent_test(num_requests=16, request_type="chat")
    
    await asyncio.sleep(5)  # 等待任务完成
    
    # 测试嵌入请求负载均衡
    logger.info("🚀 开始测试嵌入请求负载均衡")
    embed_results = await test.run_concurrent_test(num_requests=12, request_type="embedding")
    
    # 打印详细错误信息
    logger.info("详细错误信息:")
    all_results = chat_results + embed_results
    for result in all_results:
        if isinstance(result, dict) and result.get("status") != "success":
            logger.error(f"请求 {result.get('request_id')} 失败: {result}")

if __name__ == "__main__":
    asyncio.run(main())
