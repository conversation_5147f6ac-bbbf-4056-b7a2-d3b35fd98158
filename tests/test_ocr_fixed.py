#!/usr/bin/env python3
"""
测试修复后的OCR功能
"""

import requests
import time

def test_ocr_fixed():
    """测试修复后的OCR功能"""
    print("🧪 测试修复后的OCR binary端点")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
        
        print(f"📄 测试图片: {test_image_path}")
        print(f"📏 图片大小: {len(image_data)} 字节")
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        # 测试同步模式，合理的超时时间
        params = {
            "wait": "true",
            "timeout": "60.0"  # 60秒应该足够了
        }
        
        print(f"\n🚀 发送OCR binary请求（同步模式，60秒超时）...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params,
            timeout=90  # HTTP超时稍长一些
        )
        
        response_time = time.time() - start_time
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ OCR成功!")
                print(f"📝 结果类型: {type(result)}")
                
                if isinstance(result, dict):
                    print(f"📝 结果键: {list(result.keys())}")
                    
                    # 显示识别的文本（如果有）
                    if 'text' in result:
                        text = str(result['text'])
                        if len(text) > 200:
                            text = text[:200] + "..."
                        print(f"📝 识别文本: {text}")
                    
                    # 显示其他有用信息
                    if 'layout_result' in result:
                        layout = result['layout_result']
                        if isinstance(layout, dict) and 'detected_regions' in layout:
                            regions = layout['detected_regions']
                            print(f"📝 检测到 {len(regions)} 个区域")
                    
                else:
                    print(f"📝 完整结果: {result}")
                    
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
                
        elif response.status_code == 408:
            print(f"⏰ OCR超时（60秒）")
            try:
                result = response.json()
                print(f"📝 超时信息: {result}")
            except:
                print(f"📄 响应: {response.text}")
                
        else:
            print(f"❌ OCR失败!")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_ocr_async():
    """测试异步模式"""
    print(f"\n{'='*60}")
    print("🧪 测试OCR异步模式")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        # 异步模式
        params = {
            "wait": "false",
            "timeout": "60.0"
        }
        
        print(f"🚀 发送OCR binary请求（异步模式）...")
        
        response = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params,
            timeout=10
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 202:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ OCR任务启动成功!")
            print(f"📝 任务ID: {task_id}")
            print(f"📝 状态: {result.get('status')}")
            
            # 简单等待几秒看看任务是否完成
            if task_id:
                print(f"\n⏳ 等待5秒后检查任务状态...")
                time.sleep(5)
                
                result_response = requests.get(
                    f"http://localhost:8888/ocr/result/{task_id}",
                    headers={"Authorization": "Bearer startfrom2023"},
                    timeout=10
                )
                
                print(f"📊 任务状态查询: {result_response.status_code}")
                if result_response.status_code == 200:
                    task_result = result_response.json()
                    print(f"📝 任务状态: {task_result.get('status')}")
                    print(f"📝 进度: {task_result.get('progress', 0)}%")
                    
                    if task_result.get('status') == 'completed':
                        print(f"✅ 任务已完成!")
                    elif task_result.get('status') == 'processing':
                        print(f"🔄 任务仍在处理中...")
                else:
                    print(f"❌ 无法获取任务状态: {result_response.text}")
        else:
            print(f"❌ 异步任务启动失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 异步测试异常: {e}")

if __name__ == "__main__":
    test_ocr_fixed()
    test_ocr_async()
