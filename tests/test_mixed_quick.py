#!/usr/bin/env python3
"""
快速测试混合并发功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_concurrent_capacity import ConcurrencyTester

async def quick_test():
    """快速测试混合并发功能"""
    print("🧪 快速测试混合并发功能")
    
    tester = ConcurrencyTester()
    
    # 小规模测试：10个请求，并发级别5
    results = await tester.test_mixed_concurrent_requests(
        total_requests=10,
        concurrent_level=5
    )
    
    print(f"\n📊 测试完成!")
    print(f"总请求数: {results['total_requests']}")
    print(f"并发级别: {results['concurrent_level']}")
    print(f"总耗时: {results['total_time']:.2f}s")
    
    # 统计成功率
    success_count = sum(1 for r in results['results'] if r['success'])
    success_rate = (success_count / len(results['results'])) * 100
    print(f"成功率: {success_rate:.1f}%")

if __name__ == "__main__":
    asyncio.run(quick_test())
