#!/usr/bin/env python3
"""
测试OCR异步模式
"""

import requests
import time
import json

def test_ocr_async():
    """测试OCR异步模式"""
    print("🧪 测试OCR异步模式")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
        
        print(f"📄 测试图片: {test_image_path}")
        print(f"📏 图片大小: {len(image_data)} 字节")
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        # 使用异步模式
        params = {
            "wait": "false",  # 异步模式
            "timeout": "120.0"  # 2分钟超时
        }
        
        print(f"\n🚀 发送OCR binary请求（异步模式）...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params,
            timeout=10  # HTTP请求超时短一些，因为是异步
        )
        
        response_time = time.time() - start_time
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        
        if response.status_code == 202:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ OCR任务启动成功!")
            print(f"📝 任务ID: {task_id}")
            print(f"📝 状态: {result.get('status')}")
            
            # 轮询检查任务状态
            if task_id:
                print(f"\n🔄 开始轮询任务状态...")
                max_polls = 30  # 最多轮询30次
                poll_interval = 5  # 每5秒轮询一次
                
                for i in range(max_polls):
                    time.sleep(poll_interval)
                    
                    # 检查任务结果
                    result_response = requests.get(
                        f"http://localhost:8888/ocr/result/{task_id}",
                        headers={"Authorization": "Bearer startfrom2023"},
                        timeout=10
                    )
                    
                    print(f"📊 轮询 {i+1}/{max_polls}: 状态码 {result_response.status_code}")
                    
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        status = result_data.get('status', 'unknown')
                        progress = result_data.get('progress', 0)
                        
                        print(f"  状态: {status}, 进度: {progress}%")
                        
                        if status == 'completed':
                            print(f"✅ OCR任务完成!")
                            # 显示部分结果
                            if 'result' in result_data:
                                ocr_result = result_data['result']
                                if isinstance(ocr_result, dict) and 'text' in ocr_result:
                                    text = str(ocr_result['text'])[:200] + "..." if len(str(ocr_result['text'])) > 200 else ocr_result['text']
                                    print(f"📝 识别文本: {text}")
                                else:
                                    print(f"📝 结果类型: {type(ocr_result)}")
                            break
                        elif status == 'failed':
                            print(f"❌ OCR任务失败: {result_data.get('error', '未知错误')}")
                            break
                        elif status == 'timeout':
                            print(f"⏰ OCR任务超时")
                            break
                    else:
                        print(f"  获取结果失败: {result_response.text}")
                        
                else:
                    print(f"⏰ 轮询超时，任务可能仍在处理中")
                    
        else:
            print(f"❌ OCR任务启动失败!")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_ocr_sync_long():
    """测试OCR同步模式（长超时）"""
    print("\n" + "="*60)
    print("🧪 测试OCR同步模式（长超时）")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            image_data = image_file.read()
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        # 使用同步模式，长超时
        params = {
            "wait": "true",
            "timeout": "120.0"  # 2分钟超时
        }
        
        print(f"🚀 发送OCR binary请求（同步模式，2分钟超时）...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8888/ocr/binary/",
            data=image_data,
            headers=headers,
            params=params,
            timeout=150  # HTTP请求超时2.5分钟
        )
        
        response_time = time.time() - start_time
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ OCR成功!")
                if isinstance(result, dict) and 'text' in result:
                    text = str(result['text'])[:200] + "..." if len(str(result['text'])) > 200 else result['text']
                    print(f"📝 识别文本: {text}")
                else:
                    print(f"📝 结果类型: {type(result)}")
                    print(f"📝 结果键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
        elif response.status_code == 408:
            print(f"⏰ OCR超时（2分钟）")
            print(f"📄 响应: {response.text}")
        else:
            print(f"❌ OCR失败!")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ocr_async()
    test_ocr_sync_long()
