#!/usr/bin/env python3
"""
简单的并发测试
"""

import asyncio
import aiohttp
import time

async def test_chat():
    """测试Chat请求"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"}
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": False
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=data, headers=headers, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    return "✅ 成功"
                else:
                    return f"❌ HTTP {response.status}"
        except Exception as e:
            return f"❌ 异常: {str(e)}"

async def test_embedding():
    """测试Embedding请求"""
    url = "http://localhost:8888/v1/embeddings"
    headers = {"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"}
    data = {
        "model": "hngpt-embedding",
        "input": "test text"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=data, headers=headers, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    return "✅ 成功"
                else:
                    return f"❌ HTTP {response.status}"
        except Exception as e:
            return f"❌ 异常: {str(e)}"

async def main():
    print("🧪 简单并发测试")
    print("=" * 30)
    
    # 测试2个并发Chat请求
    print("\n📝 测试2个并发Chat请求:")
    start_time = time.time()
    chat_tasks = [test_chat() for _ in range(2)]
    chat_results = await asyncio.gather(*chat_tasks)
    chat_time = time.time() - start_time
    
    for i, result in enumerate(chat_results, 1):
        print(f"  Chat {i}: {result}")
    print(f"  总耗时: {chat_time:.2f}s")
    
    # 测试6个并发Embedding请求
    print("\n🔤 测试6个并发Embedding请求:")
    start_time = time.time()
    embed_tasks = [test_embedding() for _ in range(6)]
    embed_results = await asyncio.gather(*embed_tasks)
    embed_time = time.time() - start_time
    
    for i, result in enumerate(embed_results, 1):
        print(f"  Embedding {i}: {result}")
    print(f"  总耗时: {embed_time:.2f}s")

if __name__ == "__main__":
    asyncio.run(main())
