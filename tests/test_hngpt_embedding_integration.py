#!/usr/bin/env python3
"""
测试app.py中hngpt-embedding模型的集成
验证指令感知、归一化等功能是否正常工作
"""

import requests
import json
import numpy as np
from typing import List, Dict, Any
import time

class HngptEmbeddingTester:
    def __init__(self, base_url: str = "http://localhost:8888", token: str = "startfrom2023"):
        self.base_url = base_url
        self.token = token
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        if not vec1 or not vec2:
            return 0.0
        
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        # 归一化
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)
        
        return float(np.dot(vec1_norm, vec2_norm))
    
    def test_basic_embedding(self, model: str = "hngpt-embedding"):
        """测试基本embedding功能"""
        print(f"\n🔍 测试基本embedding功能 (模型: {model})")
        
        test_text = "人工智能是计算机科学的一个重要分支"
        
        try:
            response = requests.post(
                f"{self.base_url}/v1/embeddings",
                headers=self.headers,
                json={
                    "model": model,
                    "input": test_text
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                
                print(f"  ✅ 成功生成embedding")
                print(f"  - 维度: {len(embedding)}")
                print(f"  - 范围: [{min(embedding):.4f}, {max(embedding):.4f}]")
                print(f"  - L2范数: {np.linalg.norm(embedding):.4f}")
                
                return embedding
            else:
                print(f"  ❌ 失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            return None
    
    def test_instruction_awareness(self, model: str = "hngpt-embedding"):
        """测试指令感知功能"""
        print(f"\n🧠 测试指令感知功能 (模型: {model})")
        
        test_text = "深度学习在图像识别中的应用"
        
        # 测试不同任务类型
        tasks = [
            ("text_search", "语义搜索"),
            ("document_retrieval", "文档检索"),
            ("classification", "分类任务"),
            ("similarity", "相似度计算")
        ]
        
        embeddings = {}
        
        for task_type, task_desc in tasks:
            try:
                response = requests.post(
                    f"{self.base_url}/v1/embeddings",
                    headers=self.headers,
                    json={
                        "model": model,
                        "input": test_text,
                        "task": task_type
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embedding = result["data"][0]["embedding"]
                    embeddings[task_type] = embedding
                    
                    print(f"  ✅ {task_desc} ({task_type}): {len(embedding)}维")
                else:
                    print(f"  ❌ {task_desc} 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ {task_desc} 异常: {e}")
        
        # 比较不同任务类型的embedding差异
        if len(embeddings) >= 2:
            print(f"\n  📊 任务类型间的相似度:")
            task_list = list(embeddings.keys())
            for i in range(len(task_list)):
                for j in range(i + 1, len(task_list)):
                    task1, task2 = task_list[i], task_list[j]
                    sim = self.cosine_similarity(embeddings[task1], embeddings[task2])
                    print(f"    {task1} vs {task2}: {sim:.4f}")
        
        return embeddings
    
    def test_custom_instruction(self, model: str = "hngpt-embedding"):
        """测试自定义指令功能"""
        print(f"\n📝 测试自定义指令功能 (模型: {model})")
        
        test_text = "机器学习算法的性能评估"
        
        # 测试自定义指令
        custom_instruction = "Represent this text for academic research and analysis:"
        
        try:
            response = requests.post(
                f"{self.base_url}/v1/embeddings",
                headers=self.headers,
                json={
                    "model": model,
                    "input": test_text,
                    "instruction": custom_instruction
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                
                print(f"  ✅ 自定义指令成功")
                print(f"  - 维度: {len(embedding)}")
                print(f"  - 指令: {custom_instruction}")
                
                return embedding
            else:
                print(f"  ❌ 失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            return None
    
    def test_model_comparison(self):
        """对比不同模型的表现"""
        print(f"\n⚖️  模型对比测试")
        
        test_text = "自然语言处理技术的发展趋势"
        models = ["hngpt-embedding", "hngpt-embedding"]
        
        results = {}
        
        for model in models:
            print(f"\n  🔍 测试模型: {model}")
            
            try:
                response = requests.post(
                    f"{self.base_url}/v1/embeddings",
                    headers=self.headers,
                    json={
                        "model": model,
                        "input": test_text,
                        "task": "text_search"
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embedding = result["data"][0]["embedding"]
                    
                    results[model] = {
                        "embedding": embedding,
                        "dimensions": len(embedding),
                        "l2_norm": np.linalg.norm(embedding),
                        "success": True
                    }
                    
                    print(f"    ✅ 成功: {len(embedding)}维, L2范数: {results[model]['l2_norm']:.4f}")
                else:
                    results[model] = {"success": False, "error": response.text}
                    print(f"    ❌ 失败: {response.status_code}")
                    
            except Exception as e:
                results[model] = {"success": False, "error": str(e)}
                print(f"    ❌ 异常: {e}")
        
        # 对比分析
        successful_models = [m for m, r in results.items() if r.get("success")]
        
        if len(successful_models) >= 2:
            print(f"\n  📊 模型间相似度:")
            for i in range(len(successful_models)):
                for j in range(i + 1, len(successful_models)):
                    model1, model2 = successful_models[i], successful_models[j]
                    emb1 = results[model1]["embedding"]
                    emb2 = results[model2]["embedding"]
                    sim = self.cosine_similarity(emb1, emb2)
                    print(f"    {model1} vs {model2}: {sim:.4f}")
        
        return results
    
    def test_normalization_check(self, model: str = "hngpt-embedding"):
        """检查归一化是否正确应用"""
        print(f"\n📏 测试归一化功能 (模型: {model})")
        
        test_texts = [
            "短文本",
            "这是一个中等长度的测试文本，用于验证归一化功能是否正常工作。",
            "这是一个相对较长的测试文本，包含更多的词汇和语义信息，用于测试embedding模型在处理不同长度文本时的归一化表现，确保所有生成的向量都具有单位长度。"
        ]
        
        for i, text in enumerate(test_texts):
            try:
                response = requests.post(
                    f"{self.base_url}/v1/embeddings",
                    headers=self.headers,
                    json={
                        "model": model,
                        "input": text
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embedding = result["data"][0]["embedding"]
                    l2_norm = np.linalg.norm(embedding)
                    
                    print(f"  文本{i+1} (长度: {len(text)}): L2范数 = {l2_norm:.6f}")
                    
                    # 检查是否接近单位长度
                    if abs(l2_norm - 1.0) < 0.001:
                        print(f"    ✅ 归一化正确")
                    else:
                        print(f"    ⚠️  可能未归一化或归一化不准确")
                else:
                    print(f"  文本{i+1} 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"  文本{i+1} 异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始hngpt-embedding集成测试")
        print("=" * 60)
        
        # 基本功能测试
        self.test_basic_embedding("hngpt-embedding")
        self.test_basic_embedding("hngpt-embedding")
        
        # 指令感知测试
        self.test_instruction_awareness("hngpt-embedding")
        
        # 自定义指令测试
        self.test_custom_instruction("hngpt-embedding")
        
        # 模型对比测试
        self.test_model_comparison()
        
        # 归一化检查
        self.test_normalization_check("hngpt-embedding")
        self.test_normalization_check("hngpt-embedding")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成!")
        print("\n💡 测试说明:")
        print("- hngpt-embedding应该支持指令感知和自动归一化")
        print("- hngpt-embedding应该是简单的embedding，无特殊处理")
        print("- 归一化的向量L2范数应该接近1.0")
        print("- 不同任务类型应该产生略有差异的embedding")

def main():
    """主函数"""
    print("hngpt-embedding 集成测试工具")
    print("=" * 40)
    
    # 配置
    base_url = "http://localhost:8888"
    token = input("请输入认证token (或直接回车使用默认): ").strip()
    if not token:
        token = "your_token_here"  # 需要替换为实际token
    
    print(f"服务地址: {base_url}")
    print(f"认证token: {token[:10]}...")
    
    tester = HngptEmbeddingTester(base_url=base_url, token=token)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
