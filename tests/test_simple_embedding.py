#!/usr/bin/env python3
"""
简化的embedding测试脚本
测试hngpt-embedding和hngpt-embedding的基本功能
"""

import requests
import numpy as np

def test_embedding(model="hngpt-embedding", token="your_token_here"):
    """测试embedding功能"""
    
    base_url = "http://localhost:8888"
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"🔍 测试模型: {model}")
    
    # 测试文本
    test_text = "人工智能是计算机科学的一个重要分支"
    
    try:
        response = requests.post(
            f"{base_url}/v1/embeddings",
            headers=headers,
            json={
                "model": model,
                "input": test_text
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            embedding = result["data"][0]["embedding"]
            
            # 计算L2范数
            l2_norm = np.linalg.norm(embedding)
            
            print(f"  ✅ 成功")
            print(f"  - 维度: {len(embedding)}")
            print(f"  - L2范数: {l2_norm:.4f}")
            print(f"  - 范围: [{min(embedding):.4f}, {max(embedding):.4f}]")
            
            # 检查是否归一化
            if abs(l2_norm - 1.0) < 0.01:
                print(f"  - 状态: 已归一化 ✅")
            else:
                print(f"  - 状态: 未归一化")
                
            return embedding
        else:
            print(f"  ❌ 失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"  ❌ 异常: {e}")
        return None

def compare_models(token="your_token_here"):
    """对比两个模型"""
    print("\n📊 模型对比")
    print("=" * 40)
    
    # 测试两个模型
    hngpt_emb = test_embedding("hngpt-embedding", token)
    print()
    hngpt-embedding_emb = test_embedding("hngpt-embedding", token)
    
    # 计算相似度
    if hngpt_emb and hngpt-embedding_emb:
        # 归一化向量
        hngpt_norm = np.array(hngpt_emb) / np.linalg.norm(hngpt_emb)
        hngpt-embedding_norm = np.array(hngpt-embedding_emb) / np.linalg.norm(hngpt-embedding_emb)
        
        # 计算余弦相似度
        similarity = np.dot(hngpt_norm, hngpt-embedding_norm)
        
        print(f"\n🔗 模型间相似度: {similarity:.4f}")
        
        if similarity > 0.8:
            print("  - 两个模型结果相似")
        elif similarity > 0.6:
            print("  - 两个模型结果有一定差异")
        else:
            print("  - 两个模型结果差异较大")

def main():
    """主函数"""
    print("🚀 简化Embedding测试")
    print("=" * 30)
    
    # 获取token
    token = input("请输入认证token (或直接回车): ").strip()
    if not token:
        token = "your_token_here"  # 替换为实际token
    
    # 运行测试
    compare_models(token)
    
    print(f"\n✅ 测试完成!")
    print(f"\n💡 说明:")
    print(f"- hngpt-embedding: 自动添加指令 + 归一化")
    print(f"- hngpt-embedding: 简单处理，无特殊优化")

if __name__ == "__main__":
    main()
