# HNGPT-AI 更新包制作与部署（统一使用 update.py）

## 制作更新包

- 需在项目根目录下、并使用 sudo/root 运行（读取系统 /usr 或 /usr/local 中的 ollama）：

```bash
sudo python update.py
```

- 默认会将以下项目内容打包到 workspace/hngpt/ 前缀下：
  - app.py, app.conf, authorized_users.txt, compile.py
  - static/
  - models/hngpt, models/hngpt.modelfile
  - models/hngpt-embedding, models/hngpt-embedding.modelfile

- 同时会从本机系统中采集已安装的 Ollama：
  - 优先从 /usr/bin/ollama 和 /usr/lib/ollama
  - 其次从 /usr/local/bin/ollama 和 /usr/local/lib/ollama
  - 打包目标统一写入 /usr/bin/ollama 与 /usr/lib/ollama

> 如需改打包范围，编辑 update.py 中的 project_paths。

## 目标机部署步骤

```bash
# 停止所有服务
sudo supervisorctl stop all
sudo pkill -f ollama

# 备份旧版本（可选）
sudo cp -r /workspace/hngpt /workspace/hngpt.backup.$(date +%Y%m%d_%H%M%S)
sudo cp -r /usr/lib/ollama /usr/lib/ollama.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
sudo cp -r /usr/local/lib/ollama /usr/local/lib/ollama.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# 预清理旧版本（重要，避免旧库残留）
sudo rm -f /usr/bin/ollama /usr/local/bin/ollama
sudo rm -rf /usr/lib/ollama /usr/local/lib/ollama

# 解压更新包到根目录
sudo tar -xzvf update.tar.gz -C /

# 设置权限
sudo chmod +x /usr/bin/ollama

# 启动服务
sudo supervisorctl start all
```

## 说明

- 本流程采用“统一部署到 /usr（/usr/bin 和 /usr/lib）”的策略，与官方手工安装一致。
- 本机如果未安装 Ollama，请先手工安装：

```bash
curl -LO https://ollama.com/download/ollama-linux-amd64.tgz
sudo tar -C /usr -xzf ollama-linux-amd64.tgz
```

- 如果后续需要从官方 tgz 打包（而不是采集自系统），update.py 也包含 download_ollama 与 create_update_package_with_ollama，可按需切换。

