#!/usr/bin/env python3
"""
并发测试脚本：测试app.py中的/v1/chat/completions和/v1/embeddings接口
检查在高并发情况下是否会出现阻塞
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any
import statistics
from datetime import datetime

class ConcurrentAPITester:
    def __init__(self, base_url: str = "http://localhost:8888", token: str = "startfrom2023"):
        self.base_url = base_url
        self.token = token
        self.headers = {"Authorization": f"Bearer {token}"}
        
        # 测试数据
        self.chat_messages = [
            "你好，请介绍一下人工智能",
            "什么是机器学习？",
            "深度学习有哪些应用？",
            "请解释一下神经网络",
            "自然语言处理的发展趋势如何？"
        ]
        
        self.embedding_texts = [
            "Llamas eat bananas",
            "Llamas in pyjamas", 
            "A bowl of fruit salad",
            "A sleeping dress",
            "Machine learning algorithms"
        ]
    
    async def call_chat_api(self, session: aiohttp.ClientSession, message: str, request_id: int) -> Dict[str, Any]:
        """调用chat completions API"""
        start_time = time.time()
        
        payload = {
            "model": "hngpt",
            "messages": [
                {"role": "user", "content": message}
            ],
            "max_tokens": 100,
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            async with session.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                headers=self.headers,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    return {
                        "request_id": request_id,
                        "api": "chat",
                        "success": True,
                        "response_time": response_time,
                        "status_code": response.status,
                        "content_length": len(content),
                        "message": message[:30] + "..." if len(message) > 30 else message
                    }
                else:
                    error_text = await response.text()
                    return {
                        "request_id": request_id,
                        "api": "chat",
                        "success": False,
                        "response_time": response_time,
                        "status_code": response.status,
                        "error": error_text,
                        "message": message[:30] + "..." if len(message) > 30 else message
                    }
                    
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "api": "chat",
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e),
                "message": message[:30] + "..." if len(message) > 30 else message
            }
    
    async def call_embedding_api(self, session: aiohttp.ClientSession, text: str, model: str, request_id: int) -> Dict[str, Any]:
        """调用embeddings API"""
        start_time = time.time()
        
        payload = {
            "model": model,
            "input": text,
            "encoding_format": "float"
        }
        
        try:
            async with session.post(
                f"{self.base_url}/v1/embeddings",
                json=payload,
                headers=self.headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    embedding = result["data"][0]["embedding"]
                    
                    return {
                        "request_id": request_id,
                        "api": "embedding",
                        "model": model,
                        "success": True,
                        "response_time": response_time,
                        "status_code": response.status,
                        "embedding_dim": len(embedding),
                        "text": text[:30] + "..." if len(text) > 30 else text
                    }
                else:
                    error_text = await response.text()
                    return {
                        "request_id": request_id,
                        "api": "embedding",
                        "model": model,
                        "success": False,
                        "response_time": response_time,
                        "status_code": response.status,
                        "error": error_text,
                        "text": text[:30] + "..." if len(text) > 30 else text
                    }
                    
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "api": "embedding",
                "model": model,
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e),
                "text": text[:30] + "..." if len(text) > 30 else text
            }
    
    async def run_concurrent_test(self, chat_concurrent: int = 3, embedding_concurrent: int = 5, rounds: int = 2):
        """运行并发测试"""
        print(f"🚀 并发API测试开始")
        print(f"=" * 60)
        print(f"配置:")
        print(f"  - Chat并发数: {chat_concurrent}")
        print(f"  - Embedding并发数: {embedding_concurrent}")
        print(f"  - 测试轮数: {rounds}")
        print(f"  - 总请求数: {(chat_concurrent + embedding_concurrent) * rounds}")
        
        all_results = []
        
        async with aiohttp.ClientSession() as session:
            for round_num in range(rounds):
                print(f"\n🔄 第 {round_num + 1} 轮测试")
                print(f"-" * 40)
                
                tasks = []
                request_id = round_num * 100
                
                # 创建chat任务
                for i in range(chat_concurrent):
                    message = self.chat_messages[i % len(self.chat_messages)]
                    task = self.call_chat_api(session, message, request_id + i)
                    tasks.append(task)
                
                # 创建embedding任务 (测试两个模型)
                for i in range(embedding_concurrent):
                    text = self.embedding_texts[i % len(self.embedding_texts)]
                    model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
                    task = self.call_embedding_api(session, text, model, request_id + chat_concurrent + i)
                    tasks.append(task)
                
                # 记录开始时间
                round_start = time.time()
                print(f"  📤 发送 {len(tasks)} 个并发请求...")
                
                # 并发执行所有任务
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                round_end = time.time()
                round_time = round_end - round_start
                
                # 处理结果
                valid_results = []
                for result in results:
                    if isinstance(result, dict):
                        valid_results.append(result)
                    else:
                        print(f"  ❌ 异常结果: {result}")
                
                all_results.extend(valid_results)
                
                # 分析本轮结果
                self.analyze_round_results(valid_results, round_num + 1, round_time)
                
                # 轮次间隔
                if round_num < rounds - 1:
                    await asyncio.sleep(1)
        
        # 总体分析
        self.analyze_overall_results(all_results)
    
    def analyze_round_results(self, results: List[Dict], round_num: int, round_time: float):
        """分析单轮结果"""
        successful = [r for r in results if r.get("success", False)]
        failed = [r for r in results if not r.get("success", False)]
        
        chat_results = [r for r in results if r.get("api") == "chat"]
        embedding_results = [r for r in results if r.get("api") == "embedding"]
        
        print(f"  📊 第{round_num}轮结果:")
        print(f"    总耗时: {round_time:.2f}s")
        print(f"    成功: {len(successful)}/{len(results)}")
        print(f"    失败: {len(failed)}")
        
        if successful:
            response_times = [r["response_time"] for r in successful]
            print(f"    平均响应时间: {statistics.mean(response_times):.2f}s")
            print(f"    最快响应: {min(response_times):.2f}s")
            print(f"    最慢响应: {max(response_times):.2f}s")
        
        # 按API类型分析
        if chat_results:
            chat_success = len([r for r in chat_results if r.get("success")])
            print(f"    Chat API: {chat_success}/{len(chat_results)} 成功")
        
        if embedding_results:
            emb_success = len([r for r in embedding_results if r.get("success")])
            print(f"    Embedding API: {emb_success}/{len(embedding_results)} 成功")
        
        # 显示失败的请求
        if failed:
            print(f"    ❌ 失败请求:")
            for fail in failed[:3]:  # 只显示前3个
                error = fail.get("error", "未知错误")
                api = fail.get("api", "未知")
                print(f"      {api}: {error[:50]}...")
    
    def analyze_overall_results(self, all_results: List[Dict]):
        """分析总体结果"""
        print(f"\n📈 总体测试结果分析")
        print(f"=" * 60)
        
        successful = [r for r in all_results if r.get("success", False)]
        failed = [r for r in all_results if not r.get("success", False)]
        
        chat_results = [r for r in all_results if r.get("api") == "chat"]
        embedding_results = [r for r in all_results if r.get("api") == "embedding"]
        
        print(f"📊 基本统计:")
        print(f"  总请求数: {len(all_results)}")
        print(f"  成功请求: {len(successful)} ({len(successful)/len(all_results)*100:.1f}%)")
        print(f"  失败请求: {len(failed)} ({len(failed)/len(all_results)*100:.1f}%)")
        
        if successful:
            response_times = [r["response_time"] for r in successful]
            print(f"\n⏱️ 响应时间分析:")
            print(f"  平均响应时间: {statistics.mean(response_times):.3f}s")
            print(f"  中位数响应时间: {statistics.median(response_times):.3f}s")
            print(f"  最快响应: {min(response_times):.3f}s")
            print(f"  最慢响应: {max(response_times):.3f}s")
            print(f"  标准差: {statistics.stdev(response_times):.3f}s")
        
        # API类型分析
        print(f"\n🔍 API类型分析:")
        
        if chat_results:
            chat_successful = [r for r in chat_results if r.get("success")]
            chat_times = [r["response_time"] for r in chat_successful]
            print(f"  Chat API:")
            print(f"    成功率: {len(chat_successful)}/{len(chat_results)} ({len(chat_successful)/len(chat_results)*100:.1f}%)")
            if chat_times:
                print(f"    平均响应时间: {statistics.mean(chat_times):.3f}s")
        
        if embedding_results:
            emb_successful = [r for r in embedding_results if r.get("success")]
            emb_times = [r["response_time"] for r in emb_successful]
            print(f"  Embedding API:")
            print(f"    成功率: {len(emb_successful)}/{len(embedding_results)} ({len(emb_successful)/len(embedding_results)*100:.1f}%)")
            if emb_times:
                print(f"    平均响应时间: {statistics.mean(emb_times):.3f}s")
        
        # 模型分析
        hngpt_emb = [r for r in embedding_results if r.get("model") == "hngpt-embedding" and r.get("success")]
        hngpt-embedding_emb = [r for r in embedding_results if r.get("model") == "hngpt-embedding" and r.get("success")]
        
        if hngpt_emb or hngpt-embedding_emb:
            print(f"\n🤖 Embedding模型对比:")
            if hngpt_emb:
                hngpt_times = [r["response_time"] for r in hngpt_emb]
                print(f"  hngpt-embedding: {len(hngpt_emb)}次成功, 平均 {statistics.mean(hngpt_times):.3f}s")
            if hngpt-embedding_emb:
                hngpt-embedding_times = [r["response_time"] for r in hngpt-embedding_emb]
                print(f"  hngpt-embedding: {len(hngpt-embedding_emb)}次成功, 平均 {statistics.mean(hngpt-embedding_times):.3f}s")
        
        # 阻塞分析
        print(f"\n🚦 阻塞分析:")
        if successful:
            slow_requests = [r for r in successful if r["response_time"] > 10]
            if slow_requests:
                print(f"  ⚠️ 发现 {len(slow_requests)} 个慢请求 (>10s)")
                for slow in slow_requests[:3]:
                    api = slow.get("api", "未知")
                    time_taken = slow["response_time"]
                    print(f"    {api}: {time_taken:.2f}s")
            else:
                print(f"  ✅ 没有发现明显的阻塞 (所有请求 <10s)")
        
        # 错误分析
        if failed:
            print(f"\n❌ 错误分析:")
            error_types = {}
            for fail in failed:
                error = str(fail.get("error", "未知错误"))
                error_key = error[:50]  # 取前50个字符作为错误类型
                error_types[error_key] = error_types.get(error_key, 0) + 1
            
            for error, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                print(f"    {error}: {count}次")

async def main():
    """主函数"""
    print("🧪 并发API测试工具")
    print("=" * 30)
    
    tester = ConcurrentAPITester()
    
    # 运行不同强度的测试
    test_configs = [
        {"chat_concurrent": 2, "embedding_concurrent": 3, "rounds": 2, "name": "轻度测试"},
        {"chat_concurrent": 3, "embedding_concurrent": 5, "rounds": 3, "name": "中度测试"},
        {"chat_concurrent": 5, "embedding_concurrent": 8, "rounds": 2, "name": "重度测试"}
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\n{'='*60}")
        print(f"🎯 {config['name']} ({i+1}/{len(test_configs)})")
        print(f"{'='*60}")
        
        await tester.run_concurrent_test(
            chat_concurrent=config["chat_concurrent"],
            embedding_concurrent=config["embedding_concurrent"], 
            rounds=config["rounds"]
        )
        
        if i < len(test_configs) - 1:
            print(f"\n⏸️ 等待5秒后进行下一个测试...")
            await asyncio.sleep(5)
    
    print(f"\n✅ 所有并发测试完成!")
    print(f"\n💡 总结:")
    print(f"  - 如果响应时间稳定且无超时，说明无阻塞")
    print(f"  - 如果出现大量慢请求或超时，可能存在阻塞")
    print(f"  - Chat API通常比Embedding API响应时间更长")

if __name__ == "__main__":
    asyncio.run(main())
