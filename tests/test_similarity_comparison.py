#!/usr/bin/env python3
"""
测试脚本：使用hngpt-embedding和hngpt-embedding模型计算文本相似性
对比两个模型在相同文本上的相似性矩阵表现
"""

import requests
import numpy as np
import json
from typing import List, Dict, Any

class SimilarityTester:
    def __init__(self, base_url: str = "http://localhost:8888", token: str = "startfrom2023"):
        self.base_url = base_url
        self.token = token
        self.headers = {"Authorization": f"Bearer {token}"}
        
        # 测试文本
        self.test_texts = [
            "Llamas eat bananas",      # 0
            "Llamas in pyjamas",       # 1  
            "A bowl of fruit salad",   # 2
            "A sleeping dress"         # 3
        ]
        
        # 预期相似性矩阵（作为参考）
        self.expected_similarity = [
            [1.00, 0.22, 0.46, 0.15],  # Llamas eat bananas
            [0.22, 1.00, 0.28, 0.59],  # Llamas in pyjamas
            [0.46, 0.28, 1.00, 0.33],  # A bowl of fruit salad
            [0.15, 0.59, 0.33, 1.00]   # A sleeping dress
        ]
    
    def get_embedding(self, text: str, model: str) -> List[float]:
        """获取文本的embedding向量"""
        try:
            response = requests.post(
                f"{self.base_url}/v1/embeddings",
                headers=self.headers,
                json={
                    "model": model,
                    "input": text
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                return embedding
            else:
                print(f"❌ 获取embedding失败: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return []
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        if not vec1 or not vec2:
            return 0.0
        
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        # 归一化向量
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)
        
        # 计算余弦相似度
        similarity = np.dot(vec1_norm, vec2_norm)
        return float(similarity)
    
    def get_all_embeddings(self, model: str) -> List[List[float]]:
        """获取所有文本的embeddings"""
        print(f"\n🔍 获取 {model} 模型的embeddings...")
        
        embeddings = []
        for i, text in enumerate(self.test_texts):
            print(f"  处理文本 {i+1}: '{text}'")
            embedding = self.get_embedding(text, model)
            
            if embedding:
                embeddings.append(embedding)
                # 检查向量是否归一化
                l2_norm = np.linalg.norm(embedding)
                print(f"    ✅ 成功 - 维度: {len(embedding)}, L2范数: {l2_norm:.4f}")
            else:
                print(f"    ❌ 失败")
                embeddings.append([])
        
        return embeddings
    
    def calculate_similarity_matrix(self, embeddings: List[List[float]]) -> List[List[float]]:
        """计算相似性矩阵"""
        n = len(embeddings)
        similarity_matrix = [[0.0 for _ in range(n)] for _ in range(n)]
        
        for i in range(n):
            for j in range(n):
                if embeddings[i] and embeddings[j]:
                    similarity_matrix[i][j] = self.cosine_similarity(embeddings[i], embeddings[j])
                else:
                    similarity_matrix[i][j] = 0.0
        
        return similarity_matrix
    
    def print_similarity_matrix(self, matrix: List[List[float]], model: str):
        """打印相似性矩阵"""
        print(f"\n📊 {model} 相似性矩阵:")
        print("=" * 60)
        
        # 打印表头
        print("文本".ljust(25), end="")
        for i in range(len(self.test_texts)):
            print(f"文本{i+1}".rjust(8), end="")
        print()
        
        print("-" * 60)
        
        # 打印矩阵
        for i, row in enumerate(matrix):
            text_preview = self.test_texts[i][:20] + "..." if len(self.test_texts[i]) > 20 else self.test_texts[i]
            print(text_preview.ljust(25), end="")
            
            for j, similarity in enumerate(row):
                print(f"{similarity:.2f}".rjust(8), end="")
            print()
        
        print()
    
    def compare_with_expected(self, matrix: List[List[float]], model: str):
        """与预期结果对比"""
        print(f"🎯 {model} 与预期结果对比:")
        
        total_diff = 0.0
        count = 0
        
        for i in range(len(matrix)):
            for j in range(len(matrix[i])):
                expected = self.expected_similarity[i][j]
                actual = matrix[i][j]
                diff = abs(expected - actual)
                total_diff += diff
                count += 1
                
                if diff > 0.1:  # 差异较大的情况
                    print(f"  文本{i+1} vs 文本{j+1}: 预期 {expected:.2f}, 实际 {actual:.2f}, 差异 {diff:.2f}")
        
        avg_diff = total_diff / count if count > 0 else 0
        print(f"  平均差异: {avg_diff:.3f}")
        
        if avg_diff < 0.05:
            print(f"  ✅ 与预期结果非常接近")
        elif avg_diff < 0.1:
            print(f"  🟡 与预期结果较为接近")
        else:
            print(f"  🔴 与预期结果差异较大")
        
        return avg_diff
    
    def analyze_model_differences(self, matrix1: List[List[float]], matrix2: List[List[float]], 
                                model1: str, model2: str):
        """分析两个模型的差异"""
        print(f"\n🔄 {model1} vs {model2} 差异分析:")
        
        total_diff = 0.0
        max_diff = 0.0
        max_diff_pair = (0, 0)
        
        for i in range(len(matrix1)):
            for j in range(len(matrix1[i])):
                diff = abs(matrix1[i][j] - matrix2[i][j])
                total_diff += diff
                
                if diff > max_diff:
                    max_diff = diff
                    max_diff_pair = (i, j)
        
        avg_diff = total_diff / (len(matrix1) * len(matrix1[0]))
        
        print(f"  平均差异: {avg_diff:.3f}")
        print(f"  最大差异: {max_diff:.3f} (文本{max_diff_pair[0]+1} vs 文本{max_diff_pair[1]+1})")
        
        if avg_diff < 0.02:
            print(f"  ✅ 两个模型结果非常相似")
        elif avg_diff < 0.05:
            print(f"  🟡 两个模型结果较为相似")
        else:
            print(f"  🔴 两个模型结果差异明显")
        
        return avg_diff
    
    def run_comparison_test(self):
        """运行完整的对比测试"""
        print("🚀 文本相似性对比测试")
        print("=" * 50)
        
        print("📝 测试文本:")
        for i, text in enumerate(self.test_texts):
            print(f"  {i+1}. {text}")
        
        print("\n🎯 预期相似性矩阵:")
        for i, row in enumerate(self.expected_similarity):
            print(f"  {row}")
        
        # 测试 hngpt-embedding 模型
        hngpt-embedding_embeddings = self.get_all_embeddings("hngpt-embedding")
        hngpt-embedding_matrix = self.calculate_similarity_matrix(hngpt-embedding_embeddings)
        self.print_similarity_matrix(hngpt-embedding_matrix, "hngpt-embedding")
        hngpt-embedding_diff = self.compare_with_expected(hngpt-embedding_matrix, "hngpt-embedding")
        
        # 测试 hngpt-embedding 模型
        hngpt_embeddings = self.get_all_embeddings("hngpt-embedding")
        hngpt_matrix = self.calculate_similarity_matrix(hngpt_embeddings)
        self.print_similarity_matrix(hngpt_matrix, "hngpt-embedding")
        hngpt_diff = self.compare_with_expected(hngpt_matrix, "hngpt-embedding")
        
        # 模型间对比
        if hngpt-embedding_embeddings and hngpt_embeddings:
            model_diff = self.analyze_model_differences(hngpt-embedding_matrix, hngpt_matrix, "hngpt-embedding", "hngpt-embedding")
        
        # 总结
        print(f"\n📋 测试总结:")
        print(f"  hngpt-embedding 与预期差异: {hngpt-embedding_diff:.3f}")
        print(f"  hngpt-embedding 与预期差异: {hngpt_diff:.3f}")
        
        if hngpt_diff < hngpt-embedding_diff:
            print(f"  🏆 hngpt-embedding 更接近预期结果")
        elif hngpt-embedding_diff < hngpt_diff:
            print(f"  🏆 hngpt-embedding 更接近预期结果")
        else:
            print(f"  🤝 两个模型表现相当")
        
        return {
            "hngpt-embedding_matrix": hngpt-embedding_matrix,
            "hngpt_matrix": hngpt_matrix,
            "hngpt-embedding_diff": hngpt-embedding_diff,
            "hngpt_diff": hngpt_diff
        }

def main():
    """主函数"""
    print("文本相似性对比测试工具")
    print("=" * 30)
    
    # 运行测试
    tester = SimilarityTester()
    results = tester.run_comparison_test()
    
    print(f"\n✅ 测试完成!")
    print(f"\n💡 说明:")
    print(f"- 相似性值越接近预期，说明模型语义理解越准确")
    print(f"- hngpt-embedding 使用指令优化，理论上应该表现更好")
    print(f"- 实际效果可能因模型训练数据和优化方式而异")

if __name__ == "__main__":
    main()
