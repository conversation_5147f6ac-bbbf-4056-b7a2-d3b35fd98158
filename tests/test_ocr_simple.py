#!/usr/bin/env python3
"""
简单的OCR测试
"""

import asyncio
import aiohttp
import base64
import json
import time

async def test_ocr():
    """测试OCR功能"""
    print("🧪 测试OCR功能")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            img_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        payload = {
            "image": img_base64
        }
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/json"
        }
        
        print(f"📄 使用测试图片: {test_image_path}")
        print(f"📏 图片base64长度: {len(img_base64)} 字符")
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/ocr", 
                                   json=payload, 
                                   headers=headers, 
                                   timeout=30) as response:
                response_time = time.time() - start_time
                
                print(f"📊 响应状态码: {response.status}")
                print(f"⏱️  响应时间: {response_time:.3f}s")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ OCR成功!")
                    print(f"📝 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"❌ OCR失败: {error_text}")
                    
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_ocr())
