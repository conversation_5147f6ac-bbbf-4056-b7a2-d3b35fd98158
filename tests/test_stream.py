#!/usr/bin/env python3
"""
测试流式响应功能
"""

import requests
import json

def test_stream_response():
    """测试流式响应"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "Hello, please respond with a short message"}],
        "stream": True  # 启用流式响应
    }
    
    print("🧪 测试流式响应...")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, json=data, headers=headers, stream=True, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("\n📝 流式响应内容:")
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    print(f"  {decoded_line}")
                    if "data: [DONE]" in decoded_line:
                        break
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_non_stream_response():
    """测试非流式响应"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": False  # 非流式响应
    }
    
    print("\n🧪 测试非流式响应...")
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 非流式响应成功")
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    # 先测试服务器是否运行
    try:
        response = requests.get("http://localhost:8888/", timeout=5)
        print("✅ 服务器正在运行")
        
        # 测试非流式响应
        test_non_stream_response()
        
        # 测试流式响应
        test_stream_response()
        
    except Exception as e:
        print(f"❌ 服务器未运行或无法连接: {e}")
