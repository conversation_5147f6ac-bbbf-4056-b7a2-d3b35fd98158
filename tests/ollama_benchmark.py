#!/usr/bin/env python3
"""
Ollama性能基准测试脚本
用于测试和比较不同版本的Ollama性能
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict, Any
import argparse

class OllamaBenchmark:
    def __init__(self, base_url: str = "http://localhost:8888"):
        self.base_url = base_url
        self.results = {
            "chat_tests": [],
            "embedding_tests": [],
            "concurrent_tests": []
        }
    
    async def test_chat_performance(self, num_requests: int = 10) -> Dict[str, Any]:
        """测试Chat API性能"""
        print(f"🚀 开始Chat性能测试 ({num_requests}个请求)...")
        
        chat_request = {
            "model": "qwen2.5:7b",
            "messages": [
                {"role": "user", "content": "请简单介绍一下人工智能的发展历史"}
            ],
            "stream": False
        }
        
        response_times = []
        successful_requests = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(num_requests):
                start_time = time.time()
                try:
                    async with session.post(
                        f"{self.base_url}/v1/chat/completions",
                        json=chat_request,
                        timeout=aiohttp.ClientTimeout(total=60)
                    ) as response:
                        if response.status == 200:
                            await response.json()
                            response_time = time.time() - start_time
                            response_times.append(response_time)
                            successful_requests += 1
                            print(f"  ✅ Chat请求 {i+1}/{num_requests}: {response_time:.2f}s")
                        else:
                            print(f"  ❌ Chat请求 {i+1}/{num_requests}: HTTP {response.status}")
                except Exception as e:
                    print(f"  ❌ Chat请求 {i+1}/{num_requests}: {str(e)}")
        
        if response_times:
            results = {
                "total_requests": num_requests,
                "successful_requests": successful_requests,
                "success_rate": successful_requests / num_requests * 100,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times),
                "response_times": response_times
            }
        else:
            results = {
                "total_requests": num_requests,
                "successful_requests": 0,
                "success_rate": 0,
                "avg_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "median_response_time": 0,
                "response_times": []
            }
        
        self.results["chat_tests"].append(results)
        return results
    
    async def test_embedding_performance(self, num_requests: int = 10) -> Dict[str, Any]:
        """测试Embedding API性能"""
        print(f"🔤 开始Embedding性能测试 ({num_requests}个请求)...")
        
        embedding_request = {
            "model": "bge-m3:latest",
            "input": "这是一个用于测试embedding性能的示例文本，包含了一些中文内容用于评估处理速度。"
        }
        
        response_times = []
        successful_requests = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(num_requests):
                start_time = time.time()
                try:
                    async with session.post(
                        f"{self.base_url}/v1/embeddings",
                        json=embedding_request,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        if response.status == 200:
                            await response.json()
                            response_time = time.time() - start_time
                            response_times.append(response_time)
                            successful_requests += 1
                            print(f"  ✅ Embedding请求 {i+1}/{num_requests}: {response_time:.2f}s")
                        else:
                            print(f"  ❌ Embedding请求 {i+1}/{num_requests}: HTTP {response.status}")
                except Exception as e:
                    print(f"  ❌ Embedding请求 {i+1}/{num_requests}: {str(e)}")
        
        if response_times:
            results = {
                "total_requests": num_requests,
                "successful_requests": successful_requests,
                "success_rate": successful_requests / num_requests * 100,
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "median_response_time": statistics.median(response_times),
                "response_times": response_times
            }
        else:
            results = {
                "total_requests": num_requests,
                "successful_requests": 0,
                "success_rate": 0,
                "avg_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "median_response_time": 0,
                "response_times": []
            }
        
        self.results["embedding_tests"].append(results)
        return results
    
    async def test_concurrent_performance(self, chat_concurrent: int = 5, embedding_concurrent: int = 5) -> Dict[str, Any]:
        """测试并发性能"""
        print(f"⚡ 开始并发性能测试 (Chat: {chat_concurrent}, Embedding: {embedding_concurrent})...")
        
        chat_request = {
            "model": "qwen2.5:7b",
            "messages": [
                {"role": "user", "content": "请用一句话总结机器学习的核心概念"}
            ],
            "stream": False
        }
        
        embedding_request = {
            "model": "bge-m3:latest",
            "input": "机器学习是人工智能的一个重要分支"
        }
        
        async def make_chat_request(session, request_id):
            start_time = time.time()
            try:
                async with session.post(
                    f"{self.base_url}/v1/chat/completions",
                    json=chat_request,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        await response.json()
                        return {"type": "chat", "id": request_id, "time": time.time() - start_time, "success": True}
                    else:
                        return {"type": "chat", "id": request_id, "time": time.time() - start_time, "success": False}
            except Exception as e:
                return {"type": "chat", "id": request_id, "time": time.time() - start_time, "success": False, "error": str(e)}
        
        async def make_embedding_request(session, request_id):
            start_time = time.time()
            try:
                async with session.post(
                    f"{self.base_url}/v1/embeddings",
                    json=embedding_request,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        await response.json()
                        return {"type": "embedding", "id": request_id, "time": time.time() - start_time, "success": True}
                    else:
                        return {"type": "embedding", "id": request_id, "time": time.time() - start_time, "success": False}
            except Exception as e:
                return {"type": "embedding", "id": request_id, "time": time.time() - start_time, "success": False, "error": str(e)}
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            # 创建Chat任务
            for i in range(chat_concurrent):
                tasks.append(make_chat_request(session, f"chat_{i+1}"))
            
            # 创建Embedding任务
            for i in range(embedding_concurrent):
                tasks.append(make_embedding_request(session, f"embed_{i+1}"))
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 分析结果
        chat_results = [r for r in results if isinstance(r, dict) and r.get("type") == "chat"]
        embedding_results = [r for r in results if isinstance(r, dict) and r.get("type") == "embedding"]
        
        chat_successful = [r for r in chat_results if r.get("success")]
        embedding_successful = [r for r in embedding_results if r.get("success")]
        
        concurrent_results = {
            "total_time": total_time,
            "total_requests": chat_concurrent + embedding_concurrent,
            "chat_requests": chat_concurrent,
            "embedding_requests": embedding_concurrent,
            "chat_successful": len(chat_successful),
            "embedding_successful": len(embedding_successful),
            "chat_success_rate": len(chat_successful) / chat_concurrent * 100 if chat_concurrent > 0 else 0,
            "embedding_success_rate": len(embedding_successful) / embedding_concurrent * 100 if embedding_concurrent > 0 else 0,
            "overall_qps": (len(chat_successful) + len(embedding_successful)) / total_time if total_time > 0 else 0,
            "chat_avg_time": statistics.mean([r["time"] for r in chat_successful]) if chat_successful else 0,
            "embedding_avg_time": statistics.mean([r["time"] for r in embedding_successful]) if embedding_successful else 0,
            "detailed_results": results
        }
        
        self.results["concurrent_tests"].append(concurrent_results)
        return concurrent_results
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("📊 Ollama性能测试结果摘要")
        print("="*60)
        
        if self.results["chat_tests"]:
            chat_test = self.results["chat_tests"][-1]
            print(f"\n💬 Chat API性能:")
            print(f"  成功率: {chat_test['success_rate']:.1f}%")
            print(f"  平均响应时间: {chat_test['avg_response_time']:.2f}s")
            print(f"  最快响应时间: {chat_test['min_response_time']:.2f}s")
            print(f"  最慢响应时间: {chat_test['max_response_time']:.2f}s")
        
        if self.results["embedding_tests"]:
            embed_test = self.results["embedding_tests"][-1]
            print(f"\n🔤 Embedding API性能:")
            print(f"  成功率: {embed_test['success_rate']:.1f}%")
            print(f"  平均响应时间: {embed_test['avg_response_time']:.2f}s")
            print(f"  最快响应时间: {embed_test['min_response_time']:.2f}s")
            print(f"  最慢响应时间: {embed_test['max_response_time']:.2f}s")
        
        if self.results["concurrent_tests"]:
            concurrent_test = self.results["concurrent_tests"][-1]
            print(f"\n⚡ 并发性能:")
            print(f"  总QPS: {concurrent_test['overall_qps']:.2f}")
            print(f"  Chat成功率: {concurrent_test['chat_success_rate']:.1f}%")
            print(f"  Embedding成功率: {concurrent_test['embedding_success_rate']:.1f}%")
            print(f"  Chat平均时间: {concurrent_test['chat_avg_time']:.2f}s")
            print(f"  Embedding平均时间: {concurrent_test['embedding_avg_time']:.2f}s")
    
    def save_results(self, filename: str = None):
        """保存测试结果到文件"""
        if filename is None:
            filename = f"ollama_benchmark_{int(time.time())}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: {filename}")

async def main():
    parser = argparse.ArgumentParser(description="Ollama性能基准测试")
    parser.add_argument("--url", default="http://localhost:8888", help="API基础URL")
    parser.add_argument("--chat-requests", type=int, default=5, help="Chat请求数量")
    parser.add_argument("--embedding-requests", type=int, default=5, help="Embedding请求数量")
    parser.add_argument("--chat-concurrent", type=int, default=3, help="并发Chat请求数量")
    parser.add_argument("--embedding-concurrent", type=int, default=3, help="并发Embedding请求数量")
    parser.add_argument("--output", help="结果输出文件名")
    
    args = parser.parse_args()
    
    benchmark = OllamaBenchmark(args.url)
    
    print("🔥 Ollama性能基准测试开始")
    print(f"📍 测试目标: {args.url}")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    await benchmark.test_chat_performance(args.chat_requests)
    await benchmark.test_embedding_performance(args.embedding_requests)
    await benchmark.test_concurrent_performance(args.chat_concurrent, args.embedding_concurrent)
    
    # 打印结果
    benchmark.print_summary()
    
    # 保存结果
    benchmark.save_results(args.output)
    
    print(f"\n✅ 测试完成于: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
