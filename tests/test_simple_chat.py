#!/usr/bin/env python3
"""
简单的Chat接口测试
"""

import asyncio
import aiohttp
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_chat():
    """测试原Chat接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "hngpt-mini:latest",
        "messages": [{"role": "user", "content": "Hello, test chat"}],
        "stream": False
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/chat/completions",
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ Chat接口测试成功")
                    logging.info(f"响应: {result.get('choices', [{}])[0].get('message', {}).get('content', '')[:100]}...")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ Chat接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ Chat接口异常: {e}")
        return False

async def test_test_chat():
    """测试新的测试Chat接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "hngpt-mini:latest",
        "messages": [{"role": "user", "content": "Hello, test new chat"}],
        "stream": False
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/chat/completions/test",
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ 测试Chat接口成功")
                    logging.info(f"响应: {result.get('choices', [{}])[0].get('message', {}).get('content', '')[:100]}...")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ 测试Chat接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ 测试Chat接口异常: {e}")
        return False

async def main():
    """主函数"""
    logging.info("🚀 开始简单Chat测试")
    
    # 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/") as response:
                if response.status == 200:
                    logging.info("✅ 服务器连接成功")
                else:
                    logging.error("❌ 服务器连接失败")
                    return
    except Exception as e:
        logging.error(f"❌ 无法连接到服务器: {e}")
        return
    
    # 测试Chat接口
    logging.info("\n🔍 测试原Chat接口...")
    success1 = await test_chat()

    logging.info("\n🔍 测试新的测试Chat接口...")
    success2 = await test_test_chat()

    if success1 or success2:
        logging.info("🎉 至少一个接口测试成功！")
        if success1:
            logging.info("✅ 原Chat接口工作正常")
        if success2:
            logging.info("✅ 测试Chat接口工作正常")
    else:
        logging.warning("⚠️ 所有接口测试失败")

if __name__ == "__main__":
    asyncio.run(main())
