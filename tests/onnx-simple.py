import onnx
import onnxsim
import onnx.helper as helper

def rename_io_names(model_path, new_input_name, new_output_name):

    # 加载ONNX模型
    model = onnx.load(model_path)

    # 获取模型的输入和输出
    inputs = model.graph.input
    outputs = model.graph.output

    # 创建新的输入和输出Tensor, 保留原有属性
    new_input = helper.make_tensor_value_info(
        new_input_name, 
        inputs[0].type.tensor_type.elem_type, 
        [d.dim_param if d.dim_param else d.dim_value for d in inputs[0].type.tensor_type.shape.dim]
    )
    new_output = helper.make_tensor_value_info(
        new_output_name, 
        outputs[0].type.tensor_type.elem_type, 
        [d.dim_param if d.dim_param else d.dim_value for d in outputs[0].type.tensor_type.shape.dim]
    )

    # 替换图中的节点引用
    for node in model.graph.node:
        for index, inp in enumerate(node.input):
            if inp == inputs[0].name:
                node.input[index] = new_input_name
        for index, out in enumerate(node.output):
            if out == outputs[0].name:
                node.output[index] = new_output_name

    # 替换旧的输入输出名
    model.graph.input.remove(inputs[0])
    model.graph.output.remove(outputs[0])
    model.graph.input.insert(0, new_input)
    model.graph.output.insert(0, new_output)

    return model

if __name__ == "__main__":

    for  name  in ["cls", "det", "rec"]:
        model_onnx = rename_io_names(f"PP-OCRv5_mobile_{name}/inference.onnx", "images", "output")

        # 检查导入的onnx model
        onnx.checker.check_model(model_onnx)

        print(f"Simplifying with onnx-simplifier {onnxsim.__version__}...")
        model_onnx, check = onnxsim.simplify(model_onnx)
        assert check, "assert check failed"
        onnx.save(model_onnx, f"PP-OCRv5_mobile_{name}/inference.sim.onnx")
