#!/usr/bin/env python3
"""
测试统一任务管理系统
"""
import requests
import json
import time

BASE_URL = "http://localhost:8888"

def test_queue_status():
    """测试队列状态API"""
    print("🔍 测试队列状态API...")
    try:
        response = requests.get(f"{BASE_URL}/monitor/api/queue")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("Queue Status:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            return data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_embedding_task():
    """测试Embedding任务"""
    print("\n🔤 测试Embedding任务...")
    try:
        payload = {
            "model": "hngpt-embedding",
            "input": ["测试文本"]
        }
        headers = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/v1/embeddings", 
                               json=payload, headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Embedding dimension: {len(data['data'][0]['embedding'])}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_chat_task():
    """测试Chat任务"""
    print("\n💬 测试Chat任务...")
    try:
        payload = {
            "model": "hngpt-mini",
            "messages": [
                {"role": "user", "content": "你好"}
            ],
            "stream": False
        }
        headers = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json"
        }
        
        response = requests.post(f"{BASE_URL}/v1/chat/completions", 
                               json=payload, headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data['choices'][0]['message']['content'][:100]}...")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    print("🚀 开始测试统一任务管理系统")
    print("=" * 50)
    
    # 测试队列状态
    queue_data = test_queue_status()
    
    # 测试Embedding任务
    embedding_success = test_embedding_task()
    
    # 等待一下
    time.sleep(1)
    
    # 测试Chat任务
    chat_success = test_chat_task()
    
    # 等待一下
    time.sleep(1)
    
    # 再次检查队列状态
    print("\n🔍 最终队列状态...")
    final_queue_data = test_queue_status()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  ✅ 队列状态API: {'成功' if queue_data else '失败'}")
    print(f"  ✅ Embedding任务: {'成功' if embedding_success else '失败'}")
    print(f"  ✅ Chat任务: {'成功' if chat_success else '失败'}")
    
    if final_queue_data:
        print(f"  📈 总任务数: {final_queue_data.get('total_tasks', 0)}")
        print(f"  📈 已完成: {final_queue_data.get('completed', 0)}")
        print(f"  📈 GPU状态: {final_queue_data.get('gpu_status', {})}")

if __name__ == "__main__":
    main()
