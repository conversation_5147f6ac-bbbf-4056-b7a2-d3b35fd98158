#!/usr/bin/env python3
"""
测试修复后的流式响应
"""

import requests
import json
import time

def test_fixed_stream():
    """测试修复后的流式响应"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "请说一句简短的问候"}],
        "stream": True
    }
    
    print("🧪 测试修复后的流式响应...")
    print(f"请求URL: {url}")
    
    try:
        response = requests.post(url, json=data, headers=headers, stream=True, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            print("\n📝 流式响应内容:")
            chunk_count = 0
            full_content = ""
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    print(f"  原始行: {decoded_line}")
                    chunk_count += 1
                    
                    # 解析SSE数据
                    if decoded_line.startswith('data: ') and not decoded_line.endswith('[DONE]'):
                        try:
                            json_str = decoded_line[6:]  # 移除 'data: '
                            chunk_data = json.loads(json_str)
                            
                            # 检查OpenAI格式
                            if 'choices' in chunk_data and chunk_data['choices']:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    full_content += content
                                    print(f"    ✅ 内容: '{content}'")
                                    print(f"    📝 累积内容: '{full_content}'")
                                else:
                                    print(f"    ⚠️ 无内容的delta: {choice}")
                            else:
                                print(f"    ❌ 格式错误: {chunk_data}")
                        except json.JSONDecodeError as e:
                            print(f"    ❌ JSON解析错误: {e}")
                    
                    elif "data: [DONE]" in decoded_line:
                        print("  ✅ 流式响应正常结束")
                        break
                        
                    if chunk_count > 50:  # 防止输出过多
                        print("  ⚠️ 响应过长，停止读取")
                        break
                        
            print(f"\n📊 总结:")
            print(f"  - 总数据块: {chunk_count}")
            print(f"  - 完整内容: '{full_content}'")
            print(f"  - 内容长度: {len(full_content)} 字符")
            
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    # 先检查服务器是否运行
    try:
        response = requests.get("http://localhost:8888/", timeout=5)
        print("✅ 服务器正在运行")
        test_fixed_stream()
    except Exception as e:
        print(f"❌ 服务器未运行: {e}")
        print("请先启动服务器")
