#!/usr/bin/env python3
"""
调试所有检测结果，包括低置信度的
"""
import cv2
import json
import numpy as np
import onnxruntime as ort

# 类别名称
CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

def main():
    print("🔍 调试所有检测结果")
    print("=" * 50)
    
    # 加载图像
    image_path = "/workspace/hngpt/document.png"
    image = cv2.imread(image_path)
    orig_h, orig_w = image.shape[:2]
    print(f"图像尺寸: {orig_w} x {orig_h}")
    
    # 预处理 - 直接缩放
    target_size = 640
    scale_x = target_size / orig_w
    scale_y = target_size / orig_h
    
    resized = cv2.resize(image, (target_size, target_size))
    rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
    normalized = rgb_image.astype(np.float32) / 255.0
    chw_image = np.transpose(normalized, (2, 0, 1))
    batch_image = np.expand_dims(chw_image, axis=0)
    
    inputs = {
        'image': batch_image,
        'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
        'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
    }
    
    print(f"预处理完成:")
    print(f"  scale_x: {scale_x:.4f}")
    print(f"  scale_y: {scale_y:.4f}")
    print(f"  输入形状: {batch_image.shape}")
    
    # 推理
    model_path = "/workspace/hngpt/models/doc_layout.optimized.onnx"
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    outputs = session.run(None, inputs)
    
    print(f"推理完成，输出数量: {len(outputs)}")
    for i, output in enumerate(outputs):
        print(f"  输出 {i}: {output.shape}")
    
    # 分析所有检测结果
    detections = outputs[0]  # (N, 6)
    print(f"\n分析所有检测结果 (共 {len(detections)} 个):")
    
    # 统计不同置信度范围的检测数量
    conf_ranges = [
        (0.9, 1.0, "超高"),
        (0.7, 0.9, "高"),
        (0.5, 0.7, "中高"),
        (0.3, 0.5, "中"),
        (0.1, 0.3, "低"),
        (0.0, 0.1, "极低")
    ]
    
    range_counts = {name: 0 for _, _, name in conf_ranges}
    class_counts = {}
    
    all_results = []
    
    for detection in detections:
        if len(detection) >= 6:
            class_id = int(detection[0])
            confidence = float(detection[1])
            x1, y1, x2, y2 = detection[2:6]
            
            # 统计置信度分布
            for min_conf, max_conf, range_name in conf_ranges:
                if min_conf <= confidence < max_conf:
                    range_counts[range_name] += 1
                    break
            
            # 统计类别分布（置信度 > 0.1）
            if confidence > 0.1:
                class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                
                # 坐标转换
                orig_x1 = int(x1 / scale_x)
                orig_y1 = int(y1 / scale_y)
                orig_x2 = int(x2 / scale_x)
                orig_y2 = int(y2 / scale_y)
                
                all_results.append({
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': confidence,
                    'raw_coords': [x1, y1, x2, y2],
                    'orig_coords': [orig_x1, orig_y1, orig_x2, orig_y2]
                })
    
    print(f"\n📊 置信度分布:")
    for range_name, count in range_counts.items():
        print(f"  {range_name:4s}: {count:3d}")
    
    print(f"\n📊 类别分布 (置信度 > 0.1):")
    for class_name, count in sorted(class_counts.items()):
        print(f"  {class_name:15s}: {count}")
    
    # 显示高置信度检测
    high_conf_results = [r for r in all_results if r['confidence'] > 0.3]
    high_conf_results.sort(key=lambda x: x['confidence'], reverse=True)
    
    print(f"\n📋 高置信度检测 (> 0.3, 共 {len(high_conf_results)} 个):")
    for i, result in enumerate(high_conf_results):
        print(f"{i+1:2d}. {result['class_name']:15s} "
              f"置信度: {result['confidence']:.3f} "
              f"坐标: {result['orig_coords']}")
    
    # 显示中等置信度检测（寻找可能遗漏的seal等）
    mid_conf_results = [r for r in all_results if 0.1 < r['confidence'] <= 0.3]
    mid_conf_results.sort(key=lambda x: x['confidence'], reverse=True)
    
    print(f"\n📋 中等置信度检测 (0.1-0.3, 共 {len(mid_conf_results)} 个):")
    for i, result in enumerate(mid_conf_results[:10]):  # 只显示前10个
        print(f"{i+1:2d}. {result['class_name']:15s} "
              f"置信度: {result['confidence']:.3f} "
              f"坐标: {result['orig_coords']}")
    
    # 特别查找 seal 类别
    seal_results = [r for r in all_results if r['class_name'] == 'seal']
    if seal_results:
        print(f"\n🔍 发现 seal 检测 (共 {len(seal_results)} 个):")
        for result in seal_results:
            print(f"  置信度: {result['confidence']:.3f} 坐标: {result['orig_coords']}")
    else:
        print(f"\n⚠️  未发现任何 seal 检测")
    
    print(f"\n💡 建议:")
    if len(high_conf_results) < 5:
        print("  - 尝试降低置信度阈值到 0.2 或 0.3")
    if not seal_results:
        print("  - 检查图像中是否确实有印章")
        print("  - 可能需要调整预处理方式")

if __name__ == "__main__":
    main()
