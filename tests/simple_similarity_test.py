#!/usr/bin/env python3
"""
简化版相似性测试脚本
快速测试hngpt-embedding和hngpt-embedding模型的相似性计算
"""

import requests
import numpy as np

# 配置
BASE_URL = "http://localhost:8888"
TOKEN = "startfrom2023"  # 替换为实际token
HEADERS = {"Authorization": f"Bearer {TOKEN}"}

# 测试文本
TEXTS = [
    "Llamas eat bananas",      # 0
    "Llamas in pyjamas",       # 1  
    "A bowl of fruit salad",   # 2
    "A sleeping dress"         # 3
]

# 预期相似性矩阵
EXPECTED = [
    [1.00, 0.22, 0.46, 0.15],
    [0.22, 1.00, 0.28, 0.59],
    [0.46, 0.28, 1.00, 0.33],
    [0.15, 0.59, 0.33, 1.00]
]

def get_embedding(text, model):
    """获取embedding向量"""
    response = requests.post(f"{BASE_URL}/v1/embeddings",
        headers=HEADERS,
        json={"model": model, "input": text}
    )
    
    if response.status_code == 200:
        return response.json()["data"][0]["embedding"]
    else:
        print(f"❌ 错误: {response.status_code}")
        return None

def ncosine(a, b):
    """计算余弦相似度"""
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

def find_most_similar_to_first(embeddings, model_name):
    """找出与第一个句子最相似的句子"""
    if len(embeddings) < 2:
        return None

    first_embedding = embeddings[0]
    similarities = []

    print(f"\n🔍 {model_name} - 与第一个句子的相似度:")
    print(f"  基准句子: '{TEXTS[0]}'")

    for i in range(1, len(embeddings)):  # 从第二个句子开始
        similarity = ncosine(first_embedding, embeddings[i])
        similarities.append((i, similarity, TEXTS[i]))
        print(f"  句子{i+1}: {similarity:.4f} - '{TEXTS[i]}'")

    # 找出最相似的句子
    most_similar = max(similarities, key=lambda x: x[1])
    most_similar_idx, max_similarity, most_similar_text = most_similar

    print(f"\n🏆 最相似的句子:")
    print(f"  句子{most_similar_idx+1}: '{most_similar_text}'")
    print(f"  相似度: {max_similarity:.4f}")

    return most_similar

def test_model(model_name):
    """测试单个模型"""
    print(f"\n🔍 测试模型: {model_name}")
    
    # 获取所有embeddings
    embeddings = []
    for i, text in enumerate(TEXTS):
        emb = get_embedding(text, model_name)
        if emb:
            embeddings.append(emb)
            l2_norm = np.linalg.norm(emb)
            print(f"  文本{i+1}: {len(emb)}维, L2范数={l2_norm:.3f}")
        else:
            print(f"  文本{i+1}: 失败")
            return None
    
    # 计算相似性矩阵
    n = len(embeddings)
    matrix = [[0.0 for _ in range(n)] for _ in range(n)]
    
    for i in range(n):
        for j in range(n):
            matrix[i][j] = ncosine(embeddings[i], embeddings[j])
    
    # 打印矩阵
    print(f"\n📊 {model_name} 相似性矩阵:")
    for i, row in enumerate(matrix):
        print(f"  {[f'{x:.2f}' for x in row]} ({TEXTS[i]})")
    
    # 与预期对比
    total_diff = 0
    for i in range(n):
        for j in range(n):
            total_diff += abs(matrix[i][j] - EXPECTED[i][j])
    
    avg_diff = total_diff / (n * n)
    print(f"  平均差异: {avg_diff:.3f}")

    # 找出与第一个句子最相似的句子
    most_similar = find_most_similar_to_first(embeddings, model_name)

    return matrix, avg_diff, most_similar

def main():
    """主函数"""
    print("🚀 相似性测试")
    print("=" * 30)
    
    print("📝 测试文本:")
    for i, text in enumerate(TEXTS):
        print(f"  {i+1}. {text}")
    
    print(f"\n🎯 预期相似性矩阵:")
    for i, row in enumerate(EXPECTED):
        print(f"  {[f'{x:.2f}' for x in row]} ({TEXTS[i]})")
    
    # 测试两个模型
    hngpt-embedding_result = test_model("hngpt-embedding")
    hngpt_result = test_model("hngpt-embedding")
    
    # 对比结果
    if hngpt-embedding_result and hngpt_result:
        hngpt-embedding_matrix, hngpt-embedding_diff, hngpt-embedding_most_similar = hngpt-embedding_result
        hngpt_matrix, hngpt_diff, hngpt_most_similar = hngpt_result

        print(f"\n📋 结果对比:")
        print(f"  hngpt-embedding 平均差异: {hngpt-embedding_diff:.3f}")
        print(f"  hngpt-embedding 平均差异: {hngpt_diff:.3f}")

        if hngpt_diff < hngpt-embedding_diff:
            print(f"  🏆 hngpt-embedding 更准确")
        elif hngpt-embedding_diff < hngpt_diff:
            print(f"  🏆 hngpt-embedding 更准确")
        else:
            print(f"  🤝 两个模型表现相当")

        # 模型间差异
        model_diff = 0
        for i in range(len(hngpt-embedding_matrix)):
            for j in range(len(hngpt-embedding_matrix[i])):
                model_diff += abs(hngpt-embedding_matrix[i][j] - hngpt_matrix[i][j])

        model_diff /= (len(hngpt-embedding_matrix) * len(hngpt-embedding_matrix[0]))
        print(f"  模型间差异: {model_diff:.3f}")

        # 对比最相似句子的结果
        print(f"\n🎯 最相似句子对比:")
        if hngpt-embedding_most_similar:
            print(f"  hngpt-embedding: 句子{hngpt-embedding_most_similar[0]+1} (相似度: {hngpt-embedding_most_similar[1]:.4f})")
        if hngpt_most_similar:
            print(f"  hngpt-embedding: 句子{hngpt_most_similar[0]+1} (相似度: {hngpt_most_similar[1]:.4f})")

        # 检查两个模型是否选择了相同的最相似句子
        if hngpt-embedding_most_similar and hngpt_most_similar:
            if hngpt-embedding_most_similar[0] == hngpt_most_similar[0]:
                print(f"  ✅ 两个模型选择了相同的最相似句子")
            else:
                print(f"  ⚠️ 两个模型选择了不同的最相似句子")

if __name__ == "__main__":
    main()
