#!/usr/bin/env python3
"""
重置GPU状态的工具脚本
清理所有GPU槽位，重置负载均衡器状态
"""

import asyncio
import aiohttp
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def reset_gpu_status():
    """重置GPU状态"""
    try:
        async with aiohttp.ClientSession() as session:
            # 调用重置接口
            async with session.post("http://localhost:8888/api/gpu/reset",
                                   headers={"Authorization": "Bearer startfrom2023"}) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ GPU状态重置成功")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ GPU状态重置失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ GPU状态重置异常: {e}")
        return False

async def get_gpu_status():
    """获取GPU状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/api/gpu/status") as response:
                if response.status == 200:
                    result = await response.json()
                    data = result.get("data", {})
                    logging.info("📊 当前GPU状态:")
                    logging.info(f"  GPU数量: {data.get('gpu_count', 0)}")
                    logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
                    logging.info(f"  当前负载: {data.get('current_loads', {})}")
                    return data
                else:
                    logging.error(f"❌ 获取GPU状态失败: {response.status}")
                    return None
    except Exception as e:
        logging.error(f"❌ 获取GPU状态异常: {e}")
        return None

async def main():
    """主函数"""
    logging.info("🔧 开始重置GPU状态")
    
    # 1. 检查服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/") as response:
                if response.status == 200:
                    logging.info("✅ 服务器连接成功")
                else:
                    logging.error("❌ 服务器连接失败")
                    return
    except Exception as e:
        logging.error(f"❌ 无法连接到服务器: {e}")
        return
    
    # 2. 获取重置前状态
    logging.info("\n📊 重置前GPU状态:")
    await get_gpu_status()
    
    # 3. 执行重置
    logging.info("\n🔧 执行GPU状态重置...")
    success = await reset_gpu_status()
    
    if not success:
        logging.warning("⚠️ GPU状态重置失败，尝试手动清理...")
        # 这里可以添加手动清理逻辑
    
    # 4. 获取重置后状态
    logging.info("\n📊 重置后GPU状态:")
    await get_gpu_status()
    
    if success:
        logging.info("🎉 GPU状态重置完成！")
    else:
        logging.warning("⚠️ GPU状态重置可能不完整")

if __name__ == "__main__":
    asyncio.run(main())
