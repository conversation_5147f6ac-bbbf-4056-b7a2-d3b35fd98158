#!/usr/bin/env python3
"""
检查ONNX模型的输入输出信息
"""
import onnxruntime as ort
import numpy as np

def check_model_info(model_path):
    """检查模型信息"""
    print(f"🔍 检查模型: {model_path}")
    
    # 加载模型
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    print(f"\n📥 输入信息:")
    for i, input_info in enumerate(session.get_inputs()):
        print(f"  {i}: {input_info.name}")
        print(f"     形状: {input_info.shape}")
        print(f"     类型: {input_info.type}")
    
    print(f"\n📤 输出信息:")
    for i, output_info in enumerate(session.get_outputs()):
        print(f"  {i}: {output_info.name}")
        print(f"     形状: {output_info.shape}")
        print(f"     类型: {output_info.type}")

if __name__ == "__main__":
    check_model_info("inference.onnx")
