# PP-DocLayout-L trtexec 使用示例

这个文档展示了如何使用 `trtexec` 构建 PP-DocLayout-L TensorRT 引擎的各种方式。

## 基础用法

### 1. 最简单的构建

```bash
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout.trt \
    --fp16
```

### 2. 指定动态形状 (推荐)

```bash
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout.trt \
    --explicitBatch \
    --fp16 \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --maxShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2
```

### 3. 完整参数构建

```bash
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout.trt \
    --workspace=1024 \
    --explicitBatch \
    --fp16 \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --maxShapes=image:4x3x640x640,im_shape:4x2,scale_factor:4x2 \
    --noTF32 \
    --buildOnly \
    --timingCacheFile=timing.cache \
    --verbose
```

## 高级用法

### 1. 批处理支持

```bash
# 支持批次 1-4
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout_batch.trt \
    --explicitBatch \
    --fp16 \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:2x3x640x640,im_shape:2x2,scale_factor:2x2 \
    --maxShapes=image:4x3x640x640,im_shape:4x2,scale_factor:4x2
```

### 2. INT8 量化

```bash
# 需要校准数据集
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout_int8.trt \
    --int8 \
    --calib=calibration_data \
    --explicitBatch \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --maxShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2
```

### 3. 性能分析

```bash
# 构建并测试性能
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=doc_layout.trt \
    --fp16 \
    --explicitBatch \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --maxShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --warmUp=1000 \
    --iterations=1000 \
    --avgRuns=10
```

## 引擎分析

### 1. 查看引擎信息

```bash
trtexec \
    --loadEngine=doc_layout.trt \
    --dumpLayerInfo \
    --dumpProfile \
    --noDataTransfers \
    --iterations=1
```

### 2. 性能测试

```bash
trtexec \
    --loadEngine=doc_layout.trt \
    --shapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --warmUp=100 \
    --iterations=1000 \
    --avgRuns=10
```

## 常用参数说明

### 基础参数
- `--onnx`: 输入 ONNX 模型路径
- `--saveEngine`: 输出 TensorRT 引擎路径
- `--loadEngine`: 加载已有引擎进行测试

### 精度参数
- `--fp16`: 使用 FP16 精度 (推荐)
- `--int8`: 使用 INT8 精度 (需要校准)
- `--noTF32`: 禁用 TF32 (提高精度)

### 形状参数
- `--explicitBatch`: 使用显式批次模式 (推荐)
- `--minShapes`: 最小输入形状
- `--optShapes`: 优化输入形状
- `--maxShapes`: 最大输入形状

### 优化参数
- `--workspace`: 工作空间大小 (MB)
- `--timingCacheFile`: 时序缓存文件
- `--buildOnly`: 只构建引擎，不运行测试

### 测试参数
- `--warmUp`: 预热次数
- `--iterations`: 测试迭代次数
- `--avgRuns`: 平均运行次数

## 故障排除

### 1. 内存不足
```bash
# 减少工作空间
--workspace=512

# 或使用更小的批次
--maxShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2
```

### 2. 构建失败
```bash
# 添加详细输出
--verbose

# 检查 ONNX 模型
onnx-simplifier input.onnx output.onnx
```

### 3. 性能不佳
```bash
# 使用 FP16
--fp16

# 增加工作空间
--workspace=2048

# 优化形状设置
--optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2
```

## 最佳实践

1. **总是使用 `--explicitBatch`**: 现代 TensorRT 的推荐模式
2. **设置合适的形状范围**: 根据实际使用场景设置 min/opt/max shapes
3. **使用 FP16**: 在大多数情况下提供最佳的性能/精度平衡
4. **启用时序缓存**: 使用 `--timingCacheFile` 加速后续构建
5. **适当的工作空间**: 通常 1024MB 是一个好的起点
6. **禁用 TF32**: 使用 `--noTF32` 获得更好的数值精度

## 示例脚本

完整的构建脚本示例请参考 `build_engine_simple.sh`。
