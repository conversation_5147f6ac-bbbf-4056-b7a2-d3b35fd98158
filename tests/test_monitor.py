#!/usr/bin/env python3
"""
GPU监控页面测试脚本
"""

import requests
import json
import time
import os

def test_monitor_page():
    """测试监控页面"""
    print("🧪 测试GPU监控页面...")
    
    base_url = "http://localhost:8888"
    
    # 1. 测试监控页面访问
    try:
        response = requests.get(f"{base_url}/monitor", timeout=10)
        if response.status_code == 200:
            print("✅ 监控页面访问成功")
            print(f"   页面大小: {len(response.content)} bytes")
            print(f"   内容类型: {response.headers.get('content-type', 'unknown')}")
        else:
            print(f"❌ 监控页面访问失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 监控页面访问异常: {str(e)}")
    
    # 2. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查接口正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
    
    # 3. 检查静态文件
    print("\n📁 检查静态文件:")
    static_files = [
        "static/gpu-monitor.html",
        "static/bootstrap.min.css",
        "static/jquery-3.7.1.min.js"
    ]
    
    for file_path in static_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    # 4. 测试静态文件访问
    try:
        response = requests.get(f"{base_url}/static/gpu-monitor.html", timeout=5)
        if response.status_code == 200:
            print("✅ 静态文件服务正常")
        else:
            print(f"❌ 静态文件访问失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 静态文件访问异常: {str(e)}")

def test_api_endpoints():
    """测试API接口"""
    print("\n🔍 测试API接口...")
    
    base_url = "http://localhost:8888"
    
    # 测试需要认证的接口（使用示例token）
    test_tokens = [
        "test_token_123",
        "admin_key",
        "your_secret_token_here"
    ]
    
    for token in test_tokens:
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/status", headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API状态接口正常 (token: {token[:10]}...)")
                print(f"   GPU数量: {len(data.get('gpu_info', []))}")
                print(f"   服务器数量: {data.get('total_servers', 0)}")
                break
            elif response.status_code == 403:
                print(f"🔐 Token无效: {token[:10]}...")
            else:
                print(f"❌ API接口错误: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ API接口异常: {str(e)}")
    
    # 测试队列统计接口
    for token in test_tokens:
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/queue/stats", headers=headers, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 队列统计接口正常")
                print(f"   总任务数: {data.get('total_tasks', 0)}")
                print(f"   队列长度: {data.get('queue_size', 0)}")
                break
            elif response.status_code == 403:
                continue
            else:
                print(f"❌ 队列统计接口错误: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 队列统计接口异常: {str(e)}")

def show_usage_info():
    """显示使用信息"""
    print("\n📋 GPU监控页面使用指南:")
    print("=" * 50)
    print("🎯 访问地址:")
    print("   http://localhost:8888/monitor")
    print()
    print("📊 功能特性:")
    print("   • 实时GPU状态监控 (内存使用、任务分布)")
    print("   • 任务队列统计 (队列长度、处理速度)")
    print("   • Ollama服务器状态")
    print("   • 自动刷新 (3秒间隔)")
    print("   • 响应式设计 (支持移动端)")
    print()
    print("🔧 监控指标:")
    print("   • GPU显存使用率")
    print("   • LLM/OCR/Layout任务分布")
    print("   • 队列长度和处理状态")
    print("   • 服务器健康状态")
    print()
    print("⚡ 快捷操作:")
    print("   • 点击右下角刷新按钮手动刷新")
    print("   • 页面失焦时自动暂停刷新")
    print("   • 页面重新获得焦点时恢复刷新")
    print()
    print("🚨 故障排除:")
    print("   • 如果页面无法访问，检查应用是否运行")
    print("   • 如果数据不更新，检查API接口权限")
    print("   • 如果显示错误，查看浏览器控制台")

def main():
    """主函数"""
    print("🚀 GPU监控页面测试工具")
    print("=" * 50)
    
    # 检查应用是否运行
    try:
        response = requests.get("http://localhost:8888/health", timeout=5)
        if response.status_code == 200:
            print("✅ HNGPT应用正在运行")
        else:
            print("❌ HNGPT应用可能未运行或有问题")
            return
    except Exception as e:
        print(f"❌ 无法连接到HNGPT应用: {str(e)}")
        print("   请确保应用在 http://localhost:8888 运行")
        return
    
    # 运行测试
    test_monitor_page()
    test_api_endpoints()
    show_usage_info()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
