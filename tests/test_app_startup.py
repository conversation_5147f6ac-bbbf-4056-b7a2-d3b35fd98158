#!/usr/bin/env python3
"""
测试应用启动，不运行版面分析测试代码
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 测试导入layout_content_extractor
try:
    from layout_content_extractor import extract_page_content_by_layout
    print("✅ 成功导入 extract_page_content_by_layout")
except Exception as e:
    print(f"❌ 导入 extract_page_content_by_layout 失败: {e}")

# 测试导入其他必要模块
try:
    import timefinder
    print("✅ 成功导入 timefinder")
except Exception as e:
    print(f"❌ 导入 timefinder 失败: {e}")

try:
    import cv2
    print("✅ 成功导入 cv2")
except Exception as e:
    print(f"❌ 导入 cv2 失败: {e}")

try:
    import numpy as np
    print("✅ 成功导入 numpy")
except Exception as e:
    print(f"❌ 导入 numpy 失败: {e}")

print("🎉 所有必要模块导入成功！")
