#!/usr/bin/env python3
"""
直接测试Ollama的流式响应格式
"""

import httpx
import json
import asyncio

async def test_ollama_stream():
    """直接测试Ollama的流式响应"""
    url = "http://localhost:11434/api/chat"
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": True
    }
    
    print("🧪 直接测试Ollama流式响应...")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(data, indent=2)}")
    
    try:
        async with httpx.AsyncClient() as client:
            async with client.stream('POST', url, json=data, timeout=30.0) as response:
                print(f"响应状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code != 200:
                    error_body = await response.aread()
                    print(f"错误响应: {error_body.decode()}")
                    return
                
                print("\n📝 原始Ollama流式响应:")
                chunk_count = 0
                async for line in response.aiter_lines():
                    if line.strip():
                        chunk_count += 1
                        print(f"  第{chunk_count}行: {repr(line)}")
                        
                        # 尝试解析JSON
                        try:
                            chunk = json.loads(line)
                            print(f"    解析成功: {chunk}")
                            
                            if chunk.get('done', False):
                                print("    ✅ 收到完成标记")
                                break
                        except json.JSONDecodeError as e:
                            print(f"    ❌ JSON解析失败: {e}")
                        
                        if chunk_count > 10:  # 限制输出
                            print("    ... (限制输出)")
                            break
                
                print(f"\n总共接收到 {chunk_count} 行数据")
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_ollama_stream())
