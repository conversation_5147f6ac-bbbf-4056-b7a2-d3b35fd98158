#!/usr/bin/env python3
"""
测试流式响应修复
"""

import requests
import json
import time

def test_stream_response():
    """测试修复后的流式响应"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "请说一句简短的问候"}],
        "stream": True
    }
    
    print("🧪 测试修复后的流式响应...")
    print(f"请求URL: {url}")
    
    try:
        response = requests.post(url, json=data, headers=headers, stream=True, timeout=30)
        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type')}")
        
        if response.status_code == 200:
            print("\n📝 流式响应内容:")
            chunk_count = 0
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    print(f"  {decoded_line}")
                    chunk_count += 1
                    
                    # 解析JSON检查格式
                    if decoded_line.startswith('data: ') and not decoded_line.endswith('[DONE]'):
                        try:
                            json_str = decoded_line[6:]  # 移除 'data: '
                            chunk_data = json.loads(json_str)
                            
                            # 检查是否是OpenAI格式
                            if 'choices' in chunk_data and chunk_data['choices']:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice:
                                    content = choice['delta'].get('content', '')
                                    print(f"    ✅ OpenAI格式正确，内容: '{content}'")
                                else:
                                    print(f"    ❌ 缺少delta字段")
                            else:
                                print(f"    ❌ 不是OpenAI格式: {chunk_data}")
                        except json.JSONDecodeError as e:
                            print(f"    ❌ JSON解析错误: {e}")
                    
                    if "data: [DONE]" in decoded_line:
                        print("  ✅ 流式响应正常结束")
                        break
                        
                    if chunk_count > 20:  # 防止无限循环
                        print("  ⚠️ 响应过长，停止读取")
                        break
                        
            print(f"\n总共接收到 {chunk_count} 个数据块")
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_non_stream_response():
    """测试非流式响应作为对比"""
    url = "http://localhost:8888/v1/chat/completions"
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    data = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": False
    }
    
    print("\n🧪 测试非流式响应（对比）...")
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 非流式响应成功")
            if 'choices' in result and result['choices']:
                content = result['choices'][0].get('message', {}).get('content', '')
                print(f"响应内容: {content}")
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    # 先测试服务器是否运行
    try:
        response = requests.get("http://localhost:8888/", timeout=5)
        print("✅ 服务器正在运行")
        
        # 测试非流式响应
        test_non_stream_response()
        
        # 测试流式响应
        test_stream_response()
        
    except Exception as e:
        print(f"❌ 服务器未运行或无法连接: {e}")
        print("请先启动服务器：python test_import.py")
