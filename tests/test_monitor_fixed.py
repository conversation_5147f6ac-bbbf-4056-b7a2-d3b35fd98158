#!/usr/bin/env python3
"""
GPU监控页面修复验证脚本
"""

import requests
import json
import time

def test_monitor_apis():
    """测试监控页面的无认证API"""
    print("🧪 测试GPU监控页面修复效果...")
    
    base_url = "http://localhost:8888"
    
    # 1. 测试监控页面访问
    try:
        response = requests.get(f"{base_url}/monitor", timeout=10)
        if response.status_code == 200:
            print("✅ 监控页面访问成功")
            print(f"   页面大小: {len(response.content)} bytes")
        else:
            print(f"❌ 监控页面访问失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 监控页面访问异常: {str(e)}")
        return False
    
    # 2. 测试无认证状态API
    try:
        response = requests.get(f"{base_url}/monitor/api/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 无认证状态API正常")
            print(f"   状态: {data.get('status', 'unknown')}")
            print(f"   GPU数量: {len(data.get('gpu_info', []))}")
            print(f"   服务器数量: {data.get('total_servers', 0)}")
            
            # 显示GPU详细信息
            for gpu in data.get('gpu_info', []):
                gpu_id = gpu.get('gpu_id', 'unknown')
                name = gpu.get('name', 'Unknown')
                memory_usage = gpu.get('memory_usage_percent', 0)
                llm_tasks = gpu.get('llm_tasks', 0)
                ocr_tasks = gpu.get('ocr_tasks', 0)
                layout_tasks = gpu.get('layout_tasks', 0)
                
                print(f"   📊 GPU {gpu_id} ({name}):")
                print(f"      显存使用: {memory_usage:.1f}%")
                print(f"      任务分布: LLM={llm_tasks}, OCR={ocr_tasks}, Layout={layout_tasks}")
                
        else:
            print(f"❌ 无认证状态API失败: HTTP {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 无认证状态API异常: {str(e)}")
        return False
    
    # 3. 测试无认证队列API
    try:
        response = requests.get(f"{base_url}/monitor/api/queue", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 无认证队列API正常")
            print(f"   总任务数: {data.get('total_tasks', 0)}")
            print(f"   队列长度: {data.get('queue_size', 0)}")
            
            # 显示GPU状态
            gpu_status = data.get('gpu_status', {})
            for gpu_id, status in gpu_status.items():
                print(f"   🎮 {gpu_id}: LLM={status.get('llm_tasks', 0)}, OCR={status.get('ocr_tasks', 0)}")
                
        else:
            print(f"❌ 无认证队列API失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无认证队列API异常: {str(e)}")
        return False
    
    # 4. 验证原有认证API仍然需要token
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 403:
            print("✅ 原有认证API正确要求token")
        else:
            print(f"⚠️  原有认证API状态异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 原有认证API测试异常: {str(e)}")
    
    return True

def test_browser_compatibility():
    """测试浏览器兼容性"""
    print("\n🌐 测试浏览器兼容性...")
    
    base_url = "http://localhost:8888"
    
    # 模拟不同浏览器的User-Agent
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
    ]
    
    for i, ua in enumerate(user_agents):
        try:
            headers = {"User-Agent": ua}
            response = requests.get(f"{base_url}/monitor", headers=headers, timeout=5)
            
            if response.status_code == 200:
                browser_type = ["Chrome/Windows", "Chrome/macOS", "Chrome/Linux", "Safari/iOS"][i]
                print(f"   ✅ {browser_type}: 正常")
            else:
                print(f"   ❌ 浏览器兼容性问题: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 浏览器测试异常: {str(e)}")

def test_performance():
    """测试性能"""
    print("\n⚡ 测试API性能...")
    
    base_url = "http://localhost:8888"
    
    # 测试API响应时间
    endpoints = [
        "/monitor",
        "/monitor/api/status", 
        "/monitor/api/queue"
    ]
    
    for endpoint in endpoints:
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                print(f"   ✅ {endpoint}: {response_time:.1f}ms")
            else:
                print(f"   ❌ {endpoint}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {endpoint}: 异常 - {str(e)}")

def show_final_summary():
    """显示最终总结"""
    print("\n" + "="*60)
    print("🎉 GPU监控页面修复完成！")
    print("="*60)
    
    print("\n📋 修复内容:")
    print("  ✅ 创建了无认证的监控API接口")
    print("     • /monitor/api/status - GPU状态和系统信息")
    print("     • /monitor/api/queue - 任务队列统计")
    print("  ✅ 修改了前端JavaScript使用新API")
    print("  ✅ 保持了原有认证API的安全性")
    
    print("\n🎯 访问方式:")
    print("  • 监控页面: http://localhost:8888/monitor")
    print("  • 状态API: http://localhost:8888/monitor/api/status")
    print("  • 队列API: http://localhost:8888/monitor/api/queue")
    
    print("\n📊 监控功能:")
    print("  • 实时GPU状态 (显存使用、任务分布)")
    print("  • 任务队列监控 (队列长度、处理状态)")
    print("  • Ollama服务器状态")
    print("  • 自动刷新 (3秒间隔)")
    print("  • 响应式设计 (支持移动端)")
    
    print("\n🔒 安全性:")
    print("  • 监控API无需认证 (只读数据)")
    print("  • 原有业务API仍需token认证")
    print("  • 不暴露敏感信息")
    
    print("\n🚀 现在可以正常使用GPU监控页面了！")

def main():
    """主函数"""
    print("🔧 GPU监控页面修复验证")
    print("="*50)
    
    # 检查应用是否运行
    try:
        response = requests.get("http://localhost:8888/health", timeout=5)
        if response.status_code == 200:
            print("✅ HNGPT应用正在运行")
        else:
            print("❌ HNGPT应用可能未运行或有问题")
            return
    except Exception as e:
        print(f"❌ 无法连接到HNGPT应用: {str(e)}")
        return
    
    # 运行测试
    success = test_monitor_apis()
    if success:
        test_browser_compatibility()
        test_performance()
        show_final_summary()
    else:
        print("\n❌ 监控页面修复验证失败，请检查配置")

if __name__ == "__main__":
    main()
