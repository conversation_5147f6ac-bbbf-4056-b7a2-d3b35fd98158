#!/usr/bin/env python3
"""
简单的服务器测试，确认路由问题
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/v1/chat/completions")
async def chat():
    return {"message": "Chat endpoint working"}

@app.post("/v1/embeddings")
async def embeddings():
    return {"message": "Embeddings endpoint working"}

@app.post("/ocr")
async def ocr():
    return {"message": "OCR endpoint working"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8888)
