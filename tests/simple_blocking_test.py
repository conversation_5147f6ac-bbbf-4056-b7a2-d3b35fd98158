#!/usr/bin/env python3
"""
简化的阻塞测试脚本
快速检测/v1/chat/completions和/v1/embeddings接口是否会阻塞
"""

import asyncio
import aiohttp
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8888"
TOKEN = "startfrom2023"
HEADERS = {"Authorization": f"Bearer {TOKEN}"}

async def test_chat_api(session, message, request_id):
    """测试chat API"""
    start_time = time.time()
    
    payload = {
        "model": "hngpt",
        "messages": [{"role": "user", "content": message}],
        "max_tokens": 50,
        "stream": False
    }
    
    try:
        async with session.post(
            f"{BASE_URL}/v1/chat/completions",
            json=payload,
            headers=HEADERS,
            timeout=aiohttp.ClientTimeout(total=30)
        ) as response:
            response_time = time.time() - start_time
            success = response.status == 200
            
            if success:
                result = await response.json()
                content = result["choices"][0]["message"]["content"]
                return f"Chat-{request_id}: ✅ {response_time:.2f}s - {content[:30]}..."
            else:
                error = await response.text()
                return f"Chat-{request_id}: ❌ {response_time:.2f}s - {error[:50]}..."
                
    except Exception as e:
        response_time = time.time() - start_time
        return f"Chat-{request_id}: ❌ {response_time:.2f}s - {str(e)[:50]}..."

async def test_embedding_api(session, text, model, request_id):
    """测试embedding API"""
    start_time = time.time()
    
    payload = {
        "model": model,
        "input": text
    }
    
    try:
        async with session.post(
            f"{BASE_URL}/v1/embeddings",
            json=payload,
            headers=HEADERS,
            timeout=aiohttp.ClientTimeout(total=20)
        ) as response:
            response_time = time.time() - start_time
            success = response.status == 200
            
            if success:
                result = await response.json()
                dim = len(result["data"][0]["embedding"])
                return f"Emb-{request_id}({model}): ✅ {response_time:.2f}s - {dim}维"
            else:
                error = await response.text()
                return f"Emb-{request_id}({model}): ❌ {response_time:.2f}s - {error[:30]}..."
                
    except Exception as e:
        response_time = time.time() - start_time
        return f"Emb-{request_id}({model}): ❌ {response_time:.2f}s - {str(e)[:30]}..."

async def run_blocking_test():
    """运行阻塞测试"""
    print("🚀 API阻塞测试")
    print("=" * 50)
    
    # 测试数据
    chat_messages = [
        "你好",
        "什么是AI？", 
        "解释机器学习",
        "深度学习应用",
        "NLP发展趋势"
    ]
    
    embedding_texts = [
        "Llamas eat bananas",
        "Machine learning",
        "Deep neural networks", 
        "Natural language processing",
        "Computer vision"
    ]
    
    async with aiohttp.ClientSession() as session:
        # 测试1: 混合并发 (模拟真实使用场景)
        print(f"\n🧪 测试1: 混合并发 (3个Chat + 4个Embedding)")
        print(f"-" * 40)
        
        tasks = []
        start_time = time.time()
        
        # 添加chat任务
        for i in range(3):
            message = chat_messages[i]
            task = test_chat_api(session, message, i+1)
            tasks.append(task)
        
        # 添加embedding任务
        for i in range(4):
            text = embedding_texts[i]
            model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
            task = test_embedding_api(session, text, model, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送7个并发请求...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 全部完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        # 等待间隔
        await asyncio.sleep(2)
        
        # 测试2: 高并发Chat (检查Chat API阻塞)
        print(f"\n🧪 测试2: 高并发Chat (5个并发)")
        print(f"-" * 40)
        
        tasks = []
        start_time = time.time()
        
        for i in range(5):
            message = chat_messages[i]
            task = test_chat_api(session, message, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送5个Chat请求...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 全部完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        # 等待间隔
        await asyncio.sleep(2)
        
        # 测试3: 高并发Embedding (检查Embedding API阻塞)
        print(f"\n🧪 测试3: 高并发Embedding (8个并发)")
        print(f"-" * 40)
        
        tasks = []
        start_time = time.time()
        
        for i in range(8):
            text = embedding_texts[i % len(embedding_texts)]
            model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
            task = test_embedding_api(session, text, model, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送8个Embedding请求...")
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 全部完成，总耗时: {total_time:.2f}s")
        for result in results:
            print(f"  {result}")
        
        # 等待间隔
        await asyncio.sleep(2)
        
        # 测试4: 极限并发 (检查系统极限)
        print(f"\n🧪 测试4: 极限并发 (10个Chat + 10个Embedding)")
        print(f"-" * 40)
        
        tasks = []
        start_time = time.time()
        
        # 10个chat
        for i in range(10):
            message = chat_messages[i % len(chat_messages)]
            task = test_chat_api(session, message, i+1)
            tasks.append(task)
        
        # 10个embedding
        for i in range(10):
            text = embedding_texts[i % len(embedding_texts)]
            model = "hngpt-embedding" if i % 2 == 0 else "hngpt-embedding"
            task = test_embedding_api(session, text, model, i+1)
            tasks.append(task)
        
        print(f"📤 {datetime.now().strftime('%H:%M:%S')} - 发送20个并发请求...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        print(f"📥 {datetime.now().strftime('%H:%M:%S')} - 全部完成，总耗时: {total_time:.2f}s")
        
        success_count = 0
        for result in results:
            if isinstance(result, str):
                print(f"  {result}")
                if "✅" in result:
                    success_count += 1
            else:
                print(f"  异常: {result}")
        
        print(f"\n📊 极限测试结果: {success_count}/20 成功")

def analyze_blocking_indicators():
    """分析阻塞指标"""
    print(f"\n🔍 阻塞分析指标:")
    print(f"=" * 30)
    print(f"✅ 无阻塞指标:")
    print(f"  - 并发请求响应时间相近")
    print(f"  - 总耗时接近最慢单个请求时间")
    print(f"  - 成功率高 (>90%)")
    print(f"  - 无超时错误")
    
    print(f"\n⚠️ 可能阻塞指标:")
    print(f"  - 并发请求响应时间差异很大")
    print(f"  - 总耗时远超最慢单个请求时间")
    print(f"  - 出现大量超时错误")
    print(f"  - 后面的请求明显比前面的慢")
    
    print(f"\n❌ 严重阻塞指标:")
    print(f"  - 请求按顺序完成 (无并发效果)")
    print(f"  - 总耗时 = 单个请求时间 × 请求数量")
    print(f"  - 大量请求失败或超时")

async def main():
    """主函数"""
    print("🧪 API阻塞检测工具")
    print("=" * 30)
    print(f"目标: {BASE_URL}")
    print(f"Token: {TOKEN}")
    
    await run_blocking_test()
    analyze_blocking_indicators()
    
    print(f"\n✅ 测试完成!")
    print(f"\n💡 如何判断:")
    print(f"  - 看总耗时是否合理 (接近最慢单个请求)")
    print(f"  - 看是否有明显的排队现象")
    print(f"  - 看不同API类型是否相互影响")

if __name__ == "__main__":
    asyncio.run(main())
