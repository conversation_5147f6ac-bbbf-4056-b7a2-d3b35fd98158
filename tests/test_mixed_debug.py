#!/usr/bin/env python3
"""
调试混合并发测试
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_concurrent_capacity import ConcurrencyTester

async def debug_test():
    """调试测试"""
    print("🧪 调试混合并发功能")
    
    tester = ConcurrencyTester()
    
    # 小规模测试：6个请求，并发级别3
    try:
        results = await tester.test_mixed_concurrent_requests(
            total_requests=6,
            concurrent_level=3
        )
        
        print(f"\n📊 测试完成!")
        print(f"总请求数: {results['total_requests']}")
        print(f"并发级别: {results['concurrent_level']}")
        print(f"总耗时: {results['total_time']:.2f}s")
        
        # 检查结果结构
        print(f"\n🔍 结果结构检查:")
        for i, result in enumerate(results['results'][:3]):  # 只检查前3个
            print(f"  结果 {i}: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_test())
