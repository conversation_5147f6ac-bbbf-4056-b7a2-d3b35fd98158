#!/usr/bin/env python3
"""
全面测试所有接口的异步和同步模式
"""

import asyncio
import aiohttp
import time
import json
import base64
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AllModesTest:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }

    async def test_chat_sync(self, session):
        """测试Chat同步模式"""
        logging.info("🧪 测试Chat同步模式")
        
        payload = {
            "model": "hngpt",
            "messages": [{"role": "user", "content": "Hello, this is a sync test"}],
            "stream": False
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions", 
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                    logging.info(f"✅ Chat同步成功 - 响应时间: {response_time:.3f}s")
                    logging.info(f"📝 回复内容: {content[:100]}...")
                    return {"success": True, "response_time": response_time, "mode": "sync"}
                else:
                    logging.error(f"❌ Chat同步失败 - 状态码: {response.status}")
                    return {"success": False, "response_time": response_time, "mode": "sync", "status_code": response.status}
                    
        except Exception as e:
            logging.error(f"❌ Chat同步异常: {e}")
            return {"success": False, "response_time": time.time() - start_time, "mode": "sync", "error": str(e)}

    async def test_chat_stream(self, session):
        """测试Chat流式模式"""
        logging.info("🧪 测试Chat流式模式")
        
        payload = {
            "model": "hngpt",
            "messages": [{"role": "user", "content": "Hello, this is a stream test"}],
            "stream": True
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions", 
                                   json=payload, headers=self.headers, timeout=30) as response:
                
                if response.status == 200:
                    content_parts = []
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: ') and not line_str.endswith('[DONE]'):
                                try:
                                    json_str = line_str[6:]  # 移除 'data: '
                                    chunk = json.loads(json_str)
                                    if 'choices' in chunk and chunk['choices']:
                                        delta = chunk['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            content_parts.append(delta['content'])
                                except json.JSONDecodeError:
                                    continue
                            elif "data: [DONE]" in line_str:
                                break
                    
                    response_time = time.time() - start_time
                    full_content = ''.join(content_parts)
                    logging.info(f"✅ Chat流式成功 - 响应时间: {response_time:.3f}s")
                    logging.info(f"📝 流式内容: {full_content[:100]}...")
                    return {"success": True, "response_time": response_time, "mode": "stream", "chunks": len(content_parts)}
                else:
                    response_time = time.time() - start_time
                    logging.error(f"❌ Chat流式失败 - 状态码: {response.status}")
                    return {"success": False, "response_time": response_time, "mode": "stream", "status_code": response.status}
                    
        except Exception as e:
            logging.error(f"❌ Chat流式异常: {e}")
            return {"success": False, "response_time": time.time() - start_time, "mode": "stream", "error": str(e)}

    async def test_embedding(self, session):
        """测试Embedding（只有同步模式）"""
        logging.info("🧪 测试Embedding")
        
        payload = {
            "model": "hngpt-embedding",
            "input": "This is a test text for embedding"
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/embeddings", 
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    embeddings = result.get('data', [{}])[0].get('embedding', [])
                    logging.info(f"✅ Embedding成功 - 响应时间: {response_time:.3f}s")
                    logging.info(f"📝 向量维度: {len(embeddings)}")
                    return {"success": True, "response_time": response_time, "mode": "sync", "dimensions": len(embeddings)}
                else:
                    logging.error(f"❌ Embedding失败 - 状态码: {response.status}")
                    return {"success": False, "response_time": response_time, "mode": "sync", "status_code": response.status}
                    
        except Exception as e:
            logging.error(f"❌ Embedding异常: {e}")
            return {"success": False, "response_time": time.time() - start_time, "mode": "sync", "error": str(e)}

    async def test_ocr_sync(self, session):
        """测试OCR同步模式"""
        logging.info("🧪 测试OCR同步模式")
        
        # 读取测试图片
        test_image_path = "/workspace/hngpt/tests/document.png"
        try:
            with open(test_image_path, "rb") as image_file:
                image_data = image_file.read()
        except Exception as e:
            logging.error(f"❌ 无法读取测试图片: {e}")
            return {"success": False, "response_time": 0, "mode": "sync", "error": "Cannot read test image"}
        
        headers = {
            "Authorization": f"Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        params = {
            "wait": "true",
            "timeout": "30.0"
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/ocr/binary/", 
                                   data=image_data, headers=headers, params=params, timeout=60) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    text_content = result.get('text', '') if isinstance(result, dict) else str(result)
                    logging.info(f"✅ OCR同步成功 - 响应时间: {response_time:.3f}s")
                    logging.info(f"📝 识别文本长度: {len(text_content)} 字符")
                    return {"success": True, "response_time": response_time, "mode": "sync", "text_length": len(text_content)}
                else:
                    logging.error(f"❌ OCR同步失败 - 状态码: {response.status}")
                    return {"success": False, "response_time": response_time, "mode": "sync", "status_code": response.status}
                    
        except Exception as e:
            logging.error(f"❌ OCR同步异常: {e}")
            return {"success": False, "response_time": time.time() - start_time, "mode": "sync", "error": str(e)}

    async def test_ocr_async(self, session):
        """测试OCR异步模式"""
        logging.info("🧪 测试OCR异步模式")
        
        # 读取测试图片
        test_image_path = "/workspace/hngpt/tests/document.png"
        try:
            with open(test_image_path, "rb") as image_file:
                image_data = image_file.read()
        except Exception as e:
            logging.error(f"❌ 无法读取测试图片: {e}")
            return {"success": False, "response_time": 0, "mode": "async", "error": "Cannot read test image"}
        
        headers = {
            "Authorization": f"Bearer startfrom2023",
            "Content-Type": "application/octet-stream"
        }
        
        params = {
            "wait": "false",
            "timeout": "60.0"
        }
        
        start_time = time.time()
        try:
            # 提交异步任务
            async with session.post(f"{self.base_url}/ocr/binary/", 
                                   data=image_data, headers=headers, params=params, timeout=30) as response:
                submit_time = time.time() - start_time
                
                if response.status == 202:
                    result = await response.json()
                    task_id = result.get('task_id')
                    logging.info(f"✅ OCR异步任务提交成功 - 任务ID: {task_id}, 提交时间: {submit_time:.3f}s")
                    
                    if task_id:
                        # 轮询任务结果
                        max_polls = 20
                        poll_interval = 1
                        
                        for i in range(max_polls):
                            await asyncio.sleep(poll_interval)
                            
                            try:
                                async with session.get(f"{self.base_url}/ocr/result/{task_id}",
                                                     headers={"Authorization": "Bearer startfrom2023"},
                                                     timeout=10) as result_response:
                                    
                                    if result_response.status == 200:
                                        task_result = await result_response.json()
                                        status = task_result.get('status', 'unknown')
                                        
                                        if status == 'completed':
                                            total_time = time.time() - start_time
                                            ocr_result = task_result.get('result', {})
                                            text_content = ocr_result.get('text', '') if isinstance(ocr_result, dict) else str(ocr_result)
                                            logging.info(f"✅ OCR异步完成 - 总时间: {total_time:.3f}s, 轮询次数: {i+1}")
                                            logging.info(f"📝 识别文本长度: {len(text_content)} 字符")
                                            return {"success": True, "response_time": total_time, "mode": "async", 
                                                   "polls": i+1, "text_length": len(text_content)}
                                        elif status == 'failed':
                                            logging.error(f"❌ OCR异步任务失败: {task_result.get('error', '未知错误')}")
                                            return {"success": False, "response_time": time.time() - start_time, 
                                                   "mode": "async", "error": "Task failed"}
                                        elif status == 'processing':
                                            progress = task_result.get('progress', 0)
                                            logging.info(f"🔄 OCR异步处理中... 进度: {progress}%")
                                            continue
                                    else:
                                        logging.warning(f"⚠️ 获取任务状态失败: {result_response.status}")
                                        
                            except Exception as e:
                                logging.warning(f"⚠️ 轮询异常: {e}")
                                continue
                        
                        logging.error(f"❌ OCR异步超时 - 轮询了{max_polls}次")
                        return {"success": False, "response_time": time.time() - start_time, 
                               "mode": "async", "error": "Polling timeout"}
                    else:
                        logging.error(f"❌ OCR异步任务ID为空")
                        return {"success": False, "response_time": submit_time, "mode": "async", "error": "No task ID"}
                else:
                    logging.error(f"❌ OCR异步提交失败 - 状态码: {response.status}")
                    return {"success": False, "response_time": submit_time, "mode": "async", "status_code": response.status}
                    
        except Exception as e:
            logging.error(f"❌ OCR异步异常: {e}")
            return {"success": False, "response_time": time.time() - start_time, "mode": "async", "error": str(e)}

    async def run_all_tests(self):
        """运行所有测试"""
        logging.info("🚀 开始全面测试所有接口的异步和同步模式")
        
        results = {}
        
        async with aiohttp.ClientSession() as session:
            # 测试Chat同步模式
            results["chat_sync"] = await self.test_chat_sync(session)
            await asyncio.sleep(2)
            
            # 测试Chat流式模式
            results["chat_stream"] = await self.test_chat_stream(session)
            await asyncio.sleep(2)
            
            # 测试Embedding
            results["embedding"] = await self.test_embedding(session)
            await asyncio.sleep(2)
            
            # 测试OCR同步模式
            results["ocr_sync"] = await self.test_ocr_sync(session)
            await asyncio.sleep(2)
            
            # 测试OCR异步模式
            results["ocr_async"] = await self.test_ocr_async(session)
        
        return results

    def analyze_results(self, results):
        """分析测试结果"""
        logging.info(f"\n{'='*80}")
        logging.info("📊 全面测试结果分析")
        logging.info(f"{'='*80}")
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results.values() if r.get('success', False))
        
        logging.info(f"📈 总体统计:")
        logging.info(f"  总测试数: {total_tests}")
        logging.info(f"  成功数: {successful_tests}")
        logging.info(f"  失败数: {total_tests - successful_tests}")
        logging.info(f"  成功率: {(successful_tests / total_tests) * 100:.1f}%")
        
        logging.info(f"\n📋 详细结果:")
        for test_name, result in results.items():
            status = "✅" if result.get('success', False) else "❌"
            response_time = result.get('response_time', 0)
            mode = result.get('mode', 'unknown')
            
            logging.info(f"  {status} {test_name.upper().replace('_', ' ')}: {response_time:.3f}s ({mode})")
            
            if not result.get('success', False):
                error = result.get('error', result.get('status_code', 'Unknown error'))
                logging.info(f"      错误: {error}")
            else:
                # 显示额外信息
                if 'dimensions' in result:
                    logging.info(f"      向量维度: {result['dimensions']}")
                elif 'text_length' in result:
                    logging.info(f"      文本长度: {result['text_length']} 字符")
                elif 'chunks' in result:
                    logging.info(f"      流式块数: {result['chunks']}")
                if 'polls' in result:
                    logging.info(f"      轮询次数: {result['polls']}")

async def main():
    """主函数"""
    # 检查服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/") as response:
                if response.status != 200:
                    logging.error("❌ 服务器连接失败，请确保服务器正在运行")
                    return
    except Exception as e:
        logging.error(f"❌ 无法连接到服务器: {e}")
        return
    
    # 运行测试
    tester = AllModesTest()
    results = await tester.run_all_tests()
    
    # 分析结果
    tester.analyze_results(results)
    
    # 保存结果
    filename = "all_modes_test_results.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logging.info(f"\n✅ 全面测试完成，结果已保存到 {filename}")

if __name__ == "__main__":
    asyncio.run(main())
