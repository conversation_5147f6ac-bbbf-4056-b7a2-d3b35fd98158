#!/usr/bin/env python3
"""
生成版面分析效果对比图 - 原图 vs 版面提取结果
"""
import cv2
import numpy as np
import rec
from PIL import Image, ImageDraw, ImageFont
import json
import timefinder

def calculate_overlap_ratio(box1, box2):
    """
    计算两个矩形框的重叠比例
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # 计算重叠区域
    x1_overlap = max(x1_1, x1_2)
    y1_overlap = max(y1_1, y1_2)
    x2_overlap = min(x2_1, x2_2)
    y2_overlap = min(y2_1, y2_2)

    if x2_overlap <= x1_overlap or y2_overlap <= y1_overlap:
        return 0.0

    # 重叠面积
    overlap_area = (x2_overlap - x1_overlap) * (y2_overlap - y1_overlap)

    # OCR文本框面积
    ocr_area = (x2_2 - x1_2) * (y2_2 - y1_2)

    if ocr_area == 0:
        return 0.0

    return overlap_area / ocr_area

def calculate_iou(box1, box2):
    """
    计算两个矩形框的IoU (Intersection over Union)
    box1, box2: [x1, y1, x2, y2]
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # 计算交集区域
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)

    # 如果没有交集
    if x1_inter >= x2_inter or y1_inter >= y2_inter:
        return 0.0

    # 计算交集面积
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

    # 计算两个框的面积
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)

    # 计算并集面积
    union_area = area1 + area2 - inter_area

    if union_area == 0:
        return 0.0

    return inter_area / union_area

def sort_text_boxes(text_boxes):
    """
    对文本框按从上到下、从左到右的顺序排序
    """
    if not text_boxes:
        return []

    # 按y坐标排序（从上到下）
    text_boxes.sort(key=lambda box: box['coordinate'][1])

    # 对于y坐标相近的文本框，按x坐标排序（从左到右）
    sorted_boxes = []
    current_line = []
    current_y = None
    line_height_threshold = 20  # 行高阈值

    for box in text_boxes:
        y = box['coordinate'][1]

        if current_y is None or abs(y - current_y) <= line_height_threshold:
            # 同一行
            current_line.append(box)
            if current_y is None:
                current_y = y
        else:
            # 新的一行
            if current_line:
                # 对当前行按x坐标排序
                current_line.sort(key=lambda b: b['coordinate'][0])
                sorted_boxes.extend(current_line)
            current_line = [box]
            current_y = y

    # 处理最后一行
    if current_line:
        current_line.sort(key=lambda b: b['coordinate'][0])
        sorted_boxes.extend(current_line)

    return sorted_boxes

def match_layout_and_ocr(layout_results, ocr_results, original_image_shape):
    """
    匹配版面区域和OCR文本
    使用正确的思路：每个OCR文本框只与IoU最大的版面区域匹配
    """
    original_height, original_width = original_image_shape[:2]

    print(f"原图尺寸: {original_width}x{original_height}")
    print("注意：Layout和OCR的坐标都已经是原图坐标，直接使用")

    # 预处理OCR结果，转换为统一格式
    processed_ocr_results = []
    if ocr_results and len(ocr_results) > 0:
        for idx, ocr_result in enumerate(ocr_results):
            if len(ocr_result) >= 3:
                ocr_coordinate = ocr_result[0]  # OCR文本框坐标
                text = ocr_result[1]
                confidence = ocr_result[2]

                if confidence > 0.3 and text.strip():
                    # OCR坐标已经是原图坐标，直接转换为[x1, y1, x2, y2]格式
                    if len(ocr_coordinate) == 4 and len(ocr_coordinate[0]) == 2:
                        # 四个点的格式 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                        ocr_orig_x1 = min(point[0] for point in ocr_coordinate)
                        ocr_orig_y1 = min(point[1] for point in ocr_coordinate)
                        ocr_orig_x2 = max(point[0] for point in ocr_coordinate)
                        ocr_orig_y2 = max(point[1] for point in ocr_coordinate)

                        processed_ocr_results.append({
                            "idx": idx,
                            "text": text,
                            "confidence": confidence,
                            "coordinate": [ocr_orig_x1, ocr_orig_y1, ocr_orig_x2, ocr_orig_y2],
                            "assigned_layout": -1  # 记录分配给哪个版面区域，-1表示未分配
                        })

    # 为每个OCR文本框找到IoU最大的版面区域
    for ocr_idx, ocr_data in enumerate(processed_ocr_results):
        ocr_box = ocr_data["coordinate"]
        max_iou = 0
        best_layout_idx = -1

        for layout_idx, layout_result in enumerate(layout_results):
            layout_box = layout_result['coordinate']
            iou = calculate_iou(ocr_box, layout_box)

            if iou > max_iou:
                max_iou = iou
                best_layout_idx = layout_idx

        # 每个OCR文本框都必须分配给IoU最大的版面区域，无论IoU值多小
        if best_layout_idx >= 0:
            processed_ocr_results[ocr_idx]["assigned_layout"] = best_layout_idx

    # 存储所有区域的信息
    regions_info = []

    for i, layout_result in enumerate(layout_results):
        cls_id = layout_result['cls_id']
        label = layout_result['label']
        score = layout_result['score']
        coordinate = layout_result['coordinate']

        # Layout坐标已经是原图坐标，直接使用
        orig_x1, orig_y1, orig_x2, orig_y2 = map(int, coordinate)

        print(f"处理版面区域 {i+1}: {label}")
        print(f"  原图坐标: ({orig_x1},{orig_y1},{orig_x2},{orig_y2})")

        # 收集分配给当前版面区域的OCR文本框
        matched_ocr_texts = []
        for ocr_data in processed_ocr_results:
            if ocr_data["assigned_layout"] == i:
                matched_ocr_texts.append({
                    "text": ocr_data["text"],
                    "confidence": ocr_data["confidence"],
                    "coordinate": ocr_data["coordinate"],
                    "overlap_ratio": 1.0  # 这里不再需要重叠比例，因为已经是最佳匹配
                })

        # 对匹配的文本框进行排序
        sorted_texts = sort_text_boxes(matched_ocr_texts)

        # 组合区域文本，保持原有的行结构
        region_text = ""
        if sorted_texts:
            # 按行组织文本，保持换行结构
            lines = []
            current_line = []
            current_y = None
            line_height_threshold = 20  # 行高阈值

            for text_box in sorted_texts:
                y = text_box['coordinate'][1]

                if current_y is None or abs(y - current_y) <= line_height_threshold:
                    # 同一行
                    current_line.append(text_box['text'])
                    if current_y is None:
                        current_y = y
                else:
                    # 新的一行
                    if current_line:
                        lines.append(" ".join(current_line))
                    current_line = [text_box['text']]
                    current_y = y

            # 处理最后一行
            if current_line:
                lines.append(" ".join(current_line))

            # 用换行符连接各行
            region_text = "\n".join(lines)
            print(f"  匹配到 {len(sorted_texts)} 个文本框: {region_text.replace(chr(10), ' ')[:50]}...")
        else:
            print(f"  未匹配到文本")

        # 存储区域信息（使用原图坐标）
        region_info = {
            "region_id": i + 1,
            "cls_id": cls_id,
            "label": label,
            "score": score,
            "coordinate": [orig_x1, orig_y1, orig_x2, orig_y2],  # 使用转换后的原图坐标
            "original_coordinate": coordinate,  # 保留原始Layout坐标
            "area": (orig_x2 - orig_x1) * (orig_y2 - orig_y1),
            "ocr_results": sorted_texts,
            "text_content": region_text.strip()
        }
        regions_info.append(region_info)

    print("📄 步骤4: 组合完整文本...")
    # 对版面区域按文档阅读顺序排序（从上到下）
    # 使用区域的Y坐标中心点进行排序
    regions_with_text = []
    seen_texts = set()  # 用于去重

    for r in regions_info:
        if r["text_content"].strip():
            text_content = r["text_content"].strip()
            # 跳过重复的文本内容
            if text_content in seen_texts:
                print(f"  跳过重复文本: {text_content[:50]}...")
                continue
            seen_texts.add(text_content)

            _, y1, _, y2 = r["coordinate"]
            center_y = (y1 + y2) / 2
            regions_with_text.append({
                "region": r,
                "center_y": center_y,
                "text_content": text_content,
                "label": r["label"]
            })

    # 按Y坐标中心点排序（从上到下）
    regions_with_text.sort(key=lambda x: x["center_y"])

    # 组合完整文本
    full_text_parts = []
    for item in regions_with_text:
        full_text_parts.append(item["text_content"])
        print(f"  添加文本 (Y={item['center_y']:.1f}): {item['text_content'][:50]}...")

    full_text = "\n\n".join(full_text_parts)

    return {
        "layout_results": regions_info,
        "full_text": full_text,
        "text_length": len(full_text)
    }

def draw_layout_and_ocr_results(image: np.ndarray, layout_results: list, ocr_results: list) -> np.ndarray:
    """绘制版面分析结果和OCR结果 - 同时显示两种框"""
    from PIL import Image, ImageDraw, ImageFont

    # 转换为PIL图像
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)

    # 版面类别颜色配置（浅色）
    layout_colors = {
        'text': (102, 204, 102),           # 清新绿色 - 正文
        'paragraph_title': (102, 178, 255), # 天空蓝色 - 段落标题
        'doc_title': (255, 153, 153),      # 粉红色 - 文档标题
        'seal': (204, 153, 255),           # 淡紫色 - 印章
        'number': (255, 204, 153),         # 桃色 - 数字
        'figure': (153, 255, 204),         # 薄荷绿 - 图片
        'table': (255, 204, 204),          # 浅粉色 - 表格
        'figure_caption': (204, 255, 153), # 浅绿色 - 图片标题
    }

    # OCR框颜色（深色）
    ocr_color = (255, 0, 0)  # 红色
    
    # 尝试加载支持中文的字体
    font_small = None
    chinese_font_paths = [
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
        "/usr/share/fonts/truetype/arphic/uming.ttc",
        "/usr/share/fonts/truetype/arphic/ukai.ttc",
    ]

    for font_path in chinese_font_paths:
        try:
            font_small = ImageFont.truetype(font_path, 14)
            break
        except:
            continue

    if font_small is None:
        try:
            font_small = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
        except:
            font_small = ImageFont.load_default()
    
    # 按类别分组统计
    category_stats = {}
    for result in layout_results:
        label = result['label']
        category_stats[label] = category_stats.get(label, 0) + 1

    # 首先绘制版面区域（浅色）
    for i, result in enumerate(layout_results):
        x1, y1, x2, y2 = result['coordinate']
        label = result['label']
        score = result['score']

        # 获取颜色
        color = layout_colors.get(label, (128, 128, 128))
        
        # 绘制填充区域 - 半透明效果
        rect_points = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
        
        # 创建半透明层
        overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)
        overlay_draw.polygon(rect_points, fill=(*color, 80))  # 80/255 透明度
        
        # 合并半透明层
        pil_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay).convert('RGB')
        draw = ImageDraw.Draw(pil_image)
        
        # 绘制边框
        draw.polygon(rect_points, outline=color, width=3)
        
        # 绘制标签
        label_text = f"{label}"
        
        # 计算标签位置
        text_x = x1 + 5
        text_y = y1 + 5
        
        # 绘制标签背景
        try:
            text_bbox = draw.textbbox((text_x, text_y), label_text, font=font_small)
            bg_points = [
                (text_bbox[0] - 3, text_bbox[1] - 2),
                (text_bbox[2] + 3, text_bbox[1] - 2),
                (text_bbox[2] + 3, text_bbox[3] + 2),
                (text_bbox[0] - 3, text_bbox[3] + 2)
            ]
            draw.polygon(bg_points, fill=(255, 255, 255))
            draw.polygon(bg_points, outline=color, width=1)
        except:
            pass
        
        # 绘制标签文字
        draw.text((text_x, text_y), label_text, fill=(64, 64, 64), font=font_small)

    # 然后绘制OCR文本框（深色）
    for i, ocr_result in enumerate(ocr_results):
        # OCR结果的坐标已经是原图坐标，直接使用
        x1, y1, x2, y2 = ocr_result['coordinate']

        # 绘制OCR框边框（深色，较细）
        ocr_points = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
        draw.polygon(ocr_points, outline=ocr_color, width=2)

        # 可选：在OCR框上显示序号
        if i < 10:  # 只显示前10个框的序号，避免太乱
            try:
                ocr_text = str(i+1)
                ocr_text_x = x1 + 2
                ocr_text_y = y1 + 2

                # 绘制序号背景
                text_bbox = draw.textbbox((ocr_text_x, ocr_text_y), ocr_text, font=font_small)
                bg_points = [
                    (text_bbox[0] - 1, text_bbox[1] - 1),
                    (text_bbox[2] + 1, text_bbox[1] - 1),
                    (text_bbox[2] + 1, text_bbox[3] + 1),
                    (text_bbox[0] - 1, text_bbox[3] + 1)
                ]
                draw.polygon(bg_points, fill=(255, 255, 255))
                draw.polygon(bg_points, outline=ocr_color, width=1)

                # 绘制序号文字
                draw.text((ocr_text_x, ocr_text_y), ocr_text, fill=ocr_color, font=font_small)
            except:
                pass

    # 转换回OpenCV格式
    return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

def create_legend(categories_stats: dict, category_colors: dict) -> np.ndarray:
    """创建图例"""
    legend_height = 60 + len(categories_stats) * 25
    legend_width = 300
    legend = np.ones((legend_height, legend_width, 3), dtype=np.uint8) * 255
    
    # 转换为PIL进行绘制
    pil_legend = Image.fromarray(legend)
    draw = ImageDraw.Draw(pil_legend)
    
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
        font_title = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
    except:
        font = ImageFont.load_default()
        font_title = ImageFont.load_default()
    
    # 标题
    draw.text((10, 10), "版面类别统计", fill=(64, 64, 64), font=font_title)
    
    # 绘制图例项
    y_offset = 40
    for label, count in sorted(categories_stats.items()):
        color = category_colors.get(label, (128, 128, 128))
        
        # 绘制颜色块
        draw.rectangle([10, y_offset, 30, y_offset + 15], fill=color, outline=(64, 64, 64))
        
        # 绘制文字
        text = f"{label}: {count} 个"
        draw.text((35, y_offset), text, fill=(64, 64, 64), font=font)
        
        y_offset += 25
    
    return cv2.cvtColor(np.array(pil_legend), cv2.COLOR_RGB2BGR)

def create_text_panel_with_spatial_layout(regions_info: list, width: int, height: int) -> np.ndarray:
    """创建按空间位置布局的文本显示面板"""
    # 创建白色背景
    text_panel = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 转换为PIL进行文本绘制
    pil_panel = Image.fromarray(text_panel)
    draw = ImageDraw.Draw(pil_panel)

    # 使用指定的中文字体
    font_text = None
    specified_font_path = "/workspace/hngpt/models/simfang.ttf"

    try:
        font_text = ImageFont.truetype(specified_font_path, 12)
        print(f"✓ 成功加载指定字体: {specified_font_path}")
    except Exception as e:
        print(f"⚠️  无法加载指定字体 {specified_font_path}: {e}")
        # 使用默认字体
        font_text = ImageFont.load_default()

    # 收集所有有文本内容的区域，并去重
    text_regions = []
    seen_texts = set()  # 用于去重

    for r in regions_info:
        if r["text_content"].strip():
            text_content = r["text_content"].strip()
            # 跳过重复的文本内容
            if text_content in seen_texts:
                continue
            seen_texts.add(text_content)

            x1, y1, x2, y2 = r["coordinate"]
            text_regions.append({
                "coordinate": [x1, y1, x2, y2],
                "text_content": text_content,
                "label": r["label"]
            })

    # 按照原图中的相对位置在右边面板中绘制文本
    for region in text_regions:
        x1, y1, x2, y2 = region["coordinate"]
        text_content = region["text_content"]

        # 计算在右边面板中的相对位置
        # 保持相对位置关系，但缩放到面板尺寸
        panel_x = int(x1 * width / 500)  # 原图宽度500
        panel_y = int(y1 * height / 688)  # 原图高度688

        # 确保文本不超出面板边界
        panel_x = max(10, min(panel_x, width - 100))
        panel_y = max(20, min(panel_y, height - 30))

        # 保持文本原有格式，只在原有换行符处换行
        lines = text_content.split('\n')
        # 过滤掉空行
        lines = [line for line in lines if line.strip()]

        # 绘制文本行
        line_height = 16
        for i, line in enumerate(lines):
            text_y = panel_y + i * line_height
            if text_y + line_height > height - 10:
                break  # 防止超出边界

            # 绘制文本
            draw.text((panel_x, text_y), line, fill=(64, 64, 64), font=font_text)

    return cv2.cvtColor(np.array(pil_panel), cv2.COLOR_RGB2BGR)

def generate_layout_effect():
    """生成版面分析效果图"""
    print("🎨 生成版面分析效果对比图")
    print("=" * 50)

    # 加载图像
    image_path = "/workspace/hngpt/seal-hand.png"
    image = cv2.imread(image_path)
    if image is None:
        print(f"✗ 无法加载图像: {image_path}")
        return

    print(f"📷 原图尺寸: {image.shape[1]} x {image.shape[0]}")
    img_array = np.array(image, dtype=np.uint8)

    # 执行综合文档分析 (版面分析 + OCR)
    print("🚀 执行综合文档分析...")
    try:
        # 步骤1: 版面分析
        print("📋 步骤1: 执行版面分析...")
        layout_results = rec.rapid_layout_with_params(img_array, gpu_id=0)

        # 步骤2: 整页OCR识别
        print("📝 步骤2: 执行整页OCR识别...")
        ocr_results = rec.rapid_ocr_with_params(img_array, gpu_id=0, version='v5')

        # 步骤3: 匹配版面区域和OCR文本
        print("🔗 步骤3: 匹配版面区域和OCR文本...")
        analysis_result = match_layout_and_ocr(layout_results, ocr_results, image.shape)

        regions_info = analysis_result['layout_results']  # 这里包含了所有区域信息
        full_text = analysis_result['full_text']

        print(f"✓ 检测到 {len(regions_info)} 个版面区域")
        print(f"✓ 提取文本长度: {len(full_text)} 字符")

        # 统计类别
        category_stats = {}
        for result in regions_info:
            label = result['label']
            category_stats[label] = category_stats.get(label, 0) + 1

        print("📊 版面类别统计:")
        for label, count in sorted(category_stats.items()):
            print(f"  {label:15s}: {count} 个")

    except Exception as e:
        print(f"✗ 文档分析失败: {e}")
        return
    
    # 绘制版面分析结果和OCR结果
    print("🎨 绘制版面分析结果和OCR文本框...")
    # 将OCR结果转换为统一格式
    formatted_ocr_results = []
    if ocr_results:
        for ocr_result in ocr_results:
            if len(ocr_result) >= 3:
                ocr_coordinate = ocr_result[0]  # OCR文本框坐标
                text = ocr_result[1]
                confidence = ocr_result[2]

                if confidence > 0.3 and text.strip():
                    # 将OCR坐标转换为[x1, y1, x2, y2]格式
                    if len(ocr_coordinate) == 4 and len(ocr_coordinate[0]) == 2:
                        # 四个点的格式 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                        # 注意：这些坐标已经是原图坐标，不需要再转换
                        x1 = min(point[0] for point in ocr_coordinate)
                        y1 = min(point[1] for point in ocr_coordinate)
                        x2 = max(point[0] for point in ocr_coordinate)
                        y2 = max(point[1] for point in ocr_coordinate)

                        formatted_ocr_results.append({
                            'coordinate': [x1, y1, x2, y2],
                            'text': text,
                            'confidence': confidence
                        })

    layout_result_image = draw_layout_and_ocr_results(image, regions_info, formatted_ocr_results)
    
    # 创建图例
    category_colors = {
        'text': (102, 204, 102),
        'paragraph_title': (102, 178, 255),
        'doc_title': (255, 153, 153),
        'seal': (204, 153, 255),
        'number': (255, 204, 153),
        'figure': (153, 255, 204),
        'table': (255, 204, 204),
        'figure_caption': (204, 255, 153),
    }
    legend = create_legend(category_stats, category_colors)
    
    # 获取图像尺寸
    h, w = image.shape[:2]

    # 创建文本面板
    print("📝 创建文本面板...")
    text_panel = create_text_panel_with_spatial_layout(regions_info, w, h)

    # 创建对比效果图
    print("🖼️  创建对比效果图...")

    # 方案1: 左右对比 - 版面分析结果 vs 提取文本
    comparison_lr = np.zeros((h, w * 2 + 20, 3), dtype=np.uint8)
    comparison_lr[:, :w] = layout_result_image
    comparison_lr[:, w+20:] = text_panel

    # 添加分隔线
    comparison_lr[:, w:w+20] = [200, 200, 200]

    # 添加左侧标题
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(comparison_lr, "Layout Analysis Result", (50, 40), font, 1.0, (255, 255, 255), 2)
    # 右侧不添加标题，直接显示文本内容
    
    # 方案2: 上下对比 + 图例
    legend_h, legend_w = legend.shape[:2]
    comparison_tb = np.zeros((h * 2 + 40 + legend_h, max(w, legend_w), 3), dtype=np.uint8)
    comparison_tb.fill(240)  # 浅灰色背景
    
    # 放置原图
    comparison_tb[20:20+h, :w] = image
    
    # 放置版面分析结果
    comparison_tb[h+40:h+40+h, :w] = layout_result_image
    
    # 放置图例
    comparison_tb[h*2+40:h*2+40+legend_h, :legend_w] = legend
    
    # 添加标题
    cv2.putText(comparison_tb, "Original Document", (20, 15), font, 0.8, (64, 64, 64), 2)
    cv2.putText(comparison_tb, "Layout Analysis Result", (20, h + 35), font, 0.8, (64, 64, 64), 2)
    
    # 保存结果
    cv2.imwrite("layout_effect_comparison_lr.jpg", comparison_lr)
    cv2.imwrite("layout_effect_comparison_tb.jpg", comparison_tb)
    cv2.imwrite("layout_result_only.jpg", layout_result_image)
    cv2.imwrite("layout_legend.jpg", legend)
    
    print("✓ 效果图已生成:")
    print("  - layout_effect_comparison_lr.jpg (左右对比)")
    print("  - layout_effect_comparison_tb.jpg (上下对比+图例)")
    print("  - layout_result_only.jpg (仅版面分析结果)")
    print("  - layout_legend.jpg (图例)")
    
    # 保存详细数据
    result_data = {
        "image_path": image_path,
        "image_size": [w, h],
        "total_regions": len(layout_results),
        "category_statistics": category_stats,
        "detailed_results": layout_results,
        "full_text": full_text,
        "text_length": len(full_text)
    }
    
    with open("layout_analysis_data.json", "w", encoding="utf-8") as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print("✓ 详细数据已保存到: layout_analysis_data.json")
    
    print(f"\n🎉 版面分析效果图生成完成!")
    print(f"📊 总计检测到 {len(layout_results)} 个版面区域")
    print(f"📈 涵盖 {len(category_stats)} 种版面类别")
    print(f"📝 提取文本长度: {len(full_text)} 字符")


def extract_page_content_by_layout(layout_results, ocr_results, original_image_shape):
    """
    按照版面布局的阅读顺序提取页面内容

    Args:
        layout_results: 版面分析结果
        ocr_results: OCR识别结果
        original_image_shape: 原图尺寸

    Returns:
        dict: 包含pageContent, sealContent, handWriteContent, dateContent的结果
    """
    original_height, original_width = original_image_shape[:2]

    # 初始化时间查找器
    time_finder = timefinder.TimeFinder()

    # 预处理OCR结果，转换为统一格式
    processed_ocr_results = []
    if ocr_results and len(ocr_results) > 0:
        for idx, ocr_result in enumerate(ocr_results):
            if len(ocr_result) >= 3:
                ocr_coordinate = ocr_result[0]
                text = ocr_result[1]
                confidence = ocr_result[2]

                if confidence > 0.3 and text.strip():
                    # OCR坐标转换为[x1, y1, x2, y2]格式
                    if len(ocr_coordinate) == 4 and len(ocr_coordinate[0]) == 2:
                        ocr_orig_x1 = min(point[0] for point in ocr_coordinate)
                        ocr_orig_y1 = min(point[1] for point in ocr_coordinate)
                        ocr_orig_x2 = max(point[0] for point in ocr_coordinate)
                        ocr_orig_y2 = max(point[1] for point in ocr_coordinate)

                        processed_ocr_results.append({
                            "idx": idx,
                            "text": text,
                            "confidence": confidence,
                            "coordinate": [ocr_orig_x1, ocr_orig_y1, ocr_orig_x2, ocr_orig_y2],
                            "assigned_layout": -1
                        })

    # 为每个OCR文本框找到IoU最大的版面区域
    print("\n🔍 OCR文本框匹配分析:")
    for ocr_idx, ocr_data in enumerate(processed_ocr_results):
        ocr_box = ocr_data["coordinate"]
        max_iou = 0
        best_layout_idx = -1

        for layout_idx, layout_result in enumerate(layout_results):
            layout_box = layout_result['coordinate']
            iou = calculate_iou(ocr_box, layout_box)

            if iou > max_iou:
                max_iou = iou
                best_layout_idx = layout_idx

        # 每个OCR文本框都必须分配给IoU最大的版面区域，无论IoU值多小
        if best_layout_idx >= 0:
            processed_ocr_results[ocr_idx]["assigned_layout"] = best_layout_idx
            print(f"OCR文本框{ocr_idx+1} '{ocr_data['text'][:20]}...' -> 版面区域{best_layout_idx+1} (IoU:{max_iou:.3f})")
        else:
            # 这种情况理论上不应该发生，因为总会有一个IoU最大的版面区域
            print(f"OCR文本框{ocr_idx+1} '{ocr_data['text'][:20]}...' -> 错误：无法找到匹配的版面区域")

    # 按版面区域组织OCR文本
    layout_ocr_map = {}
    for ocr_data in processed_ocr_results:
        layout_idx = ocr_data["assigned_layout"]
        if layout_idx >= 0:
            if layout_idx not in layout_ocr_map:
                layout_ocr_map[layout_idx] = []
            layout_ocr_map[layout_idx].append({
                "text": ocr_data["text"],
                "coordinate": ocr_data["coordinate"]
            })

    # 按版面区域的位置排序（从上到下，从左到右）
    layout_with_positions = []
    for i, layout_result in enumerate(layout_results):
        x1, y1, x2, y2 = layout_result['coordinate']
        center_y = (y1 + y2) / 2
        center_x = (x1 + x2) / 2

        layout_with_positions.append({
            "index": i,
            "label": layout_result['label'],
            "coordinate": [x1, y1, x2, y2],
            "center_x": center_x,
            "center_y": center_y,
            "ocr_texts": layout_ocr_map.get(i, [])
        })

    # 按阅读顺序排序：先按Y坐标（从上到下），再按X坐标（从左到右）
    layout_with_positions.sort(key=lambda x: (x["center_y"], x["center_x"]))

    # 提取页面内容
    page_content_lines = []

    for layout_info in layout_with_positions:
        if not layout_info["ocr_texts"]:
            continue

        # 在当前版面区域内，按OCR文本框的位置排序
        ocr_texts = layout_info["ocr_texts"]

        # 按行组织OCR文本
        lines = []
        current_line = []
        current_y = None
        line_height_threshold = 20  # 行高阈值

        # 先按Y坐标排序
        ocr_texts.sort(key=lambda x: x["coordinate"][1])

        for ocr_text in ocr_texts:
            x1, y1, x2, y2 = ocr_text["coordinate"]
            center_y = (y1 + y2) / 2

            if current_y is None or abs(center_y - current_y) <= line_height_threshold:
                # 同一行
                current_line.append({
                    "text": ocr_text["text"],
                    "x": x1
                })
                if current_y is None:
                    current_y = center_y
            else:
                # 新的一行
                if current_line:
                    # 按X坐标排序当前行的文本
                    current_line.sort(key=lambda x: x["x"])
                    line_text = " ".join([item["text"] for item in current_line])
                    lines.append(line_text)

                current_line = [{
                    "text": ocr_text["text"],
                    "x": x1
                }]
                current_y = center_y

        # 处理最后一行
        if current_line:
            current_line.sort(key=lambda x: x["x"])
            line_text = " ".join([item["text"] for item in current_line])
            lines.append(line_text)

        # 将当前版面区域的所有行添加到页面内容中
        if lines:
            page_content_lines.extend(lines)

    # 组合最终的页面内容
    page_content = "\n".join(page_content_lines)

    # 检测印章和手写内容
    seal_content = []
    handwrite_content = []

    for layout_result in layout_results:
        if layout_result['label'] == 'seal':
            seal_content = ["has_seal"]
        elif layout_result['label'] == 'handwriting':
            handwrite_content = ["has_handwritten"]

    # 提取日期内容
    date_content = time_finder.find_time(page_content)

    # 构建结果
    result = {
        "pageContent": page_content,
        "sealContent": seal_content,
        "handWriteContent": handwrite_content,
        "dateContent": date_content
    }

    return result


def test_page_content_extraction():
    """测试页面内容提取功能"""
    print("🧪 测试页面内容提取功能...")

    # 加载测试图像
    image_path = "/workspace/hngpt/seal-hand.png"
    image = cv2.imread(image_path)
    if image is None:
        print(f"✗ 无法加载图像: {image_path}")
        return

    print(f"📷 图像尺寸: {image.shape[1]} x {image.shape[0]}")

    # 转换为RGB格式用于模型推理
    img_array = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    try:
        # 步骤1: 版面分析
        print("📋 执行版面分析...")
        layout_results = rec.rapid_layout_with_params(img_array, gpu_id=0)
        print(f"✓ 检测到 {len(layout_results)} 个版面区域")

        # 步骤2: 整页OCR识别
        print("📝 执行整页OCR识别...")
        ocr_results = rec.rapid_ocr_with_params(img_array, gpu_id=0, version='v5')
        print(f"✓ 识别到 {len(ocr_results) if ocr_results else 0} 个文本区域")

        # 步骤3: 提取页面内容
        print("📄 提取页面内容...")

        # 添加详细的调试信息
        print("\n🔍 详细匹配分析:")
        print(f"版面区域数量: {len(layout_results)}")
        print(f"OCR文本框数量: {len(ocr_results) if ocr_results else 0}")

        # 显示前几个OCR文本框的信息
        if ocr_results:
            for i, ocr_result in enumerate(ocr_results[:10]):  # 只显示前10个
                if len(ocr_result) >= 3:
                    ocr_coordinate = ocr_result[0]
                    text = ocr_result[1]
                    confidence = ocr_result[2]

                    if len(ocr_coordinate) == 4 and len(ocr_coordinate[0]) == 2:
                        ocr_x1 = min(point[0] for point in ocr_coordinate)
                        ocr_y1 = min(point[1] for point in ocr_coordinate)
                        ocr_x2 = max(point[0] for point in ocr_coordinate)
                        ocr_y2 = max(point[1] for point in ocr_coordinate)
                        print(f"OCR文本框{i+1}: ({ocr_x1:.0f},{ocr_y1:.0f},{ocr_x2:.0f},{ocr_y2:.0f}) 置信度:{confidence:.3f} 文本:'{text[:20]}...'")

        result = extract_page_content_by_layout(layout_results, ocr_results, image.shape)

        # 输出结果
        print("\n🎯 提取结果:")
        print(f"📝 页面内容长度: {len(result['pageContent'])} 字符")
        print(f"🔖 印章内容: {result['sealContent']}")
        print(f"✍️  手写内容: {result['handWriteContent']}")
        print(f"📅 日期内容: {result['dateContent']}")

        print("\n📄 页面内容预览:")
        print("=" * 50)
        print(result['pageContent'][:500] + "..." if len(result['pageContent']) > 500 else result['pageContent'])
        print("=" * 50)

        # 保存结果到JSON文件
        with open("page_content_result.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("✓ 结果已保存到: page_content_result.json")

    except Exception as e:
        print(f"✗ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始生成版面分析效果图...")
    generate_layout_effect()
    print("✅ 脚本执行完成")
    test_page_content_extraction()
