#!/usr/bin/env python3
"""
测试Embedding接口
"""

import asyncio
import aiohttp
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_embedding():
    """测试Embedding接口"""
    headers = {
        "Authorization": "Bearer startfrom2023",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "hngpt-embedding",
        "input": "Test embedding text"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:8888/v1/embeddings", 
                                   json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logging.info("✅ Embedding接口测试成功")
                    return True
                else:
                    text = await response.text()
                    logging.error(f"❌ Embedding接口失败: {response.status} - {text}")
                    return False
    except Exception as e:
        logging.error(f"❌ Embedding接口异常: {e}")
        return False

async def get_gpu_status():
    """获取GPU状态"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/api/gpu/status") as response:
                if response.status == 200:
                    result = await response.json()
                    data = result.get("data", {})
                    logging.info("📊 GPU状态:")
                    logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
                    logging.info(f"  当前负载: {data.get('current_loads', {})}")
                    return data
                else:
                    logging.error(f"❌ 获取GPU状态失败: {response.status}")
                    return None
    except Exception as e:
        logging.error(f"❌ 获取GPU状态异常: {e}")
        return None

async def main():
    """主函数"""
    logging.info("🚀 开始Embedding接口测试")
    
    # 1. 获取初始状态
    logging.info("\n📊 初始GPU状态:")
    await get_gpu_status()
    
    # 2. 测试Embedding接口
    logging.info("\n🔍 测试Embedding接口...")
    success = await test_embedding()
    
    # 3. 获取最终状态
    logging.info("\n📊 最终GPU状态:")
    await get_gpu_status()
    
    if success:
        logging.info("🎉 Embedding接口测试成功！")
    else:
        logging.warning("⚠️ Embedding接口测试失败")

if __name__ == "__main__":
    asyncio.run(main())
