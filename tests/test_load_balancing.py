#!/usr/bin/env python3
"""
负载均衡测试脚本
验证4卡推理的动态分配算法
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LoadBalancingTester:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
    async def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/monitor/api/status") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logging.error(f"获取系统状态失败: {response.status}")
                        return {}
        except Exception as e:
            logging.error(f"获取系统状态异常: {e}")
            return {}

    async def send_chat_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """发送Chat请求"""
        payload = {
            "model": "hngpt-mini:latest",
            "messages": [{"role": "user", "content": f"Hello, this is test request {request_id}"}],
            "stream": False
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/chat/completions",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                return {
                    "request_id": request_id,
                    "success": response.status == 200,
                    "response_time": response_time,
                    "status_code": response.status
                }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "request_id": request_id,
                "success": False,
                "response_time": response_time,
                "error": str(e),
                "status_code": 0
            }

    async def send_embedding_request(self, session: aiohttp.ClientSession, request_id: int) -> Dict:
        """发送Embedding请求"""
        payload = {
            "model": "hngpt-embedding",
            "input": f"Test embedding text for request {request_id}"
        }
        
        start_time = time.time()
        try:
            async with session.post(f"{self.base_url}/v1/embeddings",
                                   json=payload, headers=self.headers, timeout=30) as response:
                response_time = time.time() - start_time
                return {
                    "request_id": request_id,
                    "success": response.status == 200,
                    "response_time": response_time,
                    "status_code": response.status
                }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "request_id": request_id,
                "success": False,
                "response_time": response_time,
                "error": str(e),
                "status_code": 0
            }

    async def test_load_balancing(self, num_requests: int = 20) -> Dict:
        """测试负载均衡效果"""
        logging.info(f"🧪 开始负载均衡测试，发送 {num_requests} 个请求")
        
        # 获取初始状态
        initial_status = await self.get_system_status()
        logging.info(f"📊 初始系统状态: {initial_status.get('status', 'unknown')}")
        
        # 创建混合请求
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            # 创建Chat和Embedding请求的混合
            for i in range(num_requests):
                if i % 2 == 0:
                    # Chat请求
                    task = self.send_chat_request(session, i)
                    tasks.append(("chat", i, task))
                else:
                    # Embedding请求
                    task = self.send_embedding_request(session, i)
                    tasks.append(("embedding", i, task))
            
            # 执行所有请求
            start_time = time.time()
            results = []
            
            for request_type, request_id, task in tasks:
                try:
                    result = await task
                    result["request_type"] = request_type
                    results.append(result)
                    
                    # 每5个请求显示一次进度
                    if len(results) % 5 == 0:
                        logging.info(f"📈 已完成 {len(results)}/{num_requests} 个请求")
                        
                except Exception as e:
                    logging.error(f"❌ 请求失败 [{request_type}#{request_id}]: {e}")
                    results.append({
                        "request_id": request_id,
                        "request_type": request_type,
                        "success": False,
                        "error": str(e),
                        "response_time": 0,
                        "status_code": 0
                    })
            
            total_time = time.time() - start_time
        
        # 获取最终状态
        final_status = await self.get_system_status()
        
        # 分析结果
        analysis = self.analyze_results(results, total_time, initial_status, final_status)
        
        return {
            "test_config": {
                "num_requests": num_requests,
                "total_time": total_time
            },
            "initial_status": initial_status,
            "final_status": final_status,
            "results": results,
            "analysis": analysis
        }

    def analyze_results(self, results: List[Dict], total_time: float, 
                       initial_status: Dict, final_status: Dict) -> Dict:
        """分析测试结果"""
        
        # 基本统计
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # 按类型统计
        chat_results = [r for r in results if r.get("request_type") == "chat"]
        embedding_results = [r for r in results if r.get("request_type") == "embedding"]
        
        chat_success = sum(1 for r in chat_results if r.get("success", False))
        embedding_success = sum(1 for r in embedding_results if r.get("success", False))
        
        # 响应时间统计
        successful_response_times = [r["response_time"] for r in results if r.get("success", False)]
        avg_response_time = sum(successful_response_times) / len(successful_response_times) if successful_response_times else 0
        
        # GPU状态变化
        gpu_status_change = {}
        if initial_status.get("gpu_info") and final_status.get("gpu_info"):
            for i, (initial_gpu, final_gpu) in enumerate(zip(initial_status["gpu_info"], final_status["gpu_info"])):
                gpu_status_change[f"gpu_{i}"] = {
                    "memory_change": final_gpu.get("memory_used_mb", 0) - initial_gpu.get("memory_used_mb", 0),
                    "utilization_change": final_gpu.get("utilization_percent", 0) - initial_gpu.get("utilization_percent", 0),
                    "task_change": {
                        "chat": final_gpu.get("llm_chat_tasks", 0) - initial_gpu.get("llm_chat_tasks", 0),
                        "embedding": final_gpu.get("llm_embedding_tasks", 0) - initial_gpu.get("llm_embedding_tasks", 0)
                    }
                }
        
        analysis = {
            "performance": {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate": success_rate,
                "total_time": total_time,
                "avg_response_time": avg_response_time,
                "throughput": successful_requests / total_time if total_time > 0 else 0
            },
            "by_type": {
                "chat": {
                    "total": len(chat_results),
                    "successful": chat_success,
                    "success_rate": (chat_success / len(chat_results)) * 100 if chat_results else 0
                },
                "embedding": {
                    "total": len(embedding_results),
                    "successful": embedding_success,
                    "success_rate": (embedding_success / len(embedding_results)) * 100 if embedding_results else 0
                }
            },
            "gpu_status_change": gpu_status_change
        }
        
        return analysis

    def print_analysis(self, test_result: Dict):
        """打印分析结果"""
        analysis = test_result["analysis"]
        
        logging.info(f"\n{'='*60}")
        logging.info("📊 负载均衡测试结果分析")
        logging.info(f"{'='*60}")
        
        # 性能统计
        perf = analysis["performance"]
        logging.info(f"🚀 总体性能:")
        logging.info(f"  总请求数: {perf['total_requests']}")
        logging.info(f"  成功请求: {perf['successful_requests']}")
        logging.info(f"  失败请求: {perf['failed_requests']}")
        logging.info(f"  成功率: {perf['success_rate']:.1f}%")
        logging.info(f"  总耗时: {perf['total_time']:.2f}s")
        logging.info(f"  平均响应时间: {perf['avg_response_time']:.3f}s")
        logging.info(f"  吞吐量: {perf['throughput']:.2f} 请求/秒")
        
        # 按类型统计
        logging.info(f"\n📈 按请求类型统计:")
        for req_type, stats in analysis["by_type"].items():
            logging.info(f"  {req_type.upper()}:")
            logging.info(f"    总数: {stats['total']}")
            logging.info(f"    成功: {stats['successful']}")
            logging.info(f"    成功率: {stats['success_rate']:.1f}%")
        
        # GPU状态变化
        if analysis["gpu_status_change"]:
            logging.info(f"\n🔧 GPU状态变化:")
            for gpu_id, changes in analysis["gpu_status_change"].items():
                logging.info(f"  {gpu_id.upper()}:")
                logging.info(f"    内存变化: {changes['memory_change']:.1f}MB")
                logging.info(f"    使用率变化: {changes['utilization_change']:.1f}%")
                logging.info(f"    任务变化: Chat={changes['task_change']['chat']}, Embedding={changes['task_change']['embedding']}")

async def main():
    """主函数"""
    tester = LoadBalancingTester()
    
    # 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{tester.base_url}/") as response:
                if response.status != 200:
                    logging.error("服务器连接失败，请确保服务器正在运行")
                    return
    except Exception as e:
        logging.error(f"无法连接到服务器: {e}")
        return
    
    # 执行负载均衡测试
    logging.info("🚀 开始负载均衡测试")
    test_result = await tester.test_load_balancing(num_requests=20)
    
    # 打印分析结果
    tester.print_analysis(test_result)
    
    # 保存结果
    with open("load_balancing_test_results.json", "w", encoding="utf-8") as f:
        json.dump(test_result, f, indent=2, ensure_ascii=False)
    
    logging.info(f"\n✅ 测试完成，结果已保存到 load_balancing_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
