# PP-DocLayout-L TensorRT 完整推理流程

## 🎯 **项目概述**

本项目实现了 PP-DocLayout-L 文档版面分析模型的完整 TensorRT 推理流程，从 ONNX 优化到高性能推理的一站式解决方案。

## 📁 **项目结构**

```
├── optimize_onnx_simple.py      # ONNX 模型优化脚本
├── doc_layout_infer_trt.py      # TensorRT 推理核心类
├── test_doc_layout_complete.py  # 完整测试脚本
├── complete_pipeline.py         # 一键完整流程
├── final_demo.py                # 最终演示脚本
├── debug_output.py              # 调试工具
├── models/
│   └── doc_layout.trt           # TensorRT 引擎文件
├── output/
│   ├── results.json             # 推理结果
│   └── result_annotated.jpg     # 可视化结果
└── README_Complete_Pipeline.md  # 本文档
```

## 🚀 **快速开始**

### 1. 环境要求

```bash
# Python 依赖
pip install opencv-python numpy tensorrt pycuda

# 系统要求
- CUDA >= 11.0
- TensorRT >= 8.0
- Python >= 3.8
```

### 2. 一键运行

```bash
# 最简单的使用方式
python final_demo.py
```

### 3. 完整流程

```bash
# 从 ONNX 到推理的完整流程
python complete_pipeline.py --onnx PP-DocLayout-L/inference.onnx --output-dir ./output
```

## 🔧 **详细使用方法**

### **步骤1: ONNX 优化 (可选)**

```bash
python optimize_onnx_simple.py \
    --input PP-DocLayout-L/inference.onnx \
    --output models/doc_layout.optimized.onnx
```

### **步骤2: 构建 TensorRT 引擎**

```bash
trtexec \
    --onnx=PP-DocLayout-L/inference.onnx \
    --saveEngine=models/doc_layout.trt \
    --explicitBatch \
    --fp16 \
    --minShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --optShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 \
    --maxShapes=image:1x3x640x640,im_shape:1x2,scale_factor:1x2 
```

### **步骤3: 推理测试**

```bash
python test_doc_layout_complete.py \
    --engine models/doc_layout.trt \
    --output-dir output \
    --benchmark
```

## 📊 **模型信息**

### **支持的类别 (23个)**

| ID | 类别名称 | 中文描述 | ID | 类别名称 | 中文描述 |
|----|----------|----------|----|-----------|---------| 
| 0 | paragraph_title | 段落标题 | 12 | footnote | 脚注 |
| 1 | image | 图像 | 13 | header | 页眉 |
| 2 | text | 文本 | 14 | algorithm | 算法 |
| 3 | number | 页码 | 15 | footer | 页脚 |
| 4 | abstract | 摘要 | 16 | seal | 印章 |
| 5 | content | 目录 | 17 | chart_title | 图表标题 |
| 6 | figure_title | 图片标题 | 18 | chart | 图表 |
| 7 | formula | 公式 | 19 | formula_number | 公式编号 |
| 8 | table | 表格 | 20 | header_image | 页眉图像 |
| 9 | table_title | 表格标题 | 21 | footer_image | 页脚图像 |
| 10 | reference | 参考文献 | 22 | aside_text | 侧边文本 |
| 11 | doc_title | 文档标题 | | | |

### **输入输出规格**

**输入 (3个)**:
- `image`: `float32[1, 3, 640, 640]` - 预处理后的图像
- `im_shape`: `float32[1, 2]` - 原始图像尺寸 `[height, width]`
- `scale_factor`: `float32[1, 2]` - 缩放因子 `[scale_y, scale_x]`

**输出 (2个)**:
- `outputs[0]`: `int32[1]` - 检测数量 (固定为300)
- `outputs[1]`: `float32[300, 6]` - 检测结果 `[class_id, confidence, x1, y1, x2, y2]`

## 📈 **性能指标**

### **测试环境**
- GPU: NVIDIA GeForce RTX 2080 Ti
- CUDA: 11.8
- TensorRT: 8.6.1
- 精度: FP32

### **性能结果**
- **推理时间**: ~55ms
- **FPS**: ~18
- **GPU内存**: ~236MB
- **引擎大小**: 138MB

## 🎨 **输出格式**

### **JSON 结果格式**

```json
{
  "image_path": "test_document.jpg",
  "inference_time_ms": 54.69,
  "num_detections": 24,
  "results": [
    {
      "cls_id": 8,
      "label": "table",
      "score": 0.810,
      "coordinate": [42, 387, 484, 625],
      "bbox": [42, 387, 484, 625],
      "area": 105196
    }
  ]
}
```

### **可视化结果**

生成带有边界框和标签的可视化图像，不同类别使用不同颜色标识。

## 🛠 **API 使用**

### **基础使用**

```python
from doc_layout_infer_trt import DocLayoutDetectorTRT
import cv2

# 初始化检测器
detector = DocLayoutDetectorTRT("models/doc_layout.trt", conf_threshold=0.5)

# 加载图像
image = cv2.imread("document.jpg")

# 执行推理
results = detector.predict(image)

# 处理结果
for result in results:
    print(f"{result['label']}: {result['score']:.3f}")
```

### **高级使用**

```python
# 自定义参数
detector = DocLayoutDetectorTRT(
    engine_path="models/doc_layout.trt",
    conf_threshold=0.3,
    input_size=(640, 640)
)

# 分步处理
inputs, scale_x, scale_y = detector.preprocess(image)
outputs = detector.forward(inputs)
results = detector.postprocess(outputs, scale_x, scale_y)
```

## 🔍 **调试工具**

### **输出格式调试**

```bash
python debug_output.py
```

### **性能测试**

```bash
python test_doc_layout_complete.py \
    --engine models/doc_layout.trt \
    --benchmark \
    --num-runs 100
```

## ⚠️ **注意事项**

1. **输出顺序**: 模型输出顺序为 `[检测数量, 检测框]`，与常见格式相反
2. **坐标系统**: 输出坐标相对于640×640输入，需要转换回原始图像坐标
3. **置信度过滤**: 建议使用0.3-0.5的置信度阈值
4. **内存管理**: 使用完毕后会自动释放GPU内存

## 🚀 **性能优化建议**

1. **使用FP16**: 可以显著提升推理速度
2. **批处理**: 支持批量处理多张图像
3. **内存预分配**: 避免动态内存分配
4. **异步处理**: 可以与图像预处理并行

## 📝 **更新日志**

- **v1.0**: 完成基础TensorRT推理实现
- **v1.1**: 修复输出格式解析问题
- **v1.2**: 添加完整的测试和演示脚本
- **v1.3**: 优化性能和内存使用

## 🤝 **贡献**

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 **许可证**

本项目采用MIT许可证。
