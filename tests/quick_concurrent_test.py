#!/usr/bin/env python3
"""
快速并发测试脚本
快速测试每种请求类型的并发能力
"""

import asyncio
import aiohttp
import time
import json
import base64
import cv2
import numpy as np

async def test_request_type(request_type: str, concurrent_levels: list):
    """测试指定请求类型在不同并发级别下的表现"""
    base_url = "http://localhost:8888"
    headers = {"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"}
    
    print(f"\n🧪 测试 {request_type.upper()} 请求")
    print("=" * 50)
    
    for concurrent in concurrent_levels:
        print(f"并发数 {concurrent}: ", end="", flush=True)
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            # 创建请求任务
            for i in range(concurrent):
                if request_type == "chat":
                    payload = {
                        "model": "hngpt-mini:latest",
                        "messages": [{"role": "user", "content": f"Test {i}"}],
                        "stream": False
                    }
                    task = session.post(f"{base_url}/v1/chat/completions", 
                                      json=payload, headers=headers, timeout=20)
                
                elif request_type == "embedding":
                    payload = {
                        "model": "hngpt-embedding",
                        "input": f"Test embedding {i}"
                    }
                    task = session.post(f"{base_url}/v1/embeddings", 
                                      json=payload, headers=headers, timeout=20)
                
                elif request_type == "ocr":
                    # 创建简单测试图片
                    img = np.ones((100, 200, 3), dtype=np.uint8) * 255
                    cv2.putText(img, f"Test {i}", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
                    _, buffer = cv2.imencode('.jpg', img)
                    img_base64 = base64.b64encode(buffer).decode('utf-8')

                    payload = {"image": img_base64, "enable_seal_hw": False}
                    task = session.post(f"{base_url}/ocr",
                                      json=payload, headers=headers, timeout=20)
                
                tasks.append(task)
            
            # 执行并发请求
            start_time = time.time()
            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time
                
                # 统计结果
                success = 0
                errors = 0
                status_codes = {}
                
                for response in responses:
                    if isinstance(response, Exception):
                        errors += 1
                    else:
                        if response.status == 200:
                            success += 1
                        else:
                            errors += 1
                            status_codes[response.status] = status_codes.get(response.status, 0) + 1
                        response.close()
                
                success_rate = success / concurrent * 100
                throughput = success / total_time
                
                # 输出结果
                if success_rate >= 90:
                    status = "✅"
                elif success_rate >= 70:
                    status = "⚠️"
                else:
                    status = "❌"
                
                print(f"{status} 成功率 {success_rate:.0f}% ({success}/{concurrent}), "
                      f"吞吐量 {throughput:.1f}req/s, 耗时 {total_time:.1f}s")
                
                if errors > 0 and status_codes:
                    print(f"     错误状态码: {status_codes}")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        # 短暂等待
        await asyncio.sleep(1)

async def main():
    """主函数"""
    print("🚀 快速并发能力测试")
    print("测试单GPU环境下三种请求类型的并发能力")
    
    # 定义测试的并发级别
    test_levels = {
        "embedding": [1, 2, 4, 6, 8, 10, 12],  # Embedding通常最轻量
        "ocr": [1, 2, 3, 4, 5, 6, 8],          # OCR中等资源消耗
        "chat": [1, 2, 3, 4, 5, 6]             # Chat最消耗资源
    }
    
    # 测试每种请求类型
    for request_type, levels in test_levels.items():
        await test_request_type(request_type, levels)
        await asyncio.sleep(2)  # 类型间等待
    
    print(f"\n📊 测试建议:")
    print("✅ = 推荐的并发数 (成功率≥90%)")
    print("⚠️ = 可接受的并发数 (成功率≥70%)")  
    print("❌ = 不推荐的并发数 (成功率<70%)")
    
    print(f"\n💡 根据测试结果，建议在代码中设置:")
    print("- max_concurrent_chat: 找到最后一个✅的并发数")
    print("- max_concurrent_embeddings: 找到最后一个✅的并发数") 
    print("- max_concurrent_ocr: 找到最后一个✅的并发数")

if __name__ == "__main__":
    asyncio.run(main())
