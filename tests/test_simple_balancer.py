#!/usr/bin/env python3
"""
简化的统一GPU负载均衡器测试脚本
专门测试新的UnifiedGPULoadBalancer类
"""

import asyncio
import aiohttp
import time
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SimpleBalancerTester:
    def __init__(self, base_url="http://localhost:8888", auth_token="startfrom2023"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
    async def get_gpu_status(self):
        """获取GPU状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/gpu/status") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logging.error(f"获取GPU状态失败: {response.status}")
                        return None
        except Exception as e:
            logging.error(f"获取GPU状态异常: {e}")
            return None

    async def test_request_type(self, request_type: str):
        """测试特定类型的请求"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/api/test/unified-balancer"
                params = {"request_type": request_type}
                
                # 准备测试数据
                if request_type == "chat":
                    test_data = {
                        "messages": [{"role": "user", "content": f"Test {request_type} request"}],
                        "model": "hngpt-mini:latest"
                    }
                elif request_type == "embedding":
                    test_data = {
                        "input": f"Test {request_type} text",
                        "model": "hngpt-embedding"
                    }
                elif request_type == "ocr":
                    # 简化的OCR测试数据，不包含图像
                    test_data = {
                        "timeout": 30.0,
                        "enable_seal_hw": False
                    }
                else:
                    test_data = {}
                
                start_time = time.time()
                async with session.post(url, params=params, json=test_data, headers=self.headers) as response:
                    response_time = time.time() - start_time
                    result = await response.json()
                    result["response_time"] = response_time
                    result["status_code"] = response.status
                    return result
                    
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "request_type": request_type
            }

    async def test_concurrent_requests(self, request_type: str, count: int):
        """测试并发请求"""
        logging.info(f"🧪 测试{request_type}并发请求，数量: {count}")
        
        tasks = []
        for i in range(count):
            task = self.test_request_type(request_type)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # 处理结果
        success_count = 0
        error_count = 0
        gpu_assignments = {}
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_count += 1
                logging.error(f"请求 {i} 异常: {result}")
            elif result.get("status") == "success":
                success_count += 1
                # 统计GPU分配
                gpu_id = result.get("result", {}).get("gpu_id")
                if gpu_id is not None:
                    gpu_assignments[gpu_id] = gpu_assignments.get(gpu_id, 0) + 1
            else:
                error_count += 1
                logging.error(f"请求 {i} 失败: {result.get('error', 'Unknown error')}")
        
        logging.info(f"✅ {request_type}并发测试完成:")
        logging.info(f"  总耗时: {total_time:.2f}s")
        logging.info(f"  成功: {success_count}/{count}")
        logging.info(f"  失败: {error_count}/{count}")
        logging.info(f"  成功率: {(success_count/count)*100:.1f}%")
        
        if gpu_assignments:
            logging.info(f"  GPU分配: {gpu_assignments}")
        
        return {
            "request_type": request_type,
            "total_requests": count,
            "success_count": success_count,
            "error_count": error_count,
            "total_time": total_time,
            "gpu_assignments": gpu_assignments
        }

    async def run_basic_test(self):
        """运行基础测试"""
        logging.info("🚀 开始统一GPU负载均衡器基础测试")
        
        # 1. 获取初始状态
        logging.info("\n📊 获取初始GPU状态...")
        initial_status = await self.get_gpu_status()
        if initial_status and initial_status.get("status") == "success":
            data = initial_status.get("data", {})
            logging.info(f"  GPU数量: {data.get('gpu_count', 0)}")
            logging.info(f"  并发限制: {data.get('concurrent_limits', {})}")
            
            current_loads = data.get("current_loads", {})
            for gpu_id, loads in current_loads.items():
                logging.info(f"  GPU {gpu_id}: {loads}")
        else:
            logging.error("❌ 无法获取GPU状态，测试终止")
            return
        
        # 2. 测试单个请求
        logging.info("\n🔍 测试单个请求...")
        for request_type in ["chat", "embedding", "ocr"]:
            logging.info(f"\n测试 {request_type.upper()} 请求:")
            result = await self.test_request_type(request_type)
            
            if result.get("status") == "success":
                gpu_id = result.get("result", {}).get("gpu_id")
                response_time = result.get("response_time", 0)
                logging.info(f"  ✅ 成功 - GPU {gpu_id}, 耗时: {response_time:.3f}s")
            else:
                error = result.get("error", "Unknown error")
                logging.error(f"  ❌ 失败 - {error}")
            
            await asyncio.sleep(0.5)  # 短暂等待
        
        # 3. 测试并发请求
        logging.info("\n🚀 测试并发请求...")
        concurrent_results = []
        
        for request_type in ["chat", "embedding", "ocr"]:
            # 根据并发限制调整测试数量
            if request_type == "chat":
                test_count = 6  # 测试超过4卡总并发限制
            elif request_type == "embedding":
                test_count = 15  # 测试接近4卡总并发限制
            else:  # ocr
                test_count = 6  # 测试超过4卡总并发限制
            
            result = await self.test_concurrent_requests(request_type, test_count)
            concurrent_results.append(result)
            
            await asyncio.sleep(1)  # 等待GPU释放
        
        # 4. 获取最终状态
        logging.info("\n📊 获取最终GPU状态...")
        final_status = await self.get_gpu_status()
        if final_status and final_status.get("status") == "success":
            data = final_status.get("data", {})
            current_loads = data.get("current_loads", {})
            for gpu_id, loads in current_loads.items():
                logging.info(f"  GPU {gpu_id}: {loads}")
        
        # 5. 总结
        logging.info(f"\n{'='*60}")
        logging.info("📊 测试总结")
        logging.info(f"{'='*60}")
        
        total_requests = sum(r["total_requests"] for r in concurrent_results)
        total_success = sum(r["success_count"] for r in concurrent_results)
        total_errors = sum(r["error_count"] for r in concurrent_results)
        overall_success_rate = (total_success / total_requests) * 100 if total_requests > 0 else 0
        
        logging.info(f"总请求数: {total_requests}")
        logging.info(f"成功请求: {total_success}")
        logging.info(f"失败请求: {total_errors}")
        logging.info(f"总体成功率: {overall_success_rate:.1f}%")
        
        # GPU分配统计
        all_gpu_assignments = {}
        for result in concurrent_results:
            for gpu_id, count in result["gpu_assignments"].items():
                all_gpu_assignments[gpu_id] = all_gpu_assignments.get(gpu_id, 0) + count
        
        if all_gpu_assignments:
            logging.info(f"\nGPU分配统计:")
            for gpu_id, count in sorted(all_gpu_assignments.items()):
                percentage = (count / total_success) * 100 if total_success > 0 else 0
                logging.info(f"  GPU {gpu_id}: {count} 次 ({percentage:.1f}%)")
        
        logging.info(f"\n✅ 统一GPU负载均衡器测试完成")

async def main():
    """主函数"""
    tester = SimpleBalancerTester()
    
    # 测试服务器连接
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{tester.base_url}/") as response:
                if response.status != 200:
                    logging.error("服务器连接失败，请确保服务器正在运行")
                    return
                logging.info("✅ 服务器连接成功")
    except Exception as e:
        logging.error(f"无法连接到服务器: {e}")
        return
    
    # 运行测试
    await tester.run_basic_test()

if __name__ == "__main__":
    asyncio.run(main())
