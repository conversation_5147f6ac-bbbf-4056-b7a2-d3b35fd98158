#!/usr/bin/env python3
"""
检查ONNX模型的输入输出形状
"""

import onnx
import sys
import os

def check_model_shapes(model_path):
    """检查模型的输入输出形状"""
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"📊 检查模型: {model_path}")
    print("=" * 50)
    
    try:
        model = onnx.load(model_path)
        
        print("📥 输入形状:")
        for inp in model.graph.input:
            shape = []
            for d in inp.type.tensor_type.shape.dim:
                if d.dim_value > 0:
                    shape.append(str(d.dim_value))
                else:
                    shape.append('dynamic')
            print(f"  {inp.name}: [{', '.join(shape)}]")
        
        print("\n📤 输出形状:")
        for out in model.graph.output:
            shape = []
            for d in out.type.tensor_type.shape.dim:
                if d.dim_value > 0:
                    shape.append(str(d.dim_value))
                else:
                    shape.append('dynamic')
            print(f"  {out.name}: [{', '.join(shape)}]")
        
        print(f"\n✅ 模型检查完成")
        
    except Exception as e:
        print(f"❌ 检查模型失败: {e}")

if __name__ == "__main__":
    # 检查原始模型
    print("🔍 检查原始模型:")
    check_model_shapes("models/doc_layout.onnx")
    
    print("\n" + "="*60 + "\n")
    
    # 检查固定batch=1的模型
    print("🔍 检查固定batch=1的模型:")
    check_model_shapes("models/doc_layout_batch1.onnx")
