#!/usr/bin/env python3
"""
简化的推理脚本，使用默认参数
"""
import cv2
import json
import time
import numpy as np
import onnxruntime as ort
import os

# PP-DocLayout-L 的23个类别映射
CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

def main():
    print("🔍 PP-DocLayout-L 文档版面分析")
    print("=" * 50)
    
    # 默认参数
    model_path = "/workspace/hngpt/models/doc_layout.trt"
    image_path = "/workspace/hngpt/document.png"
    output_dir = "output"
    conf_threshold = 0.3
    
    # 检查 TensorRT 模型，如果不存在则使用 ONNX
    if not os.path.exists(model_path):
        print(f"⚠️  TensorRT 模型不存在: {model_path}")
        # 尝试使用 ONNX 模型
        onnx_alternatives = [
            "/workspace/hngpt/models/doc_layout.optimized.onnx",
            "/workspace/hngpt/PP-DocLayout-L/inference.onnx"
        ]
        
        for onnx_path in onnx_alternatives:
            if os.path.exists(onnx_path):
                model_path = onnx_path
                print(f"✓ 使用 ONNX 模型: {model_path}")
                break
        else:
            print("✗ 未找到可用的模型文件")
            return
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"✗ 图像文件不存在: {image_path}")
        return
    
    # 加载图像
    print(f"📷 加载图像: {image_path}")
    image = cv2.imread(image_path)
    if image is None:
        print(f"✗ 无法加载图像: {image_path}")
        return
    
    orig_h, orig_w = image.shape[:2]
    print(f"✓ 图像加载成功，尺寸: {orig_w} x {orig_h}")
    
    # 预处理
    print("🔄 预处理图像...")
    target_size = 640
    scale = min(target_size / orig_w, target_size / orig_h)
    new_w = int(orig_w * scale)
    new_h = int(orig_h * scale)
    
    resized = cv2.resize(image, (new_w, new_h))
    canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
    start_x = (target_size - new_w) // 2
    start_y = (target_size - new_h) // 2
    canvas[start_y:start_y+new_h, start_x:start_x+new_w] = resized
    
    canvas_input = canvas.astype(np.float32) / 255.0
    canvas_input = np.transpose(canvas_input, (2, 0, 1))
    canvas_input = np.expand_dims(canvas_input, axis=0)
    
    inputs = {
        'image': canvas_input,
        'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
        'scale_factor': np.array([[scale, scale]], dtype=np.float32)
    }
    
    print(f"✓ 预处理完成，缩放比例: {scale:.4f}")
    
    # 推理
    print("🔍 执行推理...")
    start_time = time.time()
    
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    outputs = session.run(None, inputs)
    
    inference_time = time.time() - start_time
    print(f"✓ 推理完成，耗时: {inference_time*1000:.2f}ms")
    
    # 后处理
    print("🔄 后处理结果...")
    detections = outputs[0]  # (N, 6)
    results = []
    
    for detection in detections:
        if len(detection) >= 6:
            class_id = int(detection[0])
            confidence = float(detection[1])
            x1, y1, x2, y2 = detection[2:6]
            
            if confidence < conf_threshold:
                continue
            
            # 坐标转换
            orig_x1 = int((x1 - start_x) / scale)
            orig_y1 = int((y1 - start_y) / scale)
            orig_x2 = int((x2 - start_x) / scale)
            orig_y2 = int((y2 - start_y) / scale)
            
            # 限制范围
            orig_x1 = max(0, min(orig_x1, orig_w))
            orig_y1 = max(0, min(orig_y1, orig_h))
            orig_x2 = max(orig_x1, min(orig_x2, orig_w))
            orig_y2 = max(orig_y1, min(orig_y2, orig_h))
            
            if orig_x2 > orig_x1 and orig_y2 > orig_y1:
                class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
                results.append({
                    'cls_id': class_id,
                    'label': class_name,
                    'score': confidence,
                    'coordinate': [orig_x1, orig_y1, orig_x2, orig_y2],
                    'bbox': [orig_x1, orig_y1, orig_x2, orig_y2],
                    'area': (orig_x2 - orig_x1) * (orig_y2 - orig_y1)
                })
    
    results.sort(key=lambda x: x['score'], reverse=True)
    print(f"✓ 检测到 {len(results)} 个版面元素")
    
    # 显示结果
    if results:
        print(f"\n=== 检测结果 ===")
        for i, result in enumerate(results):
            coord = result['coordinate']
            print(f"{i+1:2d}. {result['label']:15s} "
                  f"置信度: {result['score']:.3f} "
                  f"坐标: [{coord[0]}, {coord[1]}, {coord[2]}, {coord[3]}]")
        
        # 统计类别
        print(f"\n📊 类别统计:")
        category_counts = {}
        for result in results:
            label = result['label']
            category_counts[label] = category_counts.get(label, 0) + 1
        
        for label, count in sorted(category_counts.items()):
            print(f"  {label:<15}: {count}")
    else:
        print("未检测到任何版面元素")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存JSON结果
    json_path = os.path.join(output_dir, 'results.json')
    result_data = {
        'image_path': image_path,
        'image_size': [orig_w, orig_h],
        'inference_time_ms': inference_time * 1000,
        'num_detections': len(results),
        'results': results
    }
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2, ensure_ascii=False)
    print(f"✓ 结果已保存到: {json_path}")
    
    # 生成可视化结果
    if results:
        print("🎨 生成可视化结果...")
        annotated = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                  (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
        
        for i, result in enumerate(results):
            x1, y1, x2, y2 = result['coordinate']
            label = result['label']
            score = result['score']
            color = colors[i % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(annotated, (x1, y1), (x2, y2), color, 3)
            
            # 绘制标签
            label_text = f"{label}: {score:.2f}"
            label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
            
            # 标签背景
            cv2.rectangle(annotated, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # 标签文字
            cv2.putText(annotated, label_text, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        output_image_path = os.path.join(output_dir, 'result_annotated.jpg')
        cv2.imwrite(output_image_path, annotated)
        print(f"✓ 可视化结果已保存到: {output_image_path}")
    
    print(f"\n🎉 分析完成!")
    print(f"默认配置:")
    print(f"  - 模型: {model_path}")
    print(f"  - 图像: {image_path}")
    print(f"  - 输出: {output_dir}/")

if __name__ == "__main__":
    main()
