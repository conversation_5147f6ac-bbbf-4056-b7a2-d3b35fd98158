import cv2
import numpy as np
import onnxruntime as ort

CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]

# 预处理
target_size = 640
scale_x = target_size / orig_w
scale_y = target_size / orig_h

resized = cv2.resize(image, (target_size, target_size))
rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
normalized = rgb_image.astype(np.float32) / 255.0
chw_image = np.transpose(normalized, (2, 0, 1))
batch_image = np.expand_dims(chw_image, axis=0)

inputs = {
    'image': batch_image,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
}

print(f"图像尺寸: {orig_w} x {orig_h}")
print(f"scale_x: {scale_x:.4f} ({orig_w} -> 640)")
print(f"scale_y: {scale_y:.4f} ({orig_h} -> 640)")

# 推理
session = ort.InferenceSession('/workspace/hngpt/models/doc_layout.optimized.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)
detections = outputs[0]

print(f"\n分析高置信度检测的坐标转换:")

# 分析几个关键检测
key_detections = []
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        if confidence > 0.5:
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            key_detections.append((class_name, confidence, x1, y1, x2, y2))

key_detections.sort(key=lambda x: x[1], reverse=True)

for i, (name, conf, x1, y1, x2, y2) in enumerate(key_detections[:5]):
    print(f"\n{i+1}. {name} (置信度: {conf:.3f})")
    print(f"   原始坐标: ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
    
    # 测试不同的坐标转换方法
    print(f"   坐标转换测试:")
    
    # 当前方法 (除以2.3)
    current_x1 = int(x1 / 2.3)
    current_y1 = int(y1 / 2.3)
    current_x2 = int(x2 / 2.3)
    current_y2 = int(y2 / 2.3)
    print(f"   当前方法 (÷2.3): ({current_x1}, {current_y1}, {current_x2}, {current_y2})")
    
    # 方法1: 根据你的分析，坐标是相对于640x640的
    method1_x1 = int(x1 * orig_w / 640)
    method1_y1 = int(y1 * orig_h / 640)
    method1_x2 = int(x2 * orig_w / 640)
    method1_y2 = int(y2 * orig_h / 640)
    print(f"   方法1 (640缩放): ({method1_x1}, {method1_y1}, {method1_x2}, {method1_y2})")
    
    # 方法2: 使用scale_factor反向缩放
    method2_x1 = int(x1 / scale_x)
    method2_y1 = int(y1 / scale_y)
    method2_x2 = int(x2 / scale_x)
    method2_y2 = int(y2 / scale_y)
    print(f"   方法2 (scale反向): ({method2_x1}, {method2_y1}, {method2_x2}, {method2_y2})")
    
    # 方法3: X和Y使用不同的缩放因子
    method3_x1 = int(x1 / 1.8)  # X方向使用较小的缩放因子
    method3_y1 = int(y1 / 2.3)  # Y方向保持2.3
    method3_x2 = int(x2 / 1.8)
    method3_y2 = int(y2 / 2.3)
    print(f"   方法3 (X÷1.8,Y÷2.3): ({method3_x1}, {method3_y1}, {method3_x2}, {method3_y2})")
    
    # 检查哪种方法的坐标更合理
    methods = [
        ("当前", current_x1, current_y1, current_x2, current_y2),
        ("640缩放", method1_x1, method1_y1, method1_x2, method1_y2),
        ("scale反向", method2_x1, method2_y1, method2_x2, method2_y2),
        ("分离缩放", method3_x1, method3_y1, method3_x2, method3_y2)
    ]
    
    for method_name, mx1, my1, mx2, my2 in methods:
        width = mx2 - mx1
        height = my2 - my1
        valid = (0 <= mx1 < orig_w and 0 <= my1 < orig_h and 
                mx1 < mx2 <= orig_w and my1 < my2 <= orig_h and
                width > 10 and height > 10)
        print(f"   {method_name:8s}: 尺寸 {width:3d}x{height:3d} {'✅' if valid else '❌'}")

print("\n分析完成")
