mode: paddle
draw_threshold: 0.5
metric: COCO
use_dynamic_shape: false
Global:
  model_name: PP-DocLayout-L
arch: DETR
min_subgraph_size: 3
Preprocess:
- interp: 2
  keep_ratio: false
  target_size:
  - 640
  - 640
  type: Resize
- mean:
  - 0.0
  - 0.0
  - 0.0
  norm_type: none
  std:
  - 1.0
  - 1.0
  - 1.0
  type: NormalizeImage
- type: Permute
label_list:
- paragraph_title
- image
- text
- number
- abstract
- content
- figure_title
- formula
- table
- table_title
- reference
- doc_title
- footnote
- header
- algorithm
- footer
- seal
- chart_title
- chart
- formula_number
- header_image
- footer_image
- aside_text
Hpi:
  backend_configs:
    paddle_infer:
      trt_dynamic_shapes: &id001
        im_shape:
        - - 1
          - 2
        - - 1
          - 2
        - - 8
          - 2
        image:
        - - 1
          - 3
          - 640
          - 640
        - - 1
          - 3
          - 640
          - 640
        - - 8
          - 3
          - 640
          - 640
        scale_factor:
        - - 1
          - 2
        - - 1
          - 2
        - - 8
          - 2
      trt_dynamic_shape_input_data:
        im_shape:
        - - 640
          - 640
        - - 640
          - 640
        - - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
          - 640
        scale_factor:
        - - 2
          - 2
        - - 1
          - 1
        - - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
          - 0.67
    tensorrt:
      dynamic_shapes: *id001
