#!/usr/bin/env python3
"""
PP-DocLayout-L ONNX 推理脚本
用于文档版面分析
"""
import cv2
import json
import time
import numpy as np
from typing import List, Dict, Tuple
import argparse
import os

# 尝试导入不同的推理引擎
try:
    import tensorrt as trt
    import pycuda.driver as cuda
    import pycuda.autoinit
    TRT_AVAILABLE = True
except ImportError:
    TRT_AVAILABLE = False

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False


class TrtModel:
    """TensorRT 推理引擎 - 基于OCR实现"""

    def __init__(self, engine_path: str, logger_severity: int = trt.Logger.INFO):
        self.engine_path = engine_path
        self.logger = trt.Logger(logger_severity)

        # 加载引擎
        with open(engine_path, 'rb') as f:
            runtime = trt.Runtime(self.logger)
            self.engine = runtime.deserialize_cuda_engine(f.read())

        self.context = self.engine.create_execution_context()

        # 映射名称到索引
        self.bind_idx = {self.engine.get_binding_name(i): i for i in range(self.engine.num_bindings)}

        # 获取输入输出信息
        self.input_names = []
        self.output_names = []
        for i in range(self.engine.num_bindings):
            name = self.engine.get_binding_name(i)
            if self.engine.binding_is_input(i):
                self.input_names.append(name)
            else:
                self.output_names.append(name)

    def infer(self, inputs_dict: dict) -> list:
        """
        执行推理

        Args:
            inputs_dict: 输入字典 {name: numpy_array}

        Returns:
            outputs: 输出列表
        """
        # 设置动态形状
        for name, input_np in inputs_dict.items():
            if name in self.bind_idx:
                idx = self.bind_idx[name]
                if not input_np.flags['C_CONTIGUOUS']:
                    input_np = np.ascontiguousarray(input_np)
                self.context.set_binding_shape(idx, tuple(input_np.shape))

        # 分配设备内存
        d_inputs = []
        d_outputs = []
        host_outputs = []
        bindings = [None] * self.engine.num_bindings

        # 处理输入
        for name, input_np in inputs_dict.items():
            if name in self.bind_idx:
                idx = self.bind_idx[name]
                if not input_np.flags['C_CONTIGUOUS']:
                    input_np = np.ascontiguousarray(input_np)
                d_in = cuda.mem_alloc(input_np.nbytes)
                cuda.memcpy_htod(d_in, input_np)
                bindings[idx] = int(d_in)
                d_inputs.append(d_in)

        # 处理输出
        for i in range(self.engine.num_bindings):
            if self.engine.binding_is_input(i):
                continue
            out_shape = tuple(self.context.get_binding_shape(i))
            out_dtype = trt.nptype(self.engine.get_binding_dtype(i))
            size = int(np.prod(out_shape))
            host_out = cuda.pagelocked_empty(size, out_dtype)
            d_out = cuda.mem_alloc(host_out.nbytes)
            bindings[i] = int(d_out)
            d_outputs.append(d_out)
            host_outputs.append((host_out, out_shape))

        # 执行推理
        self.context.execute_v2(bindings)

        # 复制结果回主机
        outputs = []
        for (host_out, out_shape), d_out in zip(host_outputs, d_outputs):
            cuda.memcpy_dtoh(host_out, d_out)
            outputs.append(host_out.reshape(out_shape))

        # 释放设备内存
        for d in d_inputs + d_outputs:
            d.free()

        return outputs

# PP-DocLayout-L 的23个类别映射
CLASS_NAMES = [
    "paragraph_title",  # 0 - 段落标题
    "image",           # 1 - 图像
    "text",            # 2 - 文本
    "number",          # 3 - 数字
    "abstract",        # 4 - 摘要
    "content",         # 5 - 内容
    "chart",           # 6 - 图表
    "figure",          # 7 - 图形
    "figure_caption",  # 8 - 图形标题
    "formula",         # 9 - 公式
    "handwriting",     # 10 - 手写
    "doc_title",       # 11 - 文档标题
    "footnote",        # 12 - 脚注
    "header",          # 13 - 页眉
    "algorithm",       # 14 - 算法
    "reference",       # 15 - 参考文献
    "seal",            # 16 - 印章
    "list",            # 17 - 列表
    "table",           # 18 - 表格
    "code",            # 19 - 代码
    "footer",          # 20 - 页脚
    "footer_image",    # 21 - 页脚图像
    "table_caption"    # 22 - 表格标题
]

class DocLayoutDetector:
    """PP-DocLayout-L 文档版面检测器"""

    def __init__(self, model_path: str, conf_threshold: float = 0.3):
        """
        初始化检测器

        Args:
            model_path: 模型路径 (支持 .onnx 和 .trt)
            conf_threshold: 置信度阈值
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.engine_type = None

        # 根据文件扩展名选择推理引擎
        if model_path.endswith('.trt'):
            if not TRT_AVAILABLE:
                raise RuntimeError("TensorRT 不可用，请安装 tensorrt 和 pycuda")
            self._init_tensorrt()
        elif model_path.endswith('.onnx'):
            if not ONNX_AVAILABLE:
                raise RuntimeError("ONNX Runtime 不可用，请安装 onnxruntime")
            self._init_onnx()
        else:
            raise ValueError(f"不支持的模型格式: {model_path}")

    def _init_tensorrt(self):
        """初始化 TensorRT 引擎"""
        self.engine_type = 'tensorrt'

        # 检查TensorRT引擎文件是否存在
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"TensorRT引擎文件不存在: {self.model_path}")

        # 初始化TensorRT模型
        self.trt_model = TrtModel(self.model_path)

        print(f"✓ TensorRT 引擎加载成功: {self.model_path}")
        print(f"  输入: {self.trt_model.input_names}")
        print(f"  输出: {self.trt_model.output_names}")

        # 保存输入输出名称以便后续使用
        self.input_names = self.trt_model.input_names
        self.output_names = self.trt_model.output_names

    def _init_onnx(self):
        """初始化 ONNX Runtime"""
        self.engine_type = 'onnx'
        providers = ['CPUExecutionProvider']
        self.session = ort.InferenceSession(self.model_path, providers=providers)

        # 获取输入输出信息
        self.input_names = [inp.name for inp in self.session.get_inputs()]
        self.output_names = [output.name for output in self.session.get_outputs()]

        print(f"✓ ONNX 模型加载成功: {self.model_path}")
        print(f"  输入: {self.input_names}")
        print(f"  输出: {self.output_names}")
    
    def preprocess(self, image: np.ndarray) -> Tuple[Dict, float, float]:
        """
        图像预处理 - 修复版本

        Args:
            image: 输入图像 (H, W, C)

        Returns:
            inputs: 模型输入字典
            scale_x: X轴缩放比例
            scale_y: Y轴缩放比例
        """
        orig_h, orig_w = image.shape[:2]
        target_size = 640  # PP-DocLayout-L 使用640x640输入

        # 计算缩放比例
        scale_x = target_size / orig_w
        scale_y = target_size / orig_h

        # 关键修复：直接缩放到640x640，不保持宽高比
        resized = cv2.resize(image, (target_size, target_size))

        # 转换为模型输入格式 (1, 3, 640, 640)
        # BGR -> RGB
        rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)

        # 归一化
        normalized = rgb_image.astype(np.float32) / 255.0

        # HWC -> CHW
        chw_image = np.transpose(normalized, (2, 0, 1))

        # 添加batch维度
        batch_image = np.expand_dims(chw_image, axis=0)

        # 准备所有输入 - 根据你的分析修正
        inputs = {
            'image': batch_image,
            'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
            'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)  # [h_scale, w_scale]
        }

        return inputs, scale_x, scale_y
    
    def postprocess(self, outputs: List[np.ndarray], orig_width: int, orig_height: int,
                   scale_x: float, scale_y: float) -> List[Dict]:
        """
        后处理：解析检测结果 - 修复版本

        Args:
            outputs: 模型输出
            orig_width: 原始图像宽度
            orig_height: 原始图像高度
            scale_x: X轴缩放比例
            scale_y: Y轴缩放比例

        Returns:
            results: 检测结果列表
        """
        results = []

        # PP-DocLayout-L 输出格式分析
        # 根据输出名称选择正确的检测结果
        detections = None
        if len(outputs) >= 1:
            # 尝试不同的输出索引
            for i, output in enumerate(outputs):
                if output.ndim == 2 and output.shape[1] >= 6:
                    detections = output
                    print(f"使用输出 {i}: {output.shape}")
                    break

        if detections is None:
            print("未找到有效的检测输出")
            return results

        print(f"处理 {len(detections)} 个检测结果...")

        for i, detection in enumerate(detections):
            if len(detection) >= 6:
                class_id = int(detection[0])
                confidence = float(detection[1])
                x1, y1, x2, y2 = detection[2:6]

                # 先收集所有检测，稍后过滤
                if confidence < 0.1:  # 只过滤极低置信度
                    continue

                # 关键修复：根据原始图像与网络输入尺寸计算精确缩放因子
                # 原始图像: 1077x1482, 网络输入: 640x640
                target_size = 640
                scale_x = target_size / orig_width   # 640 / 1077 = 0.5942
                scale_y = target_size / orig_height  # 640 / 1482 = 0.4318
                coord_scale_factor_x = 1.0 / scale_x  # 1.6828
                coord_scale_factor_y = 1.0 / scale_y  # 2.3156

                orig_x1 = int(x1 / coord_scale_factor_x)
                orig_y1 = int(y1 / coord_scale_factor_y)
                orig_x2 = int(x2 / coord_scale_factor_x)
                orig_y2 = int(y2 / coord_scale_factor_y)

                # 确保坐标在合理范围内
                orig_x1 = max(0, min(orig_x1, orig_width))
                orig_y1 = max(0, min(orig_y1, orig_height))
                orig_x2 = max(orig_x1, min(orig_x2, orig_width))
                orig_y2 = max(orig_y1, min(orig_y2, orig_height))

                # 确保坐标在合理范围内
                orig_x1 = max(0, min(orig_x1, orig_width))
                orig_y1 = max(0, min(orig_y1, orig_height))
                orig_x2 = max(orig_x1, min(orig_x2, orig_width))
                orig_y2 = max(orig_y1, min(orig_y2, orig_height))

                # 确保边界框有效
                if orig_x2 <= orig_x1 or orig_y2 <= orig_y1:
                    continue

                # 保留所有有效检测，稍后按置信度过滤
                # if confidence < self.conf_threshold:
                #     continue

                class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"

                results.append({
                    'cls_id': class_id,
                    'label': class_name,
                    'score': confidence,
                    'coordinate': [orig_x1, orig_y1, orig_x2, orig_y2],
                    'bbox': [orig_x1, orig_y1, orig_x2, orig_y2],
                    'area': (orig_x2 - orig_x1) * (orig_y2 - orig_y1)
                })

        # 按置信度排序
        results.sort(key=lambda x: x['score'], reverse=True)

        # 应用置信度过滤
        filtered_results = [r for r in results if r['score'] >= self.conf_threshold]

        # 简单的NMS去重：去除高度重叠的同类检测
        final_results = self._simple_nms(filtered_results)

        print(f"总检测数量: {len(results)}")
        print(f"高置信度检测 (>={self.conf_threshold}): {len(filtered_results)}")
        print(f"NMS后检测: {len(final_results)}")

        return final_results

    def _simple_nms(self, results, iou_threshold=0.5):
        """简单的NMS去重"""
        if not results:
            return results

        # 按类别分组
        class_groups = {}
        for result in results:
            cls_id = result['cls_id']
            if cls_id not in class_groups:
                class_groups[cls_id] = []
            class_groups[cls_id].append(result)

        final_results = []
        for cls_id, group in class_groups.items():
            # 对每个类别进行NMS
            group.sort(key=lambda x: x['score'], reverse=True)
            keep = []

            for i, current in enumerate(group):
                should_keep = True
                for kept in keep:
                    if self._calculate_iou(current['coordinate'], kept['coordinate']) > iou_threshold:
                        should_keep = False
                        break
                if should_keep:
                    keep.append(current)

            final_results.extend(keep)

        # 重新按置信度排序
        final_results.sort(key=lambda x: x['score'], reverse=True)
        return final_results

    def _calculate_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)

        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0

        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def _calculate_coord_scale_factor(self, x1, y1, x2, y2, orig_width, orig_height):
        """动态计算坐标缩放因子"""
        # 如果坐标已经在合理范围内，不需要缩放
        if x2 <= orig_width and y2 <= orig_height:
            return 1.0

        # 计算需要的缩放因子
        scale_x = x2 / orig_width if x2 > orig_width else 1.0
        scale_y = y2 / orig_height if y2 > orig_height else 1.0

        # 使用较大的缩放因子，并添加一些余量
        scale_factor = max(scale_x, scale_y) * 1.1

        # 限制在合理范围内
        scale_factor = max(1.0, min(scale_factor, 5.0))

        return scale_factor
    
    def predict(self, image: np.ndarray) -> List[Dict]:
        """
        完整的预测流程

        Args:
            image: 输入图像

        Returns:
            results: 检测结果
        """
        orig_h, orig_w = image.shape[:2]

        # 预处理
        inputs, scale_x, scale_y = self.preprocess(image)

        # 推理
        if self.engine_type == 'onnx':
            outputs = self.session.run(self.output_names, inputs)
        elif self.engine_type == 'tensorrt':
            outputs = self.trt_model.infer(inputs)
        else:
            raise ValueError(f"不支持的推理引擎类型: {self.engine_type}")

        # 后处理
        results = self.postprocess(outputs, orig_w, orig_h, scale_x, scale_y)

        return results

def draw_results(image: np.ndarray, results: List[Dict]) -> np.ndarray:
    """绘制检测结果"""
    annotated = image.copy()
    colors = [
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 0, 255),    # 红色
        (255, 255, 0),  # 青色
        (255, 0, 255),  # 品红
        (0, 255, 255),  # 黄色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]
    
    for i, result in enumerate(results):
        x1, y1, x2, y2 = result['coordinate']
        label = result['label']
        score = result['score']
        
        # 选择颜色
        color = colors[i % len(colors)]
        
        # 绘制边界框
        cv2.rectangle(annotated, (x1, y1), (x2, y2), color, 2)
        
        # 绘制标签
        label_text = f"{label}: {score:.2f}"
        label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)[0]
        
        # 标签背景
        cv2.rectangle(annotated, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), color, -1)
        
        # 标签文字
        cv2.putText(annotated, label_text, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    return annotated

def main():
    parser = argparse.ArgumentParser(description='PP-DocLayout-L 文档版面分析')
    parser.add_argument('--model', default='/workspace/hngpt/models/doc_layout.trt', help='TensorRT模型路径')
    parser.add_argument('--image', default='/workspace/hngpt/document.png', help='输入图像路径')
    parser.add_argument('--output', default='output', help='输出目录')
    parser.add_argument('--conf', type=float, default=0.7, help='置信度阈值')
    
    args = parser.parse_args()
    
    print("🔍 PP-DocLayout-L 文档版面分析")
    print("=" * 50)
    
    # 检查文件
    if not os.path.exists(args.model):
        print(f"✗ 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.image):
        print(f"✗ 图像文件不存在: {args.image}")
        return
    
    # 加载图像
    print(f"📷 加载图像: {args.image}")
    image = cv2.imread(args.image)
    if image is None:
        print(f"✗ 无法加载图像: {args.image}")
        return
    
    orig_h, orig_w = image.shape[:2]
    print(f"✓ 图像加载成功，尺寸: {orig_w} x {orig_h}")
    
    # 初始化检测器
    print("📦 初始化检测器...")
    detector = DocLayoutDetector(args.model, conf_threshold=args.conf)
    
    # 执行推理
    print("🔍 执行推理...")
    start_time = time.time()
    results = detector.predict(image)
    inference_time = time.time() - start_time
    
    print(f"✓ 推理完成，耗时: {inference_time*1000:.2f}ms")
    print(f"✓ 检测到 {len(results)} 个版面元素")
    
    # 显示结果
    if results:
        print(f"\n=== 检测结果 ===")
        for i, result in enumerate(results):
            coord = result['coordinate']
            print(f"{i+1:2d}. {result['label']:15s} "
                  f"置信度: {result['score']:.3f} "
                  f"坐标: [{coord[0]}, {coord[1]}, {coord[2]}, {coord[3]}]")
        
        # 统计类别
        print(f"\n📊 类别统计:")
        category_counts = {}
        for result in results:
            label = result['label']
            category_counts[label] = category_counts.get(label, 0) + 1
        
        for label, count in sorted(category_counts.items()):
            print(f"  {label:<15}: {count}")
    else:
        print("未检测到任何版面元素")
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 保存JSON结果
    json_path = os.path.join(args.output, 'results.json')
    result_data = {
        'image_path': args.image,
        'image_size': [orig_w, orig_h],
        'inference_time_ms': inference_time * 1000,
        'num_detections': len(results),
        'results': results
    }
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2, ensure_ascii=False)
    print(f"✓ 结果已保存到: {json_path}")
    
    # 生成可视化结果
    if results:
        print("🎨 生成可视化结果...")
        annotated_image = draw_results(image, results)
        
        output_image_path = os.path.join(args.output, 'result_annotated.jpg')
        cv2.imwrite(output_image_path, annotated_image)
        print(f"✓ 可视化结果已保存到: {output_image_path}")
    
    print(f"\n🎉 分析完成!")

if __name__ == "__main__":
    main()
