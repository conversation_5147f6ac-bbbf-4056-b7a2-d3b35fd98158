import cv2
import numpy as np
import onnxruntime as ort

CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]

# 预处理
target_size = 640
scale_x = target_size / orig_w
scale_y = target_size / orig_h

resized = cv2.resize(image, (target_size, target_size))
rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
normalized = rgb_image.astype(np.float32) / 255.0
chw_image = np.transpose(normalized, (2, 0, 1))
batch_image = np.expand_dims(chw_image, axis=0)

inputs = {
    'image': batch_image,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
}

# 推理
session = ort.InferenceSession('/workspace/hngpt/models/doc_layout.optimized.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)
detections = outputs[0]

print(f"图像尺寸: {orig_w} x {orig_h}")
print(f"scale_x: {scale_x:.4f}, scale_y: {scale_y:.4f}")

# 分析所有高置信度检测的坐标转换
high_conf_detections = []
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        if confidence > 0.4:
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            high_conf_detections.append((class_name, confidence, x1, y1, x2, y2))

high_conf_detections.sort(key=lambda x: x[1], reverse=True)

print(f"\n测试不同坐标转换方法:")
for name, conf, x1, y1, x2, y2 in high_conf_detections[:3]:
    print(f"\n{name} (置信度: {conf:.3f})")
    print(f"原始坐标: ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
    
    # 方法1: 直接使用
    m1_x1, m1_y1, m1_x2, m1_y2 = int(x1), int(y1), int(x2), int(y2)
    m1_valid = 0 <= m1_x1 < orig_w and 0 <= m1_y1 < orig_h and m1_x1 < m1_x2 <= orig_w and m1_y1 < m1_y2 <= orig_h
    print(f"方法1 (直接): ({m1_x1}, {m1_y1}, {m1_x2}, {m1_y2}) {'✅' if m1_valid else '❌'}")
    
    # 方法2: 除以 scale
    m2_x1, m2_y1 = int(x1 / scale_x), int(y1 / scale_y)
    m2_x2, m2_y2 = int(x2 / scale_x), int(y2 / scale_y)
    m2_valid = 0 <= m2_x1 < orig_w and 0 <= m2_y1 < orig_h and m2_x1 < m2_x2 <= orig_w and m2_y1 < m2_y2 <= orig_h
    print(f"方法2 (除scale): ({m2_x1}, {m2_y1}, {m2_x2}, {m2_y2}) {'✅' if m2_valid else '❌'}")
    
    # 方法3: 从640缩放
    m3_x1, m3_y1 = int(x1 * orig_w / 640), int(y1 * orig_h / 640)
    m3_x2, m3_y2 = int(x2 * orig_w / 640), int(y2 * orig_h / 640)
    m3_valid = 0 <= m3_x1 < orig_w and 0 <= m3_y1 < orig_h and m3_x1 < m3_x2 <= orig_w and m3_y1 < m3_y2 <= orig_h
    print(f"方法3 (640缩放): ({m3_x1}, {m3_y1}, {m3_x2}, {m3_y2}) {'✅' if m3_valid else '❌'}")

print("\n调试完成")
