import cv2
import numpy as np
import onnxruntime as ort

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]

# 预处理
target_size = 640
scale_x = target_size / orig_w
scale_y = target_size / orig_h

resized = cv2.resize(image, (target_size, target_size))
rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
normalized = rgb_image.astype(np.float32) / 255.0
chw_image = np.transpose(normalized, (2, 0, 1))
batch_image = np.expand_dims(chw_image, axis=0)

inputs = {
    'image': batch_image,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
}

# 推理
session = ort.InferenceSession('/workspace/hngpt/models/doc_layout.optimized.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)
detections = outputs[0]

print(f"图像尺寸: {orig_w} x {orig_h}")
print(f"scale_x: {scale_x:.4f}, scale_y: {scale_y:.4f}")

# 分析 seal 的坐标
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        if class_id == 16 and confidence > 0.9:  # seal
            print(f"\nSeal 坐标分析:")
            print(f"置信度: {confidence:.3f}")
            print(f"原始坐标: ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
            
            # 当前的错误转换
            current_x1 = max(0, min(int(x1), orig_w - 1))
            current_y1 = max(0, min(int(y1), orig_h - 1))
            current_x2 = max(current_x1 + 1, min(int(x2), orig_w))
            current_y2 = max(current_y1 + 1, min(int(y2), orig_h))
            print(f"当前转换: ({current_x1}, {current_y1}, {current_x2}, {current_y2})")
            print(f"当前尺寸: 宽度={current_x2-current_x1}, 高度={current_y2-current_y1}")
            
            # 正确的转换应该是什么？
            # 从原始坐标看，seal 应该在图像的某个位置
            # 让我们尝试不同的缩放方法
            
            print(f"\n尝试不同的坐标转换:")
            
            # 方法1: 假设坐标是相对于某个更大的坐标系
            # 从 (1020.6, 2681.1, 1341.5, 3127.6) 来看，这可能是相对于一个更大的画布
            
            # 计算可能的原始画布尺寸
            canvas_w = max(x2, orig_w * 2)  # 假设画布至少是图像的2倍
            canvas_h = max(y2, orig_h * 2)
            
            # 按比例缩放到原始图像
            scale_canvas_x = orig_w / canvas_w
            scale_canvas_y = orig_h / canvas_h
            
            method1_x1 = int(x1 * scale_canvas_x)
            method1_y1 = int(y1 * scale_canvas_y)
            method1_x2 = int(x2 * scale_canvas_x)
            method1_y2 = int(y2 * scale_canvas_y)
            
            print(f"方法1 (画布缩放): ({method1_x1}, {method1_y1}, {method1_x2}, {method1_y2})")
            
            # 方法2: 假设坐标需要除以某个固定因子
            for factor in [2.0, 2.5, 3.0, 4.0]:
                method2_x1 = int(x1 / factor)
                method2_y1 = int(y1 / factor)
                method2_x2 = int(x2 / factor)
                method2_y2 = int(y2 / factor)
                
                if (0 <= method2_x1 < orig_w and 0 <= method2_y1 < orig_h and 
                    method2_x1 < method2_x2 <= orig_w and method2_y1 < method2_y2 <= orig_h):
                    print(f"方法2 (除以{factor}): ({method2_x1}, {method2_y1}, {method2_x2}, {method2_y2}) ✅")
                else:
                    print(f"方法2 (除以{factor}): ({method2_x1}, {method2_y1}, {method2_x2}, {method2_y2}) ❌")
            
            break

print("\n调试完成")
