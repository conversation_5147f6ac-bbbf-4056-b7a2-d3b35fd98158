#!/usr/bin/env python3
import cv2
import numpy as np
import onnxruntime as ort

print("🔍 快速推理测试")

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]
print(f"图像尺寸: {orig_w} x {orig_h}")

# 预处理
target_size = 640
scale = min(target_size / orig_w, target_size / orig_h)
new_w = int(orig_w * scale)
new_h = int(orig_h * scale)

resized = cv2.resize(image, (new_w, new_h))
canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
start_x = (target_size - new_w) // 2
start_y = (target_size - new_h) // 2
canvas[start_y:start_y+new_h, start_x:start_x+new_w] = resized

canvas = canvas.astype(np.float32) / 255.0
canvas = np.transpose(canvas, (2, 0, 1))
canvas = np.expand_dims(canvas, axis=0)

inputs = {
    'image': canvas,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale, scale]], dtype=np.float32)
}

print(f"预处理完成，scale={scale:.4f}")

# 推理
session = ort.InferenceSession('inference.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)

print(f"推理完成，输出形状: {[out.shape for out in outputs]}")

# 解析结果
detections = outputs[0]
print(f"检测结果数量: {len(detections)}")

valid_results = []
for detection in detections:
    if len(detection) >= 6 and detection[1] > 0.3:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        # 坐标转换
        orig_x1 = int((x1 - start_x) / scale)
        orig_y1 = int((y1 - start_y) / scale)
        orig_x2 = int((x2 - start_x) / scale)
        orig_y2 = int((y2 - start_y) / scale)
        
        # 限制范围
        orig_x1 = max(0, min(orig_x1, orig_w))
        orig_y1 = max(0, min(orig_y1, orig_h))
        orig_x2 = max(orig_x1, min(orig_x2, orig_w))
        orig_y2 = max(orig_y1, min(orig_y2, orig_h))
        
        if orig_x2 > orig_x1 and orig_y2 > orig_y1:
            valid_results.append((class_id, confidence, orig_x1, orig_y1, orig_x2, orig_y2))

valid_results.sort(key=lambda x: x[1], reverse=True)

print(f"\n检测到 {len(valid_results)} 个有效元素:")
for i, (class_id, conf, x1, y1, x2, y2) in enumerate(valid_results):
    print(f"{i+1}. 类别={class_id}, 置信度={conf:.3f}, 坐标=[{x1}, {y1}, {x2}, {y2}]")

print("测试完成!")
