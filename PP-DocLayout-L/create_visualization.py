#!/usr/bin/env python3
import cv2
import numpy as np
import onnxruntime as ort
import json

# 类别名称
CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

print("🎨 创建可视化结果")

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]
print(f"图像尺寸: {orig_w} x {orig_h}")

# 预处理
target_size = 640
scale = min(target_size / orig_w, target_size / orig_h)
new_w = int(orig_w * scale)
new_h = int(orig_h * scale)

resized = cv2.resize(image, (new_w, new_h))
canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
start_x = (target_size - new_w) // 2
start_y = (target_size - new_h) // 2
canvas[start_y:start_y+new_h, start_x:start_x+new_w] = resized

canvas_input = canvas.astype(np.float32) / 255.0
canvas_input = np.transpose(canvas_input, (2, 0, 1))
canvas_input = np.expand_dims(canvas_input, axis=0)

inputs = {
    'image': canvas_input,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale, scale]], dtype=np.float32)
}

# 推理
session = ort.InferenceSession('inference.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)

# 解析结果
detections = outputs[0]
results = []

for detection in detections:
    if len(detection) >= 6 and detection[1] > 0.3:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        # 坐标转换
        orig_x1 = int((x1 - start_x) / scale)
        orig_y1 = int((y1 - start_y) / scale)
        orig_x2 = int((x2 - start_x) / scale)
        orig_y2 = int((y2 - start_y) / scale)
        
        # 限制范围
        orig_x1 = max(0, min(orig_x1, orig_w))
        orig_y1 = max(0, min(orig_y1, orig_h))
        orig_x2 = max(orig_x1, min(orig_x2, orig_w))
        orig_y2 = max(orig_y1, min(orig_y2, orig_h))
        
        if orig_x2 > orig_x1 and orig_y2 > orig_y1:
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            results.append({
                'cls_id': class_id,
                'label': class_name,
                'score': confidence,
                'coordinate': [orig_x1, orig_y1, orig_x2, orig_y2]
            })

results.sort(key=lambda x: x['score'], reverse=True)

print(f"检测到 {len(results)} 个元素:")
for i, result in enumerate(results):
    coord = result['coordinate']
    print(f"{i+1}. {result['label']:15s} 置信度: {result['score']:.3f} 坐标: [{coord[0]}, {coord[1]}, {coord[2]}, {coord[3]}]")

# 绘制结果
annotated = image.copy()
colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]

for i, result in enumerate(results):
    x1, y1, x2, y2 = result['coordinate']
    label = result['label']
    score = result['score']
    color = colors[i % len(colors)]
    
    # 绘制边界框
    cv2.rectangle(annotated, (x1, y1), (x2, y2), color, 3)
    
    # 绘制标签
    label_text = f"{label}: {score:.2f}"
    label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
    
    # 标签背景
    cv2.rectangle(annotated, (x1, y1 - label_size[1] - 10), 
                 (x1 + label_size[0], y1), color, -1)
    
    # 标签文字
    cv2.putText(annotated, label_text, (x1, y1 - 5), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

# 保存结果
cv2.imwrite('document_layout_result.jpg', annotated)
print("✓ 可视化结果已保存到: document_layout_result.jpg")

# 保存JSON
with open('document_layout_result.json', 'w') as f:
    json.dump({
        'image_path': '/workspace/hngpt/document.png',
        'image_size': [orig_w, orig_h],
        'num_detections': len(results),
        'results': results
    }, f, indent=2)

print("✓ JSON结果已保存到: document_layout_result.json")
print("🎉 分析完成!")
