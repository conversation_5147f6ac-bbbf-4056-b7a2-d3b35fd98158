import cv2
import numpy as np
import onnxruntime as ort

CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

print("调试开始")

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]
print(f"图像尺寸: {orig_w} x {orig_h}")

# 预处理
target_size = 640
scale_x = target_size / orig_w
scale_y = target_size / orig_h

resized = cv2.resize(image, (target_size, target_size))
rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
normalized = rgb_image.astype(np.float32) / 255.0
chw_image = np.transpose(normalized, (2, 0, 1))
batch_image = np.expand_dims(chw_image, axis=0)

inputs = {
    'image': batch_image,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
}

print(f"预处理完成: scale_x={scale_x:.4f}, scale_y={scale_y:.4f}")

# 推理
session = ort.InferenceSession('/workspace/hngpt/models/doc_layout.optimized.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)

print(f"推理完成，输出形状: {[out.shape for out in outputs]}")

# 分析检测结果
detections = outputs[0]
print(f"检测结果数量: {len(detections)}")

# 统计所有检测
valid_count = 0
seal_count = 0
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        
        if confidence > 0.1:
            valid_count += 1
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            
            if class_name == 'seal':
                seal_count += 1
                print(f"发现 seal: 置信度 {confidence:.3f}")

print(f"有效检测数量 (>0.1): {valid_count}")
print(f"seal 检测数量: {seal_count}")

# 显示前10个高置信度检测
high_conf = []
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        if confidence > 0.2:
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            high_conf.append((class_name, confidence))

high_conf.sort(key=lambda x: x[1], reverse=True)
print(f"\n前10个高置信度检测:")
for i, (name, conf) in enumerate(high_conf[:10]):
    print(f"{i+1}. {name:15s} {conf:.3f}")

print("调试完成")
