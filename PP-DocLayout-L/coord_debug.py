import cv2
import numpy as np
import onnxruntime as ort

CLASS_NAMES = [
    "paragraph_title", "image", "text", "number", "abstract", "content", 
    "chart", "figure", "figure_caption", "formula", "handwriting", "doc_title",
    "footnote", "header", "algorithm", "reference", "seal", "list", 
    "table", "code", "footer", "footer_image", "table_caption"
]

print("坐标转换调试")

# 加载图像
image = cv2.imread('/workspace/hngpt/document.png')
orig_h, orig_w = image.shape[:2]
print(f"图像尺寸: {orig_w} x {orig_h}")

# 预处理
target_size = 640
scale_x = target_size / orig_w
scale_y = target_size / orig_h

resized = cv2.resize(image, (target_size, target_size))
rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
normalized = rgb_image.astype(np.float32) / 255.0
chw_image = np.transpose(normalized, (2, 0, 1))
batch_image = np.expand_dims(chw_image, axis=0)

inputs = {
    'image': batch_image,
    'im_shape': np.array([[orig_h, orig_w]], dtype=np.float32),
    'scale_factor': np.array([[scale_y, scale_x]], dtype=np.float32)
}

# 推理
session = ort.InferenceSession('/workspace/hngpt/models/doc_layout.optimized.onnx', providers=['CPUExecutionProvider'])
outputs = session.run(None, inputs)

detections = outputs[0]

# 查找高置信度检测并分析坐标
high_conf_detections = []
for detection in detections:
    if len(detection) >= 6:
        class_id = int(detection[0])
        confidence = float(detection[1])
        x1, y1, x2, y2 = detection[2:6]
        
        if confidence > 0.3:
            class_name = CLASS_NAMES[class_id] if class_id < len(CLASS_NAMES) else f"class_{class_id}"
            high_conf_detections.append((class_name, confidence, x1, y1, x2, y2))

high_conf_detections.sort(key=lambda x: x[1], reverse=True)

print(f"\n高置信度检测分析:")
for i, (name, conf, x1, y1, x2, y2) in enumerate(high_conf_detections):
    print(f"{i+1}. {name:15s} 置信度: {conf:.3f}")
    print(f"   原始坐标: ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
    
    # 坐标转换
    orig_x1 = int(x1 / scale_x)
    orig_y1 = int(y1 / scale_y)
    orig_x2 = int(x2 / scale_x)
    orig_y2 = int(y2 / scale_y)
    
    print(f"   转换坐标: ({orig_x1}, {orig_y1}, {orig_x2}, {orig_y2})")
    
    # 检查坐标有效性
    if orig_x2 <= orig_x1 or orig_y2 <= orig_y1:
        print(f"   ❌ 无效边界框!")
    elif orig_x1 < 0 or orig_y1 < 0 or orig_x2 > orig_w or orig_y2 > orig_h:
        print(f"   ⚠️  坐标超出范围!")
    else:
        print(f"   ✅ 坐标有效")
    print()

print("调试完成")
