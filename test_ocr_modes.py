#!/usr/bin/env python3
"""
测试OCR API的三种模式：/ocr/file/, /ocr/base64/, /ocr/binary/
"""

import requests
import base64
import json
import time
import os

# 测试配置
BASE_URL = "http://localhost:8888"
TEST_IMAGE_PATH = "/workspace/hngpt/tests/test.png"

def test_ocr_file_mode():
    """测试 /ocr/file/ 模式"""
    print("\n🔍 测试 OCR File 模式")
    
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"  ❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        return False
    
    try:
        # 准备文件上传
        with open(TEST_IMAGE_PATH, 'rb') as f:
            files = {'file': ('test.png', f, 'image/png')}
            data = {'wait': 'true', 'timeout': '30.0'}
            
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/ocr/file/",
                files=files,
                data=data,
                timeout=35
            )
            response_time = time.time() - start_time
        
        print(f"  📊 状态码: {response.status_code}")
        print(f"  ⏱️ 响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ OCR File 模式成功")
            print(f"  📝 识别结果: {result.get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"  ❌ OCR File 模式失败")
            print(f"  📄 响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"  ❌ OCR File 模式异常: {e}")
        return False

def test_ocr_base64_mode():
    """测试 /ocr/base64/ 模式"""
    print("\n🔍 测试 OCR Base64 模式")
    
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"  ❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        return False
    
    try:
        # 读取图片并转换为base64
        with open(TEST_IMAGE_PATH, 'rb') as f:
            image_data = f.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
        
        # 准备JSON请求
        payload = {
            'image': base64_data,
            'wait': True,
            'timeout': 30.0
        }
        
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/ocr/base64/",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=35
        )
        response_time = time.time() - start_time
        
        print(f"  📊 状态码: {response.status_code}")
        print(f"  ⏱️ 响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ OCR Base64 模式成功")
            print(f"  📝 识别结果: {result.get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"  ❌ OCR Base64 模式失败")
            print(f"  📄 响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"  ❌ OCR Base64 模式异常: {e}")
        return False

def test_ocr_binary_mode():
    """测试 /ocr/binary/ 模式"""
    print("\n🔍 测试 OCR Binary 模式")
    
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"  ❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        return False
    
    try:
        # 读取图片二进制数据
        with open(TEST_IMAGE_PATH, 'rb') as f:
            image_data = f.read()
        
        # 准备二进制请求，参数通过URL传递
        params = {'wait': 'true', 'timeout': '30.0'}
        
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/ocr/binary/",
            data=image_data,
            params=params,
            headers={'Content-Type': 'application/octet-stream'},
            timeout=35
        )
        response_time = time.time() - start_time
        
        print(f"  📊 状态码: {response.status_code}")
        print(f"  ⏱️ 响应时间: {response_time:.3f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ OCR Binary 模式成功")
            print(f"  📝 识别结果: {result.get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"  ❌ OCR Binary 模式失败")
            print(f"  📄 响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"  ❌ OCR Binary 模式异常: {e}")
        return False

def check_test_image():
    """检查测试图片是否存在"""
    if os.path.exists(TEST_IMAGE_PATH):
        file_size = os.path.getsize(TEST_IMAGE_PATH)
        print(f"📷 测试图片: {TEST_IMAGE_PATH}")
        print(f"📏 文件大小: {file_size} bytes")
        return True
    else:
        print(f"❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        return False

def main():
    print("🚀 OCR API 三种模式测试")
    print("=" * 50)
    
    # 检查测试图片
    if not check_test_image():
        print("\n❌ 无法进行测试，请确保测试图片存在")
        return
    
    # 测试结果统计
    results = {}
    
    # 测试三种模式
    results['file'] = test_ocr_file_mode()
    results['base64'] = test_ocr_base64_mode()
    results['binary'] = test_ocr_binary_mode()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    success_count = 0
    for mode, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} /ocr/{mode}/ 模式: {'成功' if success else '失败'}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/3 个模式测试成功")
    
    if success_count == 3:
        print("🎉 所有OCR模式都工作正常！")
    elif success_count > 0:
        print("⚠️ 部分OCR模式存在问题")
    else:
        print("❌ 所有OCR模式都失败了")

if __name__ == "__main__":
    main()
