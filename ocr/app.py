import numpy as np
import base64
from typing import Union
from fastapi import FastAPI, UploadFile, HTTPException, Body
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import cv2
import os

from utils import perform_ocr

class Base64Request(BaseModel):
    image: str
    image_type: str = "base64"

# Initialize FastAPI with custom docs_url and redoc_url
app = FastAPI(
    docs_url=None,  # Disable default docs url
    redoc_url=None  # Disable default redoc url
)

# Custom swagger UI route
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )

# Mount static files (only if directory exists)
static_dir = "static"
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    return {"message": "Welcome to the OCR API"}

ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "bmp", "tiff"}

def is_valid_image(filename: str) -> bool:
    return filename.lower().split(".")[-1] in ALLOWED_EXTENSIONS

def process_image_pil(image_data: Union[bytes, str]) -> np.ndarray:
    """使用OpenCV处理图像数据"""
    try:
        if isinstance(image_data, str):
            # 处理base64数据
            image_data = base64.b64decode(image_data)
            print(f"[DEBUG] Base64 decoded length: {len(image_data)}")
        
        # 确保image_data是bytes类型
        if not isinstance(image_data, bytes):
            raise ValueError(f"Expected bytes, got {type(image_data)}")
            
        # 转换为numpy数组前先转换为bytearray
        nparr = np.asarray(bytearray(image_data), dtype=np.uint8)
        print(f"[DEBUG] Numpy array shape: {nparr.shape}")
        
        # 使用OpenCV解码图像
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise ValueError("Failed to decode image")
            
        print(f"[DEBUG] Decoded image shape: {img.shape}")
        return img
    except Exception as e:
        print(f"[DEBUG] Error in process_image_pil: {str(e)}")
        raise

@app.post("/ocr/file/", 
    summary="Perform OCR on an uploaded file",
    description="Upload an image file to extract text using OCR"
)
async def ocr_file(file: UploadFile):
    try:
        print("[DEBUG] Processing file upload")
        # 检查文件扩展名
        if not file.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            raise HTTPException(
                status_code=400,
                detail="Unsupported file format"
            )
        
        # 读取文件内容
        contents = await file.read()
        print(f"[DEBUG] File content length: {len(contents)}")
        
        # 处理图像
        img = process_image_pil(contents)
        print(f"[DEBUG] Processed image shape: {img.shape}")
        
        # 执行OCR
        ocr_text = perform_ocr(img)
        
        return JSONResponse(
            content={
                "result": ocr_text,
                "message": "OCR completed successfully"
            },
            status_code=200
        )
    except Exception as e:
        print(f"[DEBUG] Error in ocr_file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during OCR processing: {str(e)}"
        )

@app.post("/ocr/base64/")
async def ocr_base64(request: Base64Request):
    try:
        print("[DEBUG] Processing base64 request")
        # 移除base64前缀（如果存在）
        if ';base64,' in request.image:
            request.image = request.image.split(';base64,')[1]
        
        # 处理图像
        img = process_image_pil(request.image)
        print(f"[DEBUG] Processed image shape: {img.shape}")
        
        # 执行OCR
        ocr_text = perform_ocr(img)
        
        return JSONResponse(
            content={
                "result": ocr_text,
                "message": "OCR completed successfully"
            },
            status_code=200
        )
    except Exception as e:
        print(f"[DEBUG] Error in ocr_base64: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during OCR processing: {str(e)}"
        )

@app.post("/ocr/binary/")
async def ocr_binary(image_data: bytes = Body(..., media_type="application/octet-stream")):
    try:
        print("[DEBUG] Processing binary request")
        print(f"[DEBUG] Binary data length: {len(image_data)}")
        
        # 处理图像
        img = process_image_pil(image_data)
        print(f"[DEBUG] Processed image shape: {img.shape}")
        
        # 执行OCR
        ocr_text = perform_ocr(img)
        
        return JSONResponse(
            content={
                "result": ocr_text,
                "message": "OCR completed successfully"
            },
            status_code=200
        )
    except Exception as e:
        print(f"[DEBUG] Error in ocr_binary: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during OCR processing: {str(e)}"
        )

# Add entry point
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

