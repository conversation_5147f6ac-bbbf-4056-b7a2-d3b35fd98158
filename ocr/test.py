import sys
from pathlib import Path
import os
import cv2
import numpy as np
from PIL import Image, ImageFont, ImageDraw

# 🎯 优化OCR参数以提高长文本识别效果
os.environ.setdefault("OCR_DET_SIZE", "960")        # 使用960匹配TensorRT模型固定尺寸
os.environ.setdefault("OCR_DB_MASK_THRESH", "0.3")  # 与 infer-trt.py 一致的掩码阈值
os.environ.setdefault("OCR_DB_BOX_THRESH", "0.6")   # 与 infer-trt.py 一致的框阈值
os.environ.setdefault("OCR_DB_UNCLIP_RATIO", "1.5") # 与 infer-trt.py 一致的展开比例
os.environ.setdefault("OCR_DET_KEEP_RATIO", "0")    # 固定 960x960，保持与 infer-trt 对齐
os.environ.setdefault("OCR_VERSION", "v5")          # 使用v5模型

# 🔧 关键修复：强制设置批次大小为1，避免TensorRT错误
os.environ["OCR_REC_BATCH"] = "1"  # 识别 batch 与 infer-trt.py 一致
os.environ.setdefault("OCR_CLS_BATCH", "1")  # 角度分类 batch 与 infer-trt.py 一致

# 路径设置
_dir = Path(__file__).parent
print(_dir)
# 优先加载当前目录下的 rec 扩展模块
sys.path.insert(0, str(_dir))
# 关键：加入 cpp/build，确保加载最新编译的 C++ 扩展
sys.path.insert(0, str(_dir / 'cpp' / 'build'))
# 添加项目根目录到路径
project_root = _dir.parent  # /workspace/hngpt
sys.path.append(str(project_root))

import rec

from infer import draw_ocr_box_txt as draw_ocr_box_txt_infer  # 统一沿用 infer.py 的绘图函数


# =============== 直接复用 infer.py 可视化风格 ===============

def create_font(txt, sz, font_path):
    font_size = int(sz[1] * 0.99)
    font = ImageFont.truetype(font_path, font_size, encoding="utf-8")
    length = font.getlength(txt)
    if length > sz[0]:
        font_size = int(font_size * sz[0] / max(1, length))
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")
    return font


def draw_box_txt(img_size, box, txt, font_path):
    box = np.array(box, dtype=np.float32)
    box_height = int(np.linalg.norm(box[0] - box[3]))
    box_width = int(np.linalg.norm(box[0] - box[1]))

    if box_height > 2 * box_width and box_height > 30:
        img_text = Image.new("RGB", (box_height, box_width), (255, 255, 255))
        draw_text = ImageDraw.Draw(img_text)
        font = create_font(txt, (box_height, box_width), font_path)
        draw_text.text([0, 0], txt, fill=(0, 0, 0), font=font)
        img_text = img_text.transpose(Image.ROTATE_270)
    else:
        img_text = Image.new("RGB", (box_width, box_height), (255, 255, 255))
        draw_text = ImageDraw.Draw(img_text)
        font = create_font(txt, (box_width, box_height), font_path)
        draw_text.text([0, 0], txt, fill=(0, 0, 0), font=font)

    pts1 = np.float32([[0, 0], [img_text.width, 0], [img_text.width, img_text.height], [0, img_text.height]])
    pts2 = np.array(box, dtype=np.float32)
    M = cv2.getPerspectiveTransform(pts1, pts2)

    img_text_np = np.array(img_text, dtype=np.uint8)
    img_right_text = cv2.warpPerspective(
        img_text_np,
        M,
        img_size,
        flags=cv2.INTER_NEAREST,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255)
    )
    return img_right_text


def draw_ocr_box_txt(image_bgr, boxes, txts, scores, font_path, drop_score=0.5):
    image = Image.fromarray(cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB))
    h, w = image.height, image.width
    img_left = image.copy()
    img_right = np.ones((h, w, 3), dtype=np.uint8) * 255

    if txts is None or len(txts) != len(boxes):
        txts = [None] * len(boxes)

    draw_left = ImageDraw.Draw(img_left)

    # 线条粗细与 infer.py 一致：固定为 1 像素
    thickness = 1

    # 稳定的颜色映射：不同索引输出不同颜色，但可复现
    def idx_color(i: int):
        import colorsys
        h_ = (i * 0.123457) % 1.0
        r, g, b = colorsys.hsv_to_rgb(h_, 0.7, 1.0)
        return (int(r * 255), int(g * 255), int(b * 255))  # RGB

    for idx, (box, txt) in enumerate(zip(boxes, txts)):
        if scores is not None and idx < len(scores) and scores[idx] < drop_score:
            continue
        color = idx_color(idx)
        # 左侧：只画边框，避免大面积遮挡
        pts = [tuple(map(int, p)) for p in np.array(box).reshape(-1, 2)]
        draw_left.line(pts + [pts[0]], fill=color, width=thickness)

        # 右侧：贴文本+边框
        img_right_text = draw_box_txt((w, h), box, txt or "", font_path)
        pts_np = np.array(box, np.int32).reshape((-1, 1, 2))
        color_bgr = (color[2], color[1], color[0])
        cv2.polylines(img_right_text, [pts_np], True, color_bgr, thickness)
        img_right = cv2.bitwise_and(img_right, img_right_text)

    # 降低叠加权重，保证原图清晰
    img_left = Image.blend(image, img_left, 0.35)
    img_show = Image.new("RGB", (w * 2, h), (255, 255, 255))
    img_show.paste(img_left, (0, 0, w, h))
    img_show.paste(Image.fromarray(cv2.cvtColor(img_right, cv2.COLOR_BGR2RGB)), (w, 0, w * 2, h))
    return np.array(img_show)


# 统一排序：按 x 从左到右，其次 y 从上到下（便于比较与可视化一致）
def sort_boxes_xy(boxes, txts, scores):
    if not boxes:
        return boxes, txts, scores
    def key(b):
        pts = np.array(b, dtype=np.float32).reshape(-1, 2)
        return (float(np.min(pts[:, 0])), float(np.min(pts[:, 1])))
    order = sorted(range(len(boxes)), key=lambda i: key(boxes[i]))
    boxes_s  = [boxes[i] for i in order]
    txts_s   = [txts[i] for i in order]
    scores_s = [scores[i] for i in order]
    return boxes_s, txts_s, scores_s

# ======================================================

def test_ocr_with_params(version="v5", gpu_id=0):
    """测试使用指定版本和GPU ID的OCR实例"""
    print(f"\n=== 测试 OCR 实例 (version={version}, gpu_id={gpu_id}) ===")

    # 图片路径
    img_path = '/workspace/hngpt/ocr/images/receipt.jpg'

    # 读取图片
    if not os.path.exists(img_path):
        print(f"图片文件不存在: {img_path}")
        print("创建一个测试图片...")
        color_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(color_img, f"OCR Test {version} GPU{gpu_id}", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    else:
        color_img = cv2.imread(img_path)
        if color_img is None:
            print(f"无法读取图片: {img_path}, 使用内置测试图像")
            color_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(color_img, f"OCR Test {version} GPU{gpu_id}", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

    print(f"图片形状: {color_img.shape}")
    print(f"图片类型: {color_img.dtype}")

    # 测试获取指定实例
    try:
        # 1. 测试获取实例是否成功
        instance_available = rec.get_instance(gpu_id, version)
        print(f"实例可用性: {instance_available}")

        # 2. 使用带参数的 OCR 函数
        result = rec.rapid_ocr_with_params(color_img, gpu_id, version)
        print(f"OCR 结果 (使用 {version} 在 GPU {gpu_id}): {result}")

        # 从结果中提取 boxes/txts/scores
        boxes, txts, scores = [], [], []
        for item in (result or []):
            try:
                box, text, score = item
            except Exception:
                box, text, score = item[0], item[1], (item[2] if len(item) > 2 else 1.0)
            box_np = np.array(box, dtype=np.int32).reshape(-1, 2)
            if box_np.shape[0] == 4:
                boxes.append(box_np)
                txts.append(text if text is not None else "")
                scores.append(float(score) if isinstance(score, (int, float, np.floating)) else 1.0)

        # 可视化结果
        if boxes:
            font_path = '/workspace/hngpt/models/simfang.ttf'
            vis = draw_ocr_box_txt_infer(color_img, boxes, txts, scores, font_path, drop_score=0.5)
            out_path = str(_dir / f'result_{version}_gpu{gpu_id}.png')
            cv2.imwrite(out_path, vis[:, :, ::-1])
            print(f"已保存可视化结果到: {out_path}")

        return True

    except Exception as e:
        print(f"OCR 调用失败: {e}")
        print("这可能是因为模型文件不存在，但 Python 绑定工作正常")
        return False


if __name__ == "__main__":
    # 测试默认实例
    print("=== 测试默认 OCR 实例 ===")
    try:
        # 获取当前默认配置
        default_version = rec.get_version()
        default_gpu_id = rec.get_gpu_id()
        print(f"默认配置: version={default_version}, gpu_id={default_gpu_id}")

        # 使用默认实例进行OCR
        img_path = '/workspace/hngpt/ocr/images/receipt.jpg'
        if not os.path.exists(img_path):
            color_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(color_img, "Default OCR Test", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        else:
            color_img = cv2.imread(img_path)
            if color_img is None:
                color_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
                cv2.putText(color_img, "Default OCR Test", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

        result = rec.rapid_ocr(color_img)
        print(f"默认 OCR 结果: {result}")

    except Exception as e:
        print(f"默认 OCR 测试失败: {e}")

    # 测试带参数的OCR实例
    test_cases = [
        ("v5", 0),
        ("v5", 1),
        ("v4", 0),
        ("v4", 1),
    ]

    success_count = 0
    for version, gpu_id in test_cases:
        if test_ocr_with_params(version, gpu_id):
            success_count += 1

    print(f"\n=== 测试总结 ===")
    print(f"成功测试: {success_count}/{len(test_cases)}")

    # 优雅释放资源
    try:
        if hasattr(rec, 'shutdown'):
            rec.shutdown()
    except Exception:
        pass