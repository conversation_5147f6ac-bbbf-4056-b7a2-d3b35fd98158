#!/usr/bin/env python3
"""
简化的OCR测试脚本
"""

import requests
import base64
import json
import os

# 测试配置
BASE_URL = "http://localhost:8888"
TEST_IMAGE_PATH = "/workspace/hngpt/tests/test.png"

def test_ocr_base64_simple():
    """简单测试OCR Base64模式"""
    print("🔍 测试OCR Base64模式")
    
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        return False
    
    try:
        # 读取图片并转换为base64
        with open(TEST_IMAGE_PATH, 'rb') as f:
            image_data = f.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
        
        print(f"📷 图片大小: {len(image_data)} bytes")
        print(f"📝 Base64长度: {len(base64_data)} chars")
        
        # 准备JSON请求
        payload = {
            'image': base64_data
        }
        
        print("📤 发送请求...")
        response = requests.post(
            f"{BASE_URL}/ocr/base64/?wait=true&timeout=30",
            json=payload,
            headers={'Content-Type': 'application/json', "Authorization": f"Bearer startfrom2023"},
            timeout=35
        )
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ OCR成功")
            print(f"📝 识别结果: {result}")
            return True
        else:
            print(f"❌ OCR失败")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    test_ocr_base64_simple()
