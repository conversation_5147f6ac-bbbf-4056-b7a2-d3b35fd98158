#!/bin/bash
"""
启动Ollama服务器脚本
确保所有GPU上的Ollama服务器都正常运行
"""

# 检查GPU数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
echo "检测到 $GPU_COUNT 个GPU"

# 启动函数
start_ollama_server() {
    local gpu_id=$1
    local port=$((11434 - gpu_id))
    local server_name="llm$gpu_id"
    
    echo "启动 $server_name (GPU $gpu_id, 端口 $port)..."
    
    # 检查端口是否已被占用
    if netstat -tlnp | grep -q ":$port "; then
        echo "  ✅ 端口 $port 已在使用，服务器可能已运行"
        return 0
    fi
    
    # 启动Ollama服务器
    CUDA_VISIBLE_DEVICES="$gpu_id" \
    OLLAMA_KEEP_ALIVE=-1 \
    OLLAMA_HOST="127.0.0.1:$port" \
    nohup /usr/bin/ollama serve > "/var/log/ollama_gpu${gpu_id}.log" 2>&1 &
    
    local pid=$!
    echo "  🚀 启动 $server_name，PID: $pid"
    
    # 等待服务器启动
    local max_wait=30
    local wait_time=0
    while [ $wait_time -lt $max_wait ]; do
        if curl -s "http://localhost:$port/api/tags" > /dev/null 2>&1; then
            echo "  ✅ $server_name 启动成功"
            return 0
        fi
        sleep 1
        wait_time=$((wait_time + 1))
    done
    
    echo "  ❌ $server_name 启动超时"
    return 1
}

# 主函数
main() {
    echo "🚀 启动Ollama服务器集群..."
    
    # 为每个GPU启动Ollama服务器
    for ((i=0; i<GPU_COUNT; i++)); do
        start_ollama_server $i
    done
    
    echo ""
    echo "📊 服务器状态检查:"
    for ((i=0; i<GPU_COUNT; i++)); do
        local port=$((11434 - i))
        if curl -s "http://localhost:$port/api/tags" > /dev/null 2>&1; then
            echo "  ✅ GPU $i (端口 $port): 在线"
        else
            echo "  ❌ GPU $i (端口 $port): 离线"
        fi
    done
    
    echo ""
    echo "🎉 Ollama服务器集群启动完成！"
}

# 执行主函数
main
