<svg width="400" height="120" viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 深蓝渐变 -->
    <linearGradient id="darkBlueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字阴影 -->
    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#1d4ed8" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="400" height="120" fill="white"/>
  
  <!-- Combo文字 -->
  <text x="200" y="54" font-family="Arial, sans-serif" font-size="36" font-weight="bold" 
        text-anchor="middle" fill="url(#blueGradient)" filter="url(#textShadow)">
    Combo<tspan fill="url(#darkBlueGradient)">OCR</tspan>
  </text>
  
  <!-- 副标题 -->
  <text x="200" y="78" font-family="Arial, sans-serif" font-size="14" 
        text-anchor="middle" fill="#64748b" opacity="0.8">
    Intelligent Text Recognition
  </text>
  
  <!-- 装饰下划线 -->
  <line x1="120" y1="90" x2="280" y2="90" stroke="url(#blueGradient)" stroke-width="2" opacity="0.3"/>
</svg>