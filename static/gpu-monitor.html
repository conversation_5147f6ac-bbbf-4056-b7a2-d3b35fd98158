<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU实时监控 - HNGPT AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2rem;
            font-weight: 300;
        }

        .header .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            color: #4a5568;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .gpu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .gpu-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gpu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .gpu-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
        }

        .gpu-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-healthy { background: #c6f6d5; color: #22543d; }
        .status-busy { background: #fed7d7; color: #742a2a; }
        .status-warning { background: #feebc8; color: #7b341e; }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #3182ce);
            transition: width 0.3s ease;
        }

        .progress-fill.high { background: linear-gradient(90deg, #f56565, #e53e3e); }
        .progress-fill.medium { background: linear-gradient(90deg, #ed8936, #dd6b20); }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }

        .metric-label {
            color: #4a5568;
        }

        .metric-value {
            font-weight: 600;
            color: #2d3748;
        }

        .task-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .task-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-chat { background: #bee3f8; color: #2a69ac; }
        .badge-embedding { background: #c6f6d5; color: #22543d; }
        .badge-ocr { background: #fbb6ce; color: #97266d; }
        .badge-layout { background: #d6f5d6; color: #22543d; }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #3182ce;
            transform: scale(1.1);
        }

        .refresh-btn.spinning {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .queue-info {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .queue-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            text-align: center;
        }

        .queue-stat {
            padding: 1rem;
            border-radius: 10px;
            background: #f7fafc;
        }

        .queue-stat .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
        }

        .queue-stat .label {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 0.5rem;
        }

        .last-update {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        .error-message {
            background: #fed7d7;
            color: #742a2a;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .gpu-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 GPU实时监控仪表板</h1>
        <div class="subtitle">HNGPT AI - 4张T4 GPU卡实时状态监控</div>
    </div>

    <div class="container">
        <!-- 队列统计 -->
        <div class="queue-info">
            <h3>📊 任务队列统计</h3>
            <div class="queue-stats" id="queueStats">
                <div class="queue-stat">
                    <div class="number" id="totalTasks">-</div>
                    <div class="label">总任务数</div>
                </div>
                <div class="queue-stat">
                    <div class="number" id="queueSize">-</div>
                    <div class="label">队列长度</div>
                </div>
                <div class="queue-stat">
                    <div class="number" id="processingTasks">-</div>
                    <div class="label">处理中</div>
                </div>
                <div class="queue-stat">
                    <div class="number" id="completedTasks">-</div>
                    <div class="label">已完成</div>
                </div>
            </div>
        </div>

        <!-- GPU卡片 -->
        <div class="gpu-grid" id="gpuGrid">
            <!-- GPU卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 系统统计 -->
        <!-- 移除系统负载、性能指标、Ollama服务器模块，只显示GPU卡片 -->

        <div class="last-update">
            最后更新: <span id="lastUpdate">-</span>
        </div>
    </div>

    <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">🔄</button>

    <script>
        let refreshInterval;
        let isRefreshing = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            startAutoRefresh();
        });

        // 自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 3000); // 每3秒刷新
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // 刷新数据
        async function refreshData() {
            if (isRefreshing) return;
            
            isRefreshing = true;
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.classList.add('spinning');

            try {
                // 获取系统状态 - 使用无认证的监控API
                const response = await fetch('/monitor/api/status');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                updateUI(data);

                // 获取队列统计 - 使用无认证的监控API
                try {
                    const queueResponse = await fetch('/monitor/api/queue');
                    if (queueResponse.ok) {
                        const queueData = await queueResponse.json();
                        updateQueueStats(queueData);
                    }
                } catch (e) {
                    console.warn('队列统计获取失败:', e);
                }
                
            } catch (error) {
                console.error('数据获取失败:', error);
                showError('数据获取失败: ' + error.message);
            } finally {
                isRefreshing = false;
                refreshBtn.classList.remove('spinning');
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
            }
        }

        // 更新UI
        function updateUI(data) {
            updateGPUCards(data.gpu_info || []);
            // 移除了updateSystemStats调用
            clearError();
        }

        // 更新GPU卡片
        function updateGPUCards(gpuInfo) {
            const gpuGrid = document.getElementById('gpuGrid');
            gpuGrid.innerHTML = '';

            gpuInfo.forEach((gpu, index) => {
                const card = createGPUCard(gpu, index);
                gpuGrid.appendChild(card);
            });
        }

        // 创建GPU卡片
        function createGPUCard(gpu, index) {
            const card = document.createElement('div');
            card.className = 'gpu-card';
            
            // 使用nvidia-smi提供的更准确数据
            const memoryUsage = gpu.memory_total_mb > 0 ?
                (gpu.memory_used_mb / gpu.memory_total_mb * 100) : 0;
            const gpuUtilization = gpu.utilization_percent || 0;

            const status = getGPUStatus(memoryUsage, gpuUtilization, gpu);
            const statusClass = getStatusClass(status);

            card.innerHTML = `
                <div class="gpu-header">
                    <div class="gpu-title">🎮 GPU ${index} - ${gpu.name || 'Unknown'}</div>
                    <div class="gpu-status ${statusClass}">${status}</div>
                </div>

                <div class="metric-row">
                    <span class="metric-label">GPU使用率</span>
                    <span class="metric-value">${gpuUtilization.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${getProgressClass(gpuUtilization)}"
                         style="width: ${gpuUtilization}%"></div>
                </div>

                <div class="metric-row">
                    <span class="metric-label">显存使用率</span>
                    <span class="metric-value">${memoryUsage.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${getProgressClass(memoryUsage)}"
                         style="width: ${memoryUsage}%"></div>
                </div>

                <div class="metric-row">
                    <span class="metric-label">已分配显存</span>
                    <span class="metric-value">${(gpu.memory_used_mb / 1024).toFixed(1)} GB</span>
                </div>

                <div class="metric-row">
                    <span class="metric-label">总显存</span>
                    <span class="metric-value">${(gpu.memory_total_mb / 1024).toFixed(1)} GB</span>
                </div>

                ${gpu.temperature_c > 0 ? `
                <div class="metric-row">
                    <span class="metric-label">温度</span>
                    <span class="metric-value">${gpu.temperature_c.toFixed(0)}°C</span>
                </div>
                ` : ''}

                ${gpu.power_draw_w > 0 ? `
                <div class="metric-row">
                    <span class="metric-label">功耗</span>
                    <span class="metric-value">${gpu.power_draw_w.toFixed(0)}W</span>
                </div>
                ` : ''}

                <div class="task-badges">
                    ${gpu.llm_tasks ? `<span class="task-badge badge-chat">Chat: ${gpu.llm_tasks}</span>` : ''}
                    ${gpu.ocr_tasks ? `<span class="task-badge badge-ocr">OCR: ${gpu.ocr_tasks}</span>` : ''}
                    ${gpu.layout_tasks ? `<span class="task-badge badge-layout">Layout: ${gpu.layout_tasks}</span>` : ''}
                    ${(!gpu.llm_tasks && !gpu.ocr_tasks && !gpu.layout_tasks) ?
                        '<span class="task-badge badge-embedding">空闲</span>' : ''}
                </div>
            `;
            
            return card;
        }

        // 获取GPU状态
        function getGPUStatus(memoryUsage, gpuUtilization, gpu) {
            if (gpu.error) return '错误';
            if (gpuUtilization > 80 || memoryUsage > 85) return '高负载';
            if (gpuUtilization > 50 || memoryUsage > 50) return '中负载';
            if (gpuUtilization > 10 || gpu.llm_tasks || gpu.ocr_tasks || gpu.layout_tasks) return '工作中';
            return '空闲';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '空闲': return 'status-healthy';
                case '工作中': case '中负载': return 'status-warning';
                case '高负载': case '错误': return 'status-busy';
                default: return 'status-healthy';
            }
        }

        // 获取进度条样式类
        function getProgressClass(usage) {
            if (usage > 85) return 'high';
            if (usage > 50) return 'medium';
            return '';
        }

        // 更新队列统计
        function updateQueueStats(queueData) {
            document.getElementById('totalTasks').textContent = queueData.total_tasks || 0;
            document.getElementById('queueSize').textContent = queueData.queue_size || 0;
            
            // 计算处理中的任务
            let processingTasks = 0;
            if (queueData.gpu_status) {
                Object.values(queueData.gpu_status).forEach(gpu => {
                    processingTasks += (gpu.llm_tasks || 0) + (gpu.ocr_tasks || 0) + (gpu.layout_tasks || 0);
                });
            }
            document.getElementById('processingTasks').textContent = processingTasks;
            
            // 已完成任务数 = 总任务数 - 队列长度 - 处理中任务
            const completedTasks = Math.max(0, (queueData.total_tasks || 0) - (queueData.queue_size || 0) - processingTasks);
            document.getElementById('completedTasks').textContent = completedTasks;
        }

        // 移除了updateSystemStats函数，不再需要系统负载、性能指标、Ollama服务器模块

        // 显示错误
        function showError(message) {
            const container = document.querySelector('.container');
            let errorDiv = document.querySelector('.error-message');
            
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                container.insertBefore(errorDiv, container.firstChild);
            }
            
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 清除错误
        function clearError() {
            const errorDiv = document.querySelector('.error-message');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }

        // 页面可见性变化时控制刷新
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
                refreshData();
            }
        });
    </script>
</body>
</html>
