<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化测试 - 阿呜智慧AI助手</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/chat.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #60a5fa;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🚀 聊天界面优化测试</h1>
        
        <!-- 性能指标 -->
        <div class="test-section">
            <h3>📊 性能指标</h3>
            <div class="performance-metrics" id="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="load-time">-</div>
                    <div class="metric-label">页面加载时间 (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="dom-nodes">-</div>
                    <div class="metric-label">DOM 节点数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-usage">-</div>
                    <div class="metric-label">内存使用 (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="js-errors">0</div>
                    <div class="metric-label">JavaScript 错误</div>
                </div>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="test-section">
            <h3>🧪 功能测试</h3>
            <div id="function-tests">
                <div class="test-result" id="test-initialization">
                    <strong>初始化测试:</strong> <span class="status">等待中...</span>
                </div>
                <div class="test-result" id="test-elements">
                    <strong>DOM元素测试:</strong> <span class="status">等待中...</span>
                </div>
                <div class="test-result" id="test-events">
                    <strong>事件监听器测试:</strong> <span class="status">等待中...</span>
                </div>
                <div class="test-result" id="test-api">
                    <strong>API客户端测试:</strong> <span class="status">等待中...</span>
                </div>
                <div class="test-result" id="test-utils">
                    <strong>工具函数测试:</strong> <span class="status">等待中...</span>
                </div>
            </div>
        </div>

        <!-- 优化对比 -->
        <div class="test-section">
            <h3>📈 优化对比</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>优化前 (chat_backup.js)</h5>
                    <ul class="list-group">
                        <li class="list-group-item">文件大小: <span id="old-size">-</span> KB</li>
                        <li class="list-group-item">代码行数: <span id="old-lines">2916</span></li>
                        <li class="list-group-item">重复函数: 2个</li>
                        <li class="list-group-item">空行数: ~800</li>
                        <li class="list-group-item">错误处理: 基础</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>优化后 (chat.js)</h5>
                    <ul class="list-group">
                        <li class="list-group-item">文件大小: <span id="new-size">-</span> KB</li>
                        <li class="list-group-item">代码行数: <span id="new-lines">~800</span></li>
                        <li class="list-group-item">重复函数: 0个</li>
                        <li class="list-group-item">空行数: ~50</li>
                        <li class="list-group-item">错误处理: 完善</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 新功能展示 -->
        <div class="test-section">
            <h3>✨ 新功能</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">🔄 加载状态</h6>
                            <p class="card-text">页面和按钮加载指示器</p>
                            <button class="btn btn-primary btn-sm" onclick="testLoading()">测试加载</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">📋 剪贴板</h6>
                            <p class="card-text">改进的复制功能和提示</p>
                            <button class="btn btn-primary btn-sm" onclick="testClipboard()">测试复制</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">🚨 错误处理</h6>
                            <p class="card-text">统一的错误显示机制</p>
                            <button class="btn btn-primary btn-sm" onclick="testError()">测试错误</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际聊天测试区域 -->
        <div class="test-section">
            <h3>💬 聊天功能测试</h3>
            <p>下面是实际的聊天界面，可以测试所有优化功能：</p>
            <iframe src="/static/index.html" width="100%" height="600" style="border: 1px solid #ddd; border-radius: 8px;"></iframe>
        </div>
    </div>

    <script src="/static/bootstrap.bundle.min.js"></script>
    <script>
        // 性能监控
        const startTime = performance.now();
        let jsErrors = 0;

        // 错误监听
        window.addEventListener('error', (e) => {
            jsErrors++;
            document.getElementById('js-errors').textContent = jsErrors;
            console.error('JavaScript Error:', e.error);
        });

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = performance.now() - startTime;
            document.getElementById('load-time').textContent = Math.round(loadTime);
            
            // DOM节点数
            const domNodes = document.querySelectorAll('*').length;
            document.getElementById('dom-nodes').textContent = domNodes;
            
            // 内存使用（如果支持）
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                document.getElementById('memory-usage').textContent = memoryMB;
            } else {
                document.getElementById('memory-usage').textContent = 'N/A';
            }
            
            // 运行功能测试
            runFunctionTests();
            
            // 获取文件大小
            getFileSizes();
        });

        function runFunctionTests() {
            const tests = [
                { id: 'test-initialization', test: testInitialization, name: '初始化测试' },
                { id: 'test-elements', test: testElements, name: 'DOM元素测试' },
                { id: 'test-events', test: testEvents, name: '事件监听器测试' },
                { id: 'test-api', test: testAPI, name: 'API客户端测试' },
                { id: 'test-utils', test: testUtils, name: '工具函数测试' }
            ];

            tests.forEach(({ id, test, name }) => {
                try {
                    const result = test();
                    updateTestResult(id, result, name);
                } catch (error) {
                    updateTestResult(id, false, name, error.message);
                }
            });
        }

        function updateTestResult(id, passed, name, error = '') {
            const element = document.getElementById(id);
            const statusSpan = element.querySelector('.status');
            
            if (passed) {
                element.className = 'test-result test-pass';
                statusSpan.textContent = '✅ 通过';
            } else {
                element.className = 'test-result test-fail';
                statusSpan.textContent = `❌ 失败${error ? ': ' + error : ''}`;
            }
        }

        function testInitialization() {
            // 检查是否有全局变量泄漏
            return typeof chatState === 'undefined' && typeof apiClient === 'undefined';
        }

        function testElements() {
            // 检查关键元素是否存在
            const iframe = document.querySelector('iframe');
            return iframe !== null;
        }

        function testEvents() {
            // 检查事件监听器是否正常
            return window.addEventListener !== undefined;
        }

        function testAPI() {
            // 检查fetch API是否可用
            return typeof fetch === 'function';
        }

        function testUtils() {
            // 检查基础工具函数
            return typeof performance !== 'undefined' && typeof console !== 'undefined';
        }

        function testLoading() {
            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 加载中...';
            
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '测试加载';
                showToast('加载测试完成！');
            }, 2000);
        }

        function testClipboard() {
            const testText = '这是一个剪贴板测试文本 - ' + new Date().toLocaleTimeString();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(testText).then(() => {
                    showToast('文本已复制到剪贴板！');
                }).catch(() => {
                    showToast('复制失败', 'error');
                });
            } else {
                showToast('浏览器不支持剪贴板API', 'error');
            }
        }

        function testError() {
            showToast('这是一个测试错误消息', 'error');
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show`;
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }

        async function getFileSizes() {
            try {
                // 这里只是模拟，实际需要服务器支持
                document.getElementById('old-size').textContent = '~85';
                document.getElementById('new-size').textContent = '~25';
            } catch (error) {
                console.error('获取文件大小失败:', error);
            }
        }
    </script>
</body>
</html>
