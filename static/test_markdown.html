<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown测试</title>
    <link rel="stylesheet" href="/static/chat_new.css">
    <link rel="stylesheet" href="/static/prism.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
        }
        .test-input {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            font-family: var(--font-mono);
        }
        .test-output {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 20px;
            background: var(--bg-secondary);
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Markdown渲染测试</h1>
        
        <div class="test-section">
            <h3>测试输入</h3>
            <textarea class="test-input" id="markdown-input" placeholder="在这里输入Markdown文本...">
# 标题测试

这是一个段落，包含 **粗体** 和 *斜体* 文本。

## 代码测试

这是内联代码：`console.log('Hello World')`

这是代码块：

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return `Welcome, ${name}`;
}

const user = "阿呜";
const message = greet(user);
console.log(message);
```

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 计算前10个斐波那契数
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
```

## 列表测试

- 列表项 1
- 列表项 2 包含 `代码`
- 列表项 3

1. 有序列表 1
2. 有序列表 2
3. 有序列表 3

## 引用测试

> 这是一个引用块
> 可以包含多行内容

## 表格测试

| 语言 | 类型 | 特点 |
|------|------|------|
| JavaScript | 动态 | 灵活 |
| Python | 动态 | 简洁 |
| Rust | 静态 | 安全 |
            </textarea>
            
            <button onclick="renderMarkdown()" style="padding: 10px 20px; margin: 10px 0; background: var(--primary-color); color: white; border: none; border-radius: var(--radius-sm); cursor: pointer;">
                渲染Markdown
            </button>
        </div>
        
        <div class="test-section">
            <h3>渲染结果</h3>
            <div class="test-output message-content" id="markdown-output">
                <p>点击上方按钮渲染Markdown...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>调试信息</h3>
            <div id="debug-info" style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                <div>marked 可用: <span id="marked-status">检查中...</span></div>
                <div>Prism 可用: <span id="prism-status">检查中...</span></div>
                <div>支持的语言: <span id="prism-languages">检查中...</span></div>
            </div>
        </div>
    </div>

    <script src="/static/marked.min.js"></script>
    <script src="/static/prism.min.js"></script>
    <script src="/static/prism-javascript.min.js"></script>
    <script>
        // 检查依赖
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('marked-status').textContent = typeof marked !== 'undefined' ? '✅ 是' : '❌ 否';
            document.getElementById('prism-status').textContent = typeof Prism !== 'undefined' ? '✅ 是' : '❌ 否';
            
            if (typeof Prism !== 'undefined') {
                const languages = Object.keys(Prism.languages).filter(lang => lang !== 'extend' && lang !== 'insertBefore' && lang !== 'DFS');
                document.getElementById('prism-languages').textContent = languages.join(', ');
            } else {
                document.getElementById('prism-languages').textContent = '无';
            }
            
            // 自动渲染一次
            renderMarkdown();
        });
        
        function renderMarkdown() {
            const input = document.getElementById('markdown-input').value;
            const output = document.getElementById('markdown-output');
            
            try {
                let html = input;
                
                if (typeof marked !== 'undefined') {
                    // 配置marked
                    marked.setOptions({
                        breaks: true,
                        gfm: true,
                        sanitize: false
                    });
                    
                    // 自定义渲染器
                    const renderer = new marked.Renderer();
                    
                    renderer.code = function(code, language) {
                        const validLang = language && Prism.languages[language] ? language : 'javascript';
                        const escapedCode = code
                            .replace(/&/g, '&amp;')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;')
                            .replace(/"/g, '&quot;')
                            .replace(/'/g, '&#39;');
                        return `<pre class="language-${validLang}" data-language="${validLang}"><code class="language-${validLang}">${escapedCode}</code></pre>`;
                    };
                    
                    marked.use({ renderer });
                    html = marked.parse(input);
                } else {
                    html = '<p style="color: red;">marked.js 未加载</p>';
                }
                
                output.innerHTML = html;
                
                // 代码高亮
                setTimeout(() => {
                    if (typeof Prism !== 'undefined') {
                        try {
                            Prism.highlightAllUnder(output);
                            console.log('代码高亮完成');
                        } catch (error) {
                            console.error('代码高亮失败:', error);
                        }
                    }
                    
                    // 添加语言标签
                    const codeBlocks = output.querySelectorAll('pre[class*="language-"]');
                    codeBlocks.forEach(pre => {
                        const className = pre.className;
                        const match = className.match(/language-(\w+)/);
                        if (match) {
                            const language = match[1];
                            pre.setAttribute('data-language', language);
                        }
                    });
                }, 10);
                
            } catch (error) {
                console.error('渲染失败:', error);
                output.innerHTML = `<p style="color: red;">渲染失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
