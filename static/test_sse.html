<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE流式响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-debug { color: #6c757d; }
        .response-content {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 SSE流式响应测试</h1>
        
        <div class="test-section">
            <h3>1. 使用Fetch API测试流式响应</h3>
            <button id="test-fetch" onclick="testFetchStream()">测试Fetch流式</button>
            <button id="clear-fetch" onclick="clearLog('fetch-log')">清空日志</button>
            <div id="fetch-log" class="log"></div>
            <div id="fetch-content" class="response-content" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 使用EventSource测试SSE连接</h3>
            <p><strong>注意：</strong>EventSource只支持GET请求，这里仅用于测试SSE连接是否正常</p>
            <button id="test-eventsource" onclick="testEventSource()">测试EventSource</button>
            <button id="clear-eventsource" onclick="clearLog('eventsource-log')">清空日志</button>
            <div id="eventsource-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试非流式响应（对比）</h3>
            <button id="test-normal" onclick="testNormalResponse()">测试普通响应</button>
            <button id="clear-normal" onclick="clearLog('normal-log')">清空日志</button>
            <div id="normal-log" class="log"></div>
        </div>
    </div>

    <script>
        function log(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLog(containerId) {
            document.getElementById(containerId).innerHTML = '';
            const contentId = containerId.replace('-log', '-content');
            const contentEl = document.getElementById(contentId);
            if (contentEl) {
                contentEl.style.display = 'none';
                contentEl.innerHTML = '';
            }
        }

        async function testFetchStream() {
            const button = document.getElementById('test-fetch');
            button.disabled = true;
            
            log('fetch-log', '开始测试Fetch流式响应...', 'info');
            
            try {
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer startfrom2023',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        model: 'hngpt',
                        messages: [{ role: 'user', content: '请说一句简短的问候' }],
                        stream: true
                    })
                });

                log('fetch-log', `响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                log('fetch-log', `Content-Type: ${response.headers.get('content-type')}`, 'debug');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let contentDiv = document.getElementById('fetch-content');
                contentDiv.style.display = 'block';
                contentDiv.innerHTML = '';
                let fullContent = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        log('fetch-log', '流式响应结束', 'success');
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    const events = buffer.split('\n\n');
                    buffer = events.pop();

                    for (const event of events) {
                        if (event.trim()) {
                            log('fetch-log', `接收事件: ${event.trim()}`, 'debug');
                            
                            const lines = event.split('\n');
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const jsonStr = line.slice(6).trim();
                                    if (jsonStr === '[DONE]') {
                                        log('fetch-log', '收到结束标记', 'success');
                                        return;
                                    }
                                    
                                    try {
                                        const data = JSON.parse(jsonStr);
                                        if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                                            const content = data.choices[0].delta.content;
                                            fullContent += content;
                                            contentDiv.innerHTML = fullContent;
                                            log('fetch-log', `收到内容: "${content}"`, 'success');
                                        }
                                    } catch (e) {
                                        log('fetch-log', `JSON解析错误: ${e.message}`, 'error');
                                    }
                                }
                            }
                        }
                    }
                }

            } catch (error) {
                log('fetch-log', `错误: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
            }
        }

        async function testEventSource() {
            log('eventsource-log', 'EventSource不支持POST请求，无法直接测试Chat接口', 'info');
            log('eventsource-log', '可以考虑添加一个GET的SSE测试端点', 'debug');
        }

        async function testNormalResponse() {
            const button = document.getElementById('test-normal');
            button.disabled = true;
            
            log('normal-log', '开始测试普通响应...', 'info');
            
            try {
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer startfrom2023'
                    },
                    body: JSON.stringify({
                        model: 'hngpt',
                        messages: [{ role: 'user', content: 'Hello' }],
                        stream: false
                    })
                });

                log('normal-log', `响应状态: ${response.status}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    log('normal-log', `响应内容: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    log('normal-log', `错误响应: ${error}`, 'error');
                }

            } catch (error) {
                log('normal-log', `错误: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
            }
        }
    </script>
</body>
</html>
