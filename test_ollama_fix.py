#!/usr/bin/env python3
"""
简单测试Ollama服务器连接状态
"""

import requests
import json
import time

def test_ollama_server(port, server_name):
    """测试单个Ollama服务器"""
    url = f"http://localhost:{port}"
    
    print(f"\n🔍 测试 {server_name} ({url})")
    
    try:
        # 测试服务器状态
        response = requests.get(f"{url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"  ✅ 服务器在线，模型数量: {len(models)}")
            return True
        else:
            print(f"  ❌ 服务器响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"  ❌ 连接失败: 服务器未运行")
        return False
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_chat_api(port, server_name):
    """测试Chat API"""
    url = f"http://localhost:{port}"
    
    print(f"\n💬 测试 {server_name} Chat API")
    
    payload = {
        "model": "hngpt-mini:latest",
        "messages": [{"role": "user", "content": "Hello, test"}],
        "stream": False
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{url}/api/chat", json=payload, timeout=30)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"  ✅ Chat API 成功 - 耗时: {response_time:.3f}s")
            return True
        else:
            print(f"  ❌ Chat API 失败: {response.status_code}")
            print(f"  响应: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"  ❌ Chat API 异常: {e}")
        return False

def test_embedding_api(port, server_name):
    """测试Embedding API"""
    url = f"http://localhost:{port}"
    
    print(f"\n🔗 测试 {server_name} Embedding API")
    
    payload = {
        "model": "hngpt-embedding",
        "input": "Test embedding"
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{url}/api/embed", json=payload, timeout=30)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"  ✅ Embedding API 成功 - 耗时: {response_time:.3f}s")
            return True
        else:
            print(f"  ❌ Embedding API 失败: {response.status_code}")
            print(f"  响应: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"  ❌ Embedding API 异常: {e}")
        return False

def main():
    print("🚀 Ollama服务器连接测试")
    print("=" * 50)
    
    servers = [
        (11434, "server0 (GPU 0)"),
        (11433, "server1 (GPU 1)")
    ]
    
    results = {}
    
    for port, name in servers:
        # 测试基本连接
        basic_ok = test_ollama_server(port, name)
        results[name] = {"basic": basic_ok}
        
        if basic_ok:
            # 测试Chat API
            chat_ok = test_chat_api(port, name)
            results[name]["chat"] = chat_ok
            
            # 测试Embedding API
            embed_ok = test_embedding_api(port, name)
            results[name]["embedding"] = embed_ok
        else:
            results[name]["chat"] = False
            results[name]["embedding"] = False
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    for name, result in results.items():
        status = "✅" if all(result.values()) else "❌"
        print(f"  {status} {name}")
        print(f"    基本连接: {'✅' if result['basic'] else '❌'}")
        print(f"    Chat API: {'✅' if result['chat'] else '❌'}")
        print(f"    Embedding API: {'✅' if result['embedding'] else '❌'}")
    
    # 检查是否所有服务器都正常
    all_ok = all(all(result.values()) for result in results.values())
    if all_ok:
        print("\n🎉 所有Ollama服务器工作正常！")
    else:
        print("\n⚠️ 部分Ollama服务器存在问题，请检查日志")

if __name__ == "__main__":
    main()
